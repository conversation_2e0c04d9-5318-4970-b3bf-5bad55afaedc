"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* harmony import */ var _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/Person */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [enabledTabs, setEnabledTabs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        true,\n        false,\n        false\n    ]); // Solo la primera pestaña habilitada inicialmente\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Funciones de validación para cada pestaña\n    const validateTab1 = ()=>{\n        const { propietarioNombre, propietarioDocumento, propietarioTelefono, propietarioEmail } = formData;\n        return !!(propietarioNombre.trim() && propietarioDocumento.trim() && propietarioTelefono.trim() && propietarioEmail.trim());\n    };\n    const validateTab2 = ()=>{\n        const { empresaRazonSocial, empresaTipoCliente, empresaDomicilio, empresaLocalidad, empresaProvincia, empresaCondFrenteIva } = formData;\n        return !!(empresaRazonSocial.trim() && empresaTipoCliente.trim() && empresaDomicilio.trim() && empresaLocalidad.trim() && empresaProvincia.trim() && empresaCondFrenteIva.trim());\n    };\n    const validateTab3 = ()=>{\n        return formData.contactos.length > 0 && formData.contactos[0].nombre.trim() !== \"\" && formData.contactos[0].cargo.trim() !== \"\" && formData.contactos[0].telefono.trim() !== \"\" && formData.contactos[0].email.trim() !== \"\";\n    };\n    // Función para habilitar la siguiente pestaña\n    const enableNextTab = (currentTab)=>{\n        const newEnabledTabs = [\n            ...enabledTabs\n        ];\n        if (currentTab + 1 < newEnabledTabs.length) {\n            newEnabledTabs[currentTab + 1] = true;\n            setEnabledTabs(newEnabledTabs);\n        }\n    };\n    // Función para ir a la siguiente pestaña\n    const handleNextTab = ()=>{\n        let canProceed = false;\n        switch(tabValue){\n            case 0:\n                canProceed = validateTab1();\n                break;\n            case 1:\n                canProceed = validateTab2();\n                break;\n            case 2:\n                canProceed = validateTab3();\n                break;\n        }\n        if (canProceed) {\n            enableNextTab(tabValue);\n            setTabValue(tabValue + 1);\n        } else {\n            alert(\"Por favor complete todos los campos requeridos antes de continuar.\");\n        }\n    };\n    // Función para ir a la pestaña anterior\n    const handlePreviousTab = ()=>{\n        if (tabValue > 0) {\n            setTabValue(tabValue - 1);\n        }\n    };\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData((prevData)=>({\n                    ...prevData,\n                    empresaRazonSocial: selectedClient.razonSocial,\n                    empresaTipoCliente: selectedClient.tipoCliente || \"\",\n                    empresaDomicilio: selectedClient.direccion,\n                    empresaLocalidad: localidad,\n                    empresaProvincia: provincia,\n                    empresaCondFrenteIva: selectedClient.condFrenteIva,\n                    // Update contacts array with the selected client's contact information\n                    contactos: [\n                        {\n                            id: 1,\n                            nombre: selectedClient.nombreContacto || \"\",\n                            cargo: selectedClient.cargoContacto || \"\",\n                            telefono: selectedClient.telefono,\n                            email: selectedClient.mail\n                        }\n                    ]\n                }));\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Pestaña 1: Propietario/Persona Física\n        propietarioNombre: \"\",\n        propietarioDocumento: \"\",\n        propietarioTelefono: \"\",\n        propietarioEmail: \"\",\n        usarComoRazonSocial: false,\n        // Pestaña 2: Empresa/Razón Social\n        empresaRazonSocial: \"\",\n        empresaTipoCliente: \"\",\n        empresaDomicilio: \"\",\n        empresaLocalidad: \"\",\n        empresaProvincia: \"\",\n        empresaCondFrenteIva: \"\",\n        // Pestaña 3: Contactos/Encargados (array dinámico)\n        contactos: [\n            {\n                id: 1,\n                nombre: \"\",\n                cargo: \"\",\n                telefono: \"\",\n                email: \"\"\n            }\n        ]\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            // Pestaña 1: Propietario/Persona Física\n            propietarioNombre: \"\",\n            propietarioDocumento: \"\",\n            propietarioTelefono: \"\",\n            propietarioEmail: \"\",\n            usarComoRazonSocial: false,\n            // Pestaña 2: Empresa/Razón Social\n            empresaRazonSocial: \"\",\n            empresaTipoCliente: \"\",\n            empresaDomicilio: \"\",\n            empresaLocalidad: \"\",\n            empresaProvincia: \"\",\n            empresaCondFrenteIva: \"\",\n            // Pestaña 3: Contactos/Encargados\n            contactos: [\n                {\n                    id: 1,\n                    nombre: \"\",\n                    cargo: \"\",\n                    telefono: \"\",\n                    email: \"\"\n                }\n            ]\n        });\n        setError({});\n        setTabValue(0); // Resetear al primer tab\n        setEnabledTabs([\n            true,\n            false,\n            false\n        ]); // Resetear pestañas habilitadas\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Validaciones para campos de texto (nombres, razón social, etc.)\n        if (name === \"propietarioNombre\" || name === \"empresaRazonSocial\" || name === \"empresaLocalidad\" || name.startsWith(\"contacto\") && name.includes(\"nombre\")) {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para domicilio\n        if (name === \"empresaDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para teléfonos\n        if (name === \"propietarioTelefono\" || name.includes(\"telefono\")) {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"propietarioDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>{\n            const newState = {\n                ...prevState,\n                [name]: value\n            };\n            // Si está marcada la opción de usar como razón social, sincronizar datos\n            if (prevState.usarComoRazonSocial) {\n                if (name === \"propietarioNombre\") {\n                    newState.empresaRazonSocial = value;\n                    // También actualizar el primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            nombre: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioTelefono\") {\n                    // Actualizar el teléfono del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            telefono: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioEmail\") {\n                    // Actualizar el email del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            email: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                }\n            }\n            return newState;\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 553,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            // Datos del Propietario/Persona Física (Pestaña 1)\n            propietarioNombre: formData.propietarioNombre,\n            propietarioDocumento: formData.propietarioDocumento,\n            propietarioTelefono: formData.propietarioTelefono,\n            propietarioEmail: formData.propietarioEmail,\n            // Datos de la Empresa/Razón Social (Pestaña 2)\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            direccion: formData.empresaDomicilio,\n            empresaLocalidad: formData.empresaLocalidad,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            // Datos del Contacto/Encargado Principal (Pestaña 3)\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            // Campos calculados/derivados\n            lugar: lugar,\n            documento: formData.propietarioDocumento\n        };\n        // TODO: Add proper form validation if needed\n        // For now, individual field validations are handled in the onChange handlers\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData((prevData)=>({\n                        ...prevData,\n                        empresaRazonSocial: agricultor.razonSocial,\n                        empresaTipoCliente: agricultor.tipoCliente || \"\",\n                        empresaDomicilio: agricultor.direccion,\n                        empresaLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                        empresaProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                        empresaCondFrenteIva: agricultor.condFrenteIva,\n                        contactos: [\n                            {\n                                id: 1,\n                                nombre: agricultor.nombreContacto || \"\",\n                                cargo: agricultor.cargoContacto || \"\",\n                                telefono: agricultor.telefono,\n                                email: agricultor.mail\n                            }\n                        ]\n                    }));\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            // Datos del Propietario/Persona Física (Pestaña 1)\n            propietarioNombre: formData.propietarioNombre,\n            propietarioDocumento: formData.propietarioDocumento,\n            propietarioTelefono: formData.propietarioTelefono,\n            propietarioEmail: formData.propietarioEmail,\n            // Datos de la Empresa/Razón Social (Pestaña 2)\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            direccion: formData.empresaDomicilio,\n            empresaLocalidad: formData.empresaLocalidad,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            // Datos del Contacto/Encargado Principal (Pestaña 3)\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            // Campos calculados/derivados\n            lugar: lugar,\n            documento: formData.propietarioDocumento\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 959,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 992,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 981,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 951,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1009,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1007,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 999,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 1016,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 998,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1055,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1043,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            borderBottom: 1,\n                                            borderColor: \"divider\",\n                                            mb: 3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            value: tabValue,\n                                            onChange: (event, newValue)=>{\n                                                // Solo permitir cambiar a pestañas habilitadas\n                                                if (enabledTabs[newValue]) {\n                                                    setTabValue(newValue);\n                                                }\n                                            },\n                                            sx: {\n                                                \"& .MuiTab-root\": {\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    fontWeight: 600,\n                                                    textTransform: \"none\",\n                                                    fontSize: \"1rem\"\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDC64 Propietario/Persona F\\xedsica\",\n                                                    sx: {\n                                                        minWidth: 220\n                                                    },\n                                                    disabled: !enabledTabs[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1098,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83C\\uDFE2 Empresa/Raz\\xf3n Social\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDCDE Contacto/Encargado\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1108,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1080,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tabValue === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos del Propietario/Persona F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre Completo *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1132,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan Carlos P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioNombre\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioNombre),\n                                                                helperText: error.propietarioNombre,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioNombre,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioNombre && (error.propietarioNombre ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1151,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1153,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1148,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1135,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1131,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"DNI/CUIT *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 20-12345678-9\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioDocumento\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioDocumento),\n                                                                helperText: error.propietarioDocumento,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioDocumento,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioDocumento && (error.propietarioDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1182,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1184,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1179,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1166,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1162,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1194,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioTelefono\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioTelefono),\n                                                                helperText: error.propietarioTelefono,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioTelefono && (error.propietarioTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1212,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1214,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1209,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1197,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1224,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioEmail\",\n                                                                type: \"email\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioEmail),\n                                                                helperText: error.propietarioEmail,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioEmail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioEmail && (error.propietarioEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1242,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1244,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1239,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1227,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1223,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                mt: 2,\n                                                                p: 2,\n                                                                bgcolor: \"#f5f5f5\",\n                                                                borderRadius: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        cursor: \"pointer\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.usarComoRazonSocial,\n                                                                            onChange: (e)=>{\n                                                                                const checked = e.target.checked;\n                                                                                setFormData((prev)=>{\n                                                                                    var _prev_contactos_;\n                                                                                    return {\n                                                                                        ...prev,\n                                                                                        usarComoRazonSocial: checked,\n                                                                                        // Si se marca, copiar datos del propietario a la empresa\n                                                                                        empresaRazonSocial: checked ? prev.propietarioNombre : \"\",\n                                                                                        // También copiar el primer contacto con los datos del propietario\n                                                                                        contactos: checked ? [\n                                                                                            {\n                                                                                                ...prev.contactos[0],\n                                                                                                nombre: prev.propietarioNombre,\n                                                                                                telefono: prev.propietarioTelefono,\n                                                                                                email: prev.propietarioEmail,\n                                                                                                cargo: ((_prev_contactos_ = prev.contactos[0]) === null || _prev_contactos_ === void 0 ? void 0 : _prev_contactos_.cargo) || \"Propietario\"\n                                                                                            },\n                                                                                            ...prev.contactos.slice(1)\n                                                                                        ] : prev.contactos\n                                                                                    };\n                                                                                });\n                                                                            },\n                                                                            style: {\n                                                                                marginRight: \"8px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1269,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                fontFamily: \"Inter, sans-serif\"\n                                                                            },\n                                                                            children: \"✅ Usar estos datos del propietario para la empresa y contacto principal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1300,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        color: \"#2E7D32\",\n                                                                        mt: 1,\n                                                                        display: \"block\",\n                                                                        fontStyle: \"italic\"\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCA1 Los datos del propietario se sincronizar\\xe1n autom\\xe1ticamente con la raz\\xf3n social de la empresa y el contacto principal\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1309,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1254,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1253,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1129,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Cancelar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1328,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1118,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos de la Empresa/Raz\\xf3n Social\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1364,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Agropecuaria San Juan S.A.\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaRazonSocial\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaRazonSocial),\n                                                                helperText: error.empresaRazonSocial,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaRazonSocial,\n                                                                disabled: formData.usarComoRazonSocial,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaRazonSocial && (error.empresaRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1397,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1399,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1394,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        backgroundColor: formData.usarComoRazonSocial ? \"#f5f5f5\" : \"transparent\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1380,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                sx: {\n                                                                    color: \"#666\",\n                                                                    mt: 1,\n                                                                    display: \"block\"\n                                                                },\n                                                                children: \"\\uD83D\\uDCA1 Autocompletado desde datos del propietario\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1413,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1376,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1424,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaTipoCliente\",\n                                                                        value: formData.empresaTipoCliente,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaTipoCliente\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione tipo de cliente\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1447,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            const option = tipoClienteOptions.find((opt)=>opt.value === selected);\n                                                                            return option ? \"\".concat(option.icon, \" \").concat(option.value) : selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1469,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: option.value,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                        sx: {\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\",\n                                                                                            gap: 1\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.icon\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1481,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.value\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1482,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1474,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, option.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1473,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1431,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1488,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1427,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio de la Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1497,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Av. San Mart\\xedn 1234\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaDomicilio\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaDomicilio),\n                                                                helperText: error.empresaDomicilio,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaDomicilio && (error.empresaDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1515,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1517,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1512,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1500,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1496,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1527,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaLocalidad\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaLocalidad),\n                                                                helperText: error.empresaLocalidad,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaLocalidad && (error.empresaLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1546,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1548,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1543,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1530,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1526,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1557,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaProvincia\",\n                                                                        value: formData.empresaProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione provincia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1580,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione provincia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1597,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: provincia,\n                                                                                    children: provincia\n                                                                                }, provincia, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1601,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1564,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1607,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1560,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1556,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Condici\\xf3n frente al IVA *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1616,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaCondFrenteIva\",\n                                                                        value: formData.empresaCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione condici\\xf3n IVA\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1639,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1656,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            condFrenteIvaOptions.map((condicion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: condicion,\n                                                                                    children: condicion\n                                                                                }, condicion, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1660,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1623,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1666,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1619,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1615,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1374,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1684,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1691,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1675,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1363,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Contactos/Encargados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1709,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            formData.contactos.map((contacto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    sx: {\n                                                        mb: 4,\n                                                        p: 3,\n                                                        border: \"1px solid #e0e0e0\",\n                                                        borderRadius: 2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                justifyContent: \"space-between\",\n                                                                alignItems: \"center\",\n                                                                mb: 2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    sx: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        gap: 1\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#2E7D32\",\n                                                                                fontSize: \"1.2rem\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1742,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"subtitle1\",\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#333\"\n                                                                            },\n                                                                            children: [\n                                                                                \"Contacto #\",\n                                                                                index + 1\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1748,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1739,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.contactos.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"outlined\",\n                                                                    color: \"error\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>{\n                                                                        setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                contactos: prev.contactos.filter((c)=>c.id !== contacto.id)\n                                                                            }));\n                                                                    },\n                                                                    children: \"Eliminar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1756,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1731,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            container: true,\n                                                            spacing: 3,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre del Encargado *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1777,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Mar\\xeda Gonz\\xe1lez\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_nombre\"),\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_nombre\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_nombre\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].nombre = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.nombre,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.nombre && (error[\"contacto_\".concat(contacto.id, \"_nombre\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1809,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1811,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1806,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1780,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1776,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Cargo (Opcional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1821,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Administrador, Encargado\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_cargo\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].cargo = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.cargo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1824,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1820,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tel\\xe9fono de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1848,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 0000-000000\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_telefono\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_telefono\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_telefono\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].telefono = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.telefono,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.telefono && (error[\"contacto_\".concat(contacto.id, \"_telefono\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1883,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1885,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1878,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1851,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1847,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Email de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1895,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: <EMAIL>\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_email\"),\n                                                                            type: \"email\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_email\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_email\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].email = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.email,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.email && (error[\"contacto_\".concat(contacto.id, \"_email\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1926,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1928,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1923,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1898,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1894,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1774,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, contacto.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1722,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1942,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: ()=>{\n                                                    const newId = Math.max(...formData.contactos.map((c)=>c.id)) + 1;\n                                                    setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactos: [\n                                                                ...prev.contactos,\n                                                                {\n                                                                    id: newId,\n                                                                    nombre: \"\",\n                                                                    cargo: \"\",\n                                                                    telefono: \"\",\n                                                                    email: \"\"\n                                                                }\n                                                            ]\n                                                        }));\n                                                },\n                                                sx: {\n                                                    mt: 2\n                                                },\n                                                children: \"Agregar otro contacto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1940,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1975,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        type: \"submit\",\n                                                        disabled: isSubmitting,\n                                                        onClick: handleAddCliente,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: isSubmitting ? \"Guardando...\" : estadoModal === \"add\" ? \"Registrar\" : \"Actualizar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1982,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1966,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1708,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1078,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1073,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 1041,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 1028,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"BbVUwd51WSh2d/v81WqeKFo1Vys=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKHBhZ2luYXMpL2FncmljdWx0b3IvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFvQ01BO0FBbkNxRDtBQUNHO0FBQ0g7QUFTcEM7QUFDZ0I7QUFDTztBQUNGO0FBQ007QUFDTjtBQUNHO0FBRUo7QUFFRjtBQUNGO0FBQ0c7QUFDQztBQUNjO0FBQ1A7QUFDaEI7QUFDNEI7QUFDWjtBQUNTO0FBRUQ7QUFDTjtBQXdCcEQsTUFBTWtDLHFCQUF3RDs7SUFDNUQsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ21DLGFBQWFDLGVBQWUsR0FBR3BDLCtDQUFRQSxDQUFnQjtJQUM5RCxNQUFNLENBQUNxQyxNQUFNQyxRQUFRLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNLENBQUN1QyxNQUFNQyxRQUFRLEdBQUd4QywrQ0FBUUEsQ0FBVyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ3lDLFlBQVlDLGNBQWMsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQzJDLGlCQUFpQkMsbUJBQW1CLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUM2QyxjQUFjQyxnQkFBZ0IsR0FBRzlDLCtDQUFRQSxDQUFXLEVBQUU7SUFDN0QsTUFBTUksV0FBV0Msc0RBQVdBO0lBQzVCLE1BQU0sQ0FBQzBDLFVBQVVDLFlBQVksR0FBR2hELCtDQUFRQSxDQUFTO0lBQ2pELE1BQU0sQ0FBQ2lELGFBQWFDLGVBQWUsR0FBR2xELCtDQUFRQSxDQUFtQjtJQUNqRSxNQUFNLENBQUNtRCxrQkFBa0JDLG9CQUFvQixHQUFHcEQsK0NBQVFBLENBQWdCO0lBQ3hFLE1BQU0sQ0FBQ3FELFVBQVVDLFlBQVksR0FBR3RELCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3VELGFBQWFDLGVBQWUsR0FBR3hELCtDQUFRQSxDQUFZO1FBQ3hEO1FBQ0E7UUFDQTtLQUNELEdBQUcsa0RBQWtEO0lBQ3RELE1BQU15RCxxQkFBcUJ2RCw2Q0FBTUEsQ0FBdUM7SUFDeEUsTUFBTXdELG1CQUFtQnhELDZDQUFNQSxDQUF1QztJQUN0RSxNQUFNeUQsZUFBZXpELDZDQUFNQSxDQUF1QztJQUNsRSxNQUFNLENBQUMwRCxPQUFPQyxTQUFTLEdBQUc3RCwrQ0FBUUEsQ0FBNEIsQ0FBQztJQUUvRCw0Q0FBNEM7SUFDNUMsTUFBTThELGVBQWU7UUFDbkIsTUFBTSxFQUNKQyxpQkFBaUIsRUFDakJDLG9CQUFvQixFQUNwQkMsbUJBQW1CLEVBQ25CQyxnQkFBZ0IsRUFDakIsR0FBR0M7UUFDSixPQUFPLENBQUMsQ0FDTkosQ0FBQUEsa0JBQWtCSyxJQUFJLE1BQ3RCSixxQkFBcUJJLElBQUksTUFDekJILG9CQUFvQkcsSUFBSSxNQUN4QkYsaUJBQWlCRSxJQUFJLEVBQUM7SUFFMUI7SUFFQSxNQUFNQyxlQUFlO1FBQ25CLE1BQU0sRUFDSkMsa0JBQWtCLEVBQ2xCQyxrQkFBa0IsRUFDbEJDLGdCQUFnQixFQUNoQkMsZ0JBQWdCLEVBQ2hCQyxnQkFBZ0IsRUFDaEJDLG9CQUFvQixFQUNyQixHQUFHUjtRQUNKLE9BQU8sQ0FBQyxDQUNORyxDQUFBQSxtQkFBbUJGLElBQUksTUFDdkJHLG1CQUFtQkgsSUFBSSxNQUN2QkksaUJBQWlCSixJQUFJLE1BQ3JCSyxpQkFBaUJMLElBQUksTUFDckJNLGlCQUFpQk4sSUFBSSxNQUNyQk8scUJBQXFCUCxJQUFJLEVBQUM7SUFFOUI7SUFFQSxNQUFNUSxlQUFlO1FBQ25CLE9BQ0VULFNBQVNVLFNBQVMsQ0FBQ0MsTUFBTSxHQUFHLEtBQzVCWCxTQUFTVSxTQUFTLENBQUMsRUFBRSxDQUFDRSxNQUFNLENBQUNYLElBQUksT0FBTyxNQUN4Q0QsU0FBU1UsU0FBUyxDQUFDLEVBQUUsQ0FBQ0csS0FBSyxDQUFDWixJQUFJLE9BQU8sTUFDdkNELFNBQVNVLFNBQVMsQ0FBQyxFQUFFLENBQUNJLFFBQVEsQ0FBQ2IsSUFBSSxPQUFPLE1BQzFDRCxTQUFTVSxTQUFTLENBQUMsRUFBRSxDQUFDSyxLQUFLLENBQUNkLElBQUksT0FBTztJQUUzQztJQUVBLDhDQUE4QztJQUM5QyxNQUFNZSxnQkFBZ0IsQ0FBQ0M7UUFDckIsTUFBTUMsaUJBQWlCO2VBQUk5QjtTQUFZO1FBQ3ZDLElBQUk2QixhQUFhLElBQUlDLGVBQWVQLE1BQU0sRUFBRTtZQUMxQ08sY0FBYyxDQUFDRCxhQUFhLEVBQUUsR0FBRztZQUNqQzVCLGVBQWU2QjtRQUNqQjtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU1DLGdCQUFnQjtRQUNwQixJQUFJQyxhQUFhO1FBRWpCLE9BQVFsQztZQUNOLEtBQUs7Z0JBQ0hrQyxhQUFhekI7Z0JBQ2I7WUFDRixLQUFLO2dCQUNIeUIsYUFBYWxCO2dCQUNiO1lBQ0YsS0FBSztnQkFDSGtCLGFBQWFYO2dCQUNiO1FBQ0o7UUFFQSxJQUFJVyxZQUFZO1lBQ2RKLGNBQWM5QjtZQUNkQyxZQUFZRCxXQUFXO1FBQ3pCLE9BQU87WUFDTG1DLE1BQ0U7UUFFSjtJQUNGO0lBRUEsd0NBQXdDO0lBQ3hDLE1BQU1DLG9CQUFvQjtRQUN4QixJQUFJcEMsV0FBVyxHQUFHO1lBQ2hCQyxZQUFZRCxXQUFXO1FBQ3pCO0lBQ0Y7SUFFQSxNQUFNcUMscUJBQXFCLENBQUNDO1FBQzFCdkMsb0JBQW9CdUM7UUFDcEIsTUFBTUMsaUJBQWlCLEtBQW1CQyxJQUFJLENBQzVDLENBQUNDLE1BQVFBLElBQUlDLEVBQUUsS0FBS0o7UUFFdEIsSUFBSUMsZ0JBQWdCO1lBQ2xCLHFEQUFxRDtZQUNyRCxNQUFNLENBQUNJLFdBQVdDLFVBQVUsR0FBR0wsZUFBZU0sS0FBSyxDQUFDQyxLQUFLLENBQUM7WUFFMURDLFlBQVksQ0FBQ0MsV0FBYztvQkFDekIsR0FBR0EsUUFBUTtvQkFDWC9CLG9CQUFvQnNCLGVBQWVVLFdBQVc7b0JBQzlDL0Isb0JBQW9CcUIsZUFBZVcsV0FBVyxJQUFJO29CQUNsRC9CLGtCQUFrQm9CLGVBQWVZLFNBQVM7b0JBQzFDL0Isa0JBQWtCdUI7b0JBQ2xCdEIsa0JBQWtCdUI7b0JBQ2xCdEIsc0JBQXNCaUIsZUFBZWEsYUFBYTtvQkFDbEQsdUVBQXVFO29CQUN2RTVCLFdBQVc7d0JBQ1Q7NEJBQ0VrQixJQUFJOzRCQUNKaEIsUUFBUWEsZUFBZWMsY0FBYyxJQUFJOzRCQUN6QzFCLE9BQU9ZLGVBQWVlLGFBQWEsSUFBSTs0QkFDdkMxQixVQUFVVyxlQUFlWCxRQUFROzRCQUNqQ0MsT0FBT1UsZUFBZWdCLElBQUk7d0JBQzVCO3FCQUNEO2dCQUNIO1FBQ0Y7SUFDRjtJQUVBLE1BQU0sQ0FBQ3pDLFVBQVVpQyxZQUFZLEdBQUdwRywrQ0FBUUEsQ0FBQztRQUN2Qyx3Q0FBd0M7UUFDeEMrRCxtQkFBbUI7UUFDbkJDLHNCQUFzQjtRQUN0QkMscUJBQXFCO1FBQ3JCQyxrQkFBa0I7UUFDbEIyQyxxQkFBcUI7UUFFckIsa0NBQWtDO1FBQ2xDdkMsb0JBQW9CO1FBQ3BCQyxvQkFBb0I7UUFDcEJDLGtCQUFrQjtRQUNsQkMsa0JBQWtCO1FBQ2xCQyxrQkFBa0I7UUFDbEJDLHNCQUFzQjtRQUV0QixtREFBbUQ7UUFDbkRFLFdBQVc7WUFDVDtnQkFDRWtCLElBQUk7Z0JBQ0poQixRQUFRO2dCQUNSQyxPQUFPO2dCQUNQQyxVQUFVO2dCQUNWQyxPQUFPO1lBQ1Q7U0FDRDtJQUNIO0lBRUEsTUFBTTRCLGFBQWE7UUFDakI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRUQsTUFBTUMscUJBQXFCO1FBQ3pCLGNBQWM7UUFDZDtZQUFFQyxPQUFPO1lBQXdCQyxVQUFVO1lBQWVDLE1BQU07UUFBUTtRQUN4RTtZQUFFRixPQUFPO1lBQXVCQyxVQUFVO1lBQWVDLE1BQU07UUFBYztRQUM3RTtZQUFFRixPQUFPO1lBQVlDLFVBQVU7WUFBZUMsTUFBTTtRQUFNO1FBRTFELDRCQUE0QjtRQUM1QjtZQUNFRixPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsTUFBTTtRQUNSO1FBQ0E7WUFBRUYsT0FBTztZQUFlQyxVQUFVO1lBQVlDLE1BQU07UUFBTTtRQUMxRDtZQUNFRixPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsTUFBTTtRQUNSO1FBRUEsMkJBQTJCO1FBQzNCO1lBQ0VGLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxNQUFNO1FBQ1I7UUFDQTtZQUNFRixPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsTUFBTTtRQUNSO1FBRUEseUJBQXlCO1FBQ3pCO1lBQ0VGLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxNQUFNO1FBQ1I7UUFDQTtZQUNFRixPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsTUFBTTtRQUNSO1FBQ0E7WUFBRUYsT0FBTztZQUFpQ0MsVUFBVTtZQUFTQyxNQUFNO1FBQUk7UUFDdkU7WUFBRUYsT0FBTztZQUFtQkMsVUFBVTtZQUFTQyxNQUFNO1FBQUk7S0FDMUQ7SUFFRCxNQUFNQyx1QkFBdUI7UUFDM0I7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELE1BQU1DLGdCQUFnQjtRQUNwQmxFLGVBQWU7UUFDZm1FO1FBQ0EvRSxRQUFRO0lBQ1Y7SUFFQSxNQUFNK0UsWUFBWTtRQUNoQmpCLFlBQVk7WUFDVix3Q0FBd0M7WUFDeENyQyxtQkFBbUI7WUFDbkJDLHNCQUFzQjtZQUN0QkMscUJBQXFCO1lBQ3JCQyxrQkFBa0I7WUFDbEIyQyxxQkFBcUI7WUFFckIsa0NBQWtDO1lBQ2xDdkMsb0JBQW9CO1lBQ3BCQyxvQkFBb0I7WUFDcEJDLGtCQUFrQjtZQUNsQkMsa0JBQWtCO1lBQ2xCQyxrQkFBa0I7WUFDbEJDLHNCQUFzQjtZQUV0QixrQ0FBa0M7WUFDbENFLFdBQVc7Z0JBQ1Q7b0JBQ0VrQixJQUFJO29CQUNKaEIsUUFBUTtvQkFDUkMsT0FBTztvQkFDUEMsVUFBVTtvQkFDVkMsT0FBTztnQkFDVDthQUNEO1FBQ0g7UUFDQXJCLFNBQVMsQ0FBQztRQUNWUCxZQUFZLElBQUkseUJBQXlCO1FBQ3pDRSxlQUFlO1lBQUM7WUFBTTtZQUFPO1NBQU0sR0FBRyxnQ0FBZ0M7SUFDeEU7SUFFQSxNQUFNOEQsbUJBQW1CLENBQ3ZCQyxRQUNBQztRQUVBLElBQUlBLFVBQVVBLFdBQVcsaUJBQWlCO1FBQzFDbEYsUUFBUTtJQUNWO0lBRUEsTUFBTW1GLG9CQUFvQixDQUN4QkM7UUFJQSxNQUFNLEVBQUVDLElBQUksRUFBRVgsS0FBSyxFQUFFLEdBQUdVLEVBQUVFLE1BQU07UUFFaEMsa0VBQWtFO1FBQ2xFLElBQ0VELFNBQVMsdUJBQ1RBLFNBQVMsd0JBQ1RBLFNBQVMsc0JBQ1JBLEtBQUtFLFVBQVUsQ0FBQyxlQUFlRixLQUFLRyxRQUFRLENBQUMsV0FDOUM7WUFDQSxJQUFJLENBQUMsbUJBQW1CQyxJQUFJLENBQUNmLFFBQVE7Z0JBQ25DbkQsU0FBUyxDQUFDbUUsWUFBZTt3QkFDdkIsR0FBR0EsU0FBUzt3QkFDWixDQUFDTCxLQUFLLEVBQUU7b0JBQ1Y7Z0JBQ0E7WUFDRixPQUFPO2dCQUNMOUQsU0FBUyxDQUFDbUUsWUFBZTt3QkFDdkIsR0FBR0EsU0FBUzt3QkFDWixDQUFDTCxLQUFLLEVBQUU7b0JBQ1Y7WUFDRjtRQUNGO1FBRUEsOEJBQThCO1FBQzlCLElBQUlBLFNBQVMsb0JBQW9CO1lBQy9CLElBQUksQ0FBQyx1QkFBdUJJLElBQUksQ0FBQ2YsUUFBUTtnQkFDdkNuRCxTQUFTLENBQUNtRSxZQUFlO3dCQUN2QixHQUFHQSxTQUFTO3dCQUNaLENBQUNMLEtBQUssRUFBRTtvQkFDVjtnQkFDQTtZQUNGLE9BQU87Z0JBQ0w5RCxTQUFTLENBQUNtRSxZQUFlO3dCQUN2QixHQUFHQSxTQUFTO3dCQUNaLENBQUNMLEtBQUssRUFBRTtvQkFDVjtZQUNGO1FBQ0Y7UUFFQSw4QkFBOEI7UUFDOUIsSUFBSUEsU0FBUyx5QkFBeUJBLEtBQUtHLFFBQVEsQ0FBQyxhQUFhO1lBQy9ELHFDQUFxQztZQUNyQyxNQUFNRyxVQUFVakIsTUFBTWtCLE9BQU8sQ0FBQyxPQUFPO1lBRXJDLHVCQUF1QjtZQUN2QixJQUFJRCxRQUFRbkQsTUFBTSxHQUFHLElBQUk7WUFFekIsSUFBSXFEO1lBQ0osSUFBSUYsUUFBUW5ELE1BQU0sSUFBSSxHQUFHO2dCQUN2QnFELFlBQVlGO1lBQ2QsT0FBTztnQkFDTEUsWUFBWSxHQUEwQkYsT0FBdkJBLFFBQVFHLEtBQUssQ0FBQyxHQUFHLElBQUcsS0FBb0IsT0FBakJILFFBQVFHLEtBQUssQ0FBQztZQUN0RDtZQUVBLDhCQUE4QjtZQUM5QixNQUFNQyxnQkFBZ0IsZ0JBQWdCTixJQUFJLENBQUNJO1lBRTNDdEUsU0FBUyxDQUFDbUUsWUFBZTtvQkFDdkIsR0FBR0EsU0FBUztvQkFDWixDQUFDTCxLQUFLLEVBQ0pRLFVBQVVyRCxNQUFNLEtBQUssTUFBTSxDQUFDdUQsZ0JBQ3hCLDhDQUNBO2dCQUNSO1lBRUFqQyxZQUFZLENBQUNrQyxZQUFlO29CQUMxQixHQUFHQSxTQUFTO29CQUNaLENBQUNYLEtBQUssRUFBRVE7Z0JBQ1Y7WUFDQTtRQUNGO1FBRUEsSUFBSVIsU0FBUyxjQUFjO1lBQ3pCLHVDQUF1QztZQUN2QyxNQUFNWSxhQUFhO1lBRW5CLGdEQUFnRDtZQUNoRCxJQUFJdkIsU0FBUyxDQUFDdUIsV0FBV1IsSUFBSSxDQUFDZixRQUFRO2dCQUNwQ25ELFNBQVMsQ0FBQ21FLFlBQWU7d0JBQ3ZCLEdBQUdBLFNBQVM7d0JBQ1osQ0FBQ0wsS0FBSyxFQUFFO29CQUNWO1lBQ0YsT0FBTztnQkFDTDlELFNBQVMsQ0FBQ21FLFlBQWU7d0JBQ3ZCLEdBQUdBLFNBQVM7d0JBQ1osQ0FBQ0wsS0FBSyxFQUFFO29CQUNWO1lBQ0Y7UUFDRjtRQUVBLElBQUlBLFNBQVMsd0JBQXdCO1lBQ25DLHFDQUFxQztZQUNyQyxNQUFNTSxVQUFVakIsTUFBTWtCLE9BQU8sQ0FBQyxPQUFPO1lBRXJDLGdDQUFnQztZQUNoQyxJQUFJRCxRQUFRbkQsTUFBTSxHQUFHLElBQUk7WUFFekIsSUFBSXFEO1lBQ0osSUFBSUYsUUFBUW5ELE1BQU0sSUFBSSxHQUFHO2dCQUN2QnFELFlBQVlGO1lBQ2QsT0FBTyxJQUFJQSxRQUFRbkQsTUFBTSxJQUFJLElBQUk7Z0JBQy9CcUQsWUFBWSxHQUEwQkYsT0FBdkJBLFFBQVFHLEtBQUssQ0FBQyxHQUFHLElBQUcsS0FBb0IsT0FBakJILFFBQVFHLEtBQUssQ0FBQztZQUN0RCxPQUFPO2dCQUNMRCxZQUFZLEdBQTBCRixPQUF2QkEsUUFBUUcsS0FBSyxDQUFDLEdBQUcsSUFBRyxLQUc5QkgsT0FIaUNBLFFBQVFHLEtBQUssQ0FDakQsR0FDQSxLQUNBLEtBQXlCLE9BQXRCSCxRQUFRRyxLQUFLLENBQUMsSUFBSTtZQUN6QjtZQUVBLDhCQUE4QjtZQUM5QixNQUFNQyxnQkFBZ0Isc0JBQXNCTixJQUFJLENBQUNJO1lBRWpEdEUsU0FBUyxDQUFDbUUsWUFBZTtvQkFDdkIsR0FBR0EsU0FBUztvQkFDWixDQUFDTCxLQUFLLEVBQ0pRLFVBQVVyRCxNQUFNLEtBQUssTUFBTSxDQUFDdUQsZ0JBQ3hCLGdEQUNBO2dCQUNSO1lBRUFqQyxZQUFZLENBQUNrQyxZQUFlO29CQUMxQixHQUFHQSxTQUFTO29CQUNaLENBQUNYLEtBQUssRUFBRVE7Z0JBQ1Y7WUFDQTtRQUNGO1FBRUEvQixZQUFZLENBQUNrQztZQUNYLE1BQU1FLFdBQVc7Z0JBQ2YsR0FBR0YsU0FBUztnQkFDWixDQUFDWCxLQUFLLEVBQUVYO1lBQ1Y7WUFFQSx5RUFBeUU7WUFDekUsSUFBSXNCLFVBQVV6QixtQkFBbUIsRUFBRTtnQkFDakMsSUFBSWMsU0FBUyxxQkFBcUI7b0JBQ2hDYSxTQUFTbEUsa0JBQWtCLEdBQUcwQztvQkFDOUIsd0NBQXdDO29CQUN4Q3dCLFNBQVMzRCxTQUFTLEdBQUc7d0JBQ25COzRCQUNFLEdBQUd5RCxVQUFVekQsU0FBUyxDQUFDLEVBQUU7NEJBQ3pCRSxRQUFRaUM7d0JBQ1Y7MkJBQ0dzQixVQUFVekQsU0FBUyxDQUFDdUQsS0FBSyxDQUFDO3FCQUM5QjtnQkFDSCxPQUFPLElBQUlULFNBQVMsdUJBQXVCO29CQUN6Qyw2Q0FBNkM7b0JBQzdDYSxTQUFTM0QsU0FBUyxHQUFHO3dCQUNuQjs0QkFDRSxHQUFHeUQsVUFBVXpELFNBQVMsQ0FBQyxFQUFFOzRCQUN6QkksVUFBVStCO3dCQUNaOzJCQUNHc0IsVUFBVXpELFNBQVMsQ0FBQ3VELEtBQUssQ0FBQztxQkFDOUI7Z0JBQ0gsT0FBTyxJQUFJVCxTQUFTLG9CQUFvQjtvQkFDdEMsMENBQTBDO29CQUMxQ2EsU0FBUzNELFNBQVMsR0FBRzt3QkFDbkI7NEJBQ0UsR0FBR3lELFVBQVV6RCxTQUFTLENBQUMsRUFBRTs0QkFDekJLLE9BQU84Qjt3QkFDVDsyQkFDR3NCLFVBQVV6RCxTQUFTLENBQUN1RCxLQUFLLENBQUM7cUJBQzlCO2dCQUNIO1lBQ0Y7WUFFQSxPQUFPSTtRQUNUO0lBQ0Y7SUFFQSxNQUFNQyxlQUFlLENBQUNmO1FBQ3BCQSxFQUFFZ0IsY0FBYztJQUNsQjtJQUVBLE1BQU1DLG9CQUFvQjtRQUN4Qi9GLG1CQUFtQixDQUFDRDtJQUN0QjtJQUVBLE1BQU1pRyxVQUFVO1FBQ2Q7WUFDRUMsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1lBQ2pCQyxZQUFZLENBQUNDLHVCQUNYLDhEQUFDekgsbURBQUdBO29CQUFDMEgsSUFBSTt3QkFBRUMsSUFBSTtvQkFBRTs7c0NBQ2YsOERBQUM5SCxzRkFBVUE7NEJBQ1QrSCxTQUFROzRCQUNSRixJQUFJO2dDQUNGRyxZQUFZO2dDQUNaQyxPQUFPO2dDQUNQQyxVQUFVO2dDQUNWQyxZQUFZOzRCQUNkO3NDQUVDUCxPQUFPcEQsR0FBRyxDQUFDUSxXQUFXOzs7Ozs7d0JBRXhCNEMsT0FBT3BELEdBQUcsQ0FBQ1MsV0FBVyxrQkFDckIsOERBQUNqRixzRkFBVUE7NEJBQ1QrSCxTQUFROzRCQUNSRixJQUFJO2dDQUNGSSxPQUFPO2dDQUNQQyxVQUFVO2dDQUNWRSxXQUFXOzRCQUNiO3NDQUVDUixPQUFPcEQsR0FBRyxDQUFDUyxXQUFXOzs7Ozs7Ozs7Ozs7UUFLakM7UUFDQTtZQUNFc0MsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1lBQ2pCQyxZQUFZLENBQUNDLHVCQUNYLDhEQUFDekgsbURBQUdBO29CQUFDMEgsSUFBSTt3QkFBRUMsSUFBSTtvQkFBRTs4QkFDZEYsT0FBT3BELEdBQUcsQ0FBQ1ksY0FBYyxpQkFDeEI7OzBDQUNFLDhEQUFDcEYsc0ZBQVVBO2dDQUNUK0gsU0FBUTtnQ0FDUkYsSUFBSTtvQ0FDRkcsWUFBWTtvQ0FDWkMsT0FBTztvQ0FDUEMsVUFBVTtvQ0FDVkMsWUFBWTtnQ0FDZDswQ0FFQ1AsT0FBT3BELEdBQUcsQ0FBQ1ksY0FBYzs7Ozs7OzRCQUUzQndDLE9BQU9wRCxHQUFHLENBQUNhLGFBQWEsa0JBQ3ZCLDhEQUFDckYsc0ZBQVVBO2dDQUNUK0gsU0FBUTtnQ0FDUkYsSUFBSTtvQ0FDRkksT0FBTztvQ0FDUEMsVUFBVTtvQ0FDVkUsV0FBVztnQ0FDYjswQ0FFQ1IsT0FBT3BELEdBQUcsQ0FBQ2EsYUFBYTs7Ozs7OztxREFLL0IsOERBQUNyRixzRkFBVUE7d0JBQ1QrSCxTQUFRO3dCQUNSRixJQUFJOzRCQUNGSSxPQUFPOzRCQUNQRyxXQUFXOzRCQUNYRixVQUFVO3dCQUNaO2tDQUNEOzs7Ozs7Ozs7OztRQU1UO1FBQ0E7WUFDRVgsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO0tBQ0Q7SUFFRCw2QkFBNkIsR0FDN0IsTUFBTVcsbUJBQW1CO1lBb0JMeEYsc0JBQ0RBLHVCQUNMQSx1QkFDSkE7UUF0QlJ5RixRQUFRQyxHQUFHLENBQUM7UUFDWjNILGdCQUFnQjtRQUNoQixNQUFNZ0UsUUFBUSxHQUFrQy9CLE9BQS9CQSxTQUFTTSxnQkFBZ0IsRUFBQyxPQUErQixPQUExQk4sU0FBU08sZ0JBQWdCO1FBQ3pFLE1BQU1vRixZQUFZO1lBQ2hCLG1EQUFtRDtZQUNuRC9GLG1CQUFtQkksU0FBU0osaUJBQWlCO1lBQzdDQyxzQkFBc0JHLFNBQVNILG9CQUFvQjtZQUNuREMscUJBQXFCRSxTQUFTRixtQkFBbUI7WUFDakRDLGtCQUFrQkMsU0FBU0QsZ0JBQWdCO1lBRTNDLCtDQUErQztZQUMvQ29DLGFBQWFuQyxTQUFTRyxrQkFBa0I7WUFDeENpQyxhQUFhcEMsU0FBU0ksa0JBQWtCO1lBQ3hDaUMsV0FBV3JDLFNBQVNLLGdCQUFnQjtZQUNwQ0Msa0JBQWtCTixTQUFTTSxnQkFBZ0I7WUFDM0N3QixXQUFXOUIsU0FBU08sZ0JBQWdCO1lBQ3BDK0IsZUFBZXRDLFNBQVNRLG9CQUFvQjtZQUU1QyxxREFBcUQ7WUFDckQrQixnQkFBZ0J2QyxFQUFBQSx1QkFBQUEsU0FBU1UsU0FBUyxDQUFDLEVBQUUsY0FBckJWLDJDQUFBQSxxQkFBdUJZLE1BQU0sS0FBSTtZQUNqRDRCLGVBQWV4QyxFQUFBQSx3QkFBQUEsU0FBU1UsU0FBUyxDQUFDLEVBQUUsY0FBckJWLDRDQUFBQSxzQkFBdUJhLEtBQUssS0FBSTtZQUMvQ0MsVUFBVWQsRUFBQUEsd0JBQUFBLFNBQVNVLFNBQVMsQ0FBQyxFQUFFLGNBQXJCViw0Q0FBQUEsc0JBQXVCYyxRQUFRLEtBQUk7WUFDN0MyQixNQUFNekMsRUFBQUEsd0JBQUFBLFNBQVNVLFNBQVMsQ0FBQyxFQUFFLGNBQXJCViw0Q0FBQUEsc0JBQXVCZSxLQUFLLEtBQUk7WUFFdEMsOEJBQThCO1lBQzlCZ0IsT0FBT0E7WUFDUDZELFdBQVc1RixTQUFTSCxvQkFBb0I7UUFDMUM7UUFFQSw2Q0FBNkM7UUFDN0MsNkVBQTZFO1FBRTdFLDZDQUE2QztRQUM3QzRGLFFBQVFDLEdBQUcsQ0FBQyxvQkFBaUJDLFVBQVV4RCxXQUFXO1FBQ2xEc0QsUUFBUUMsR0FBRyxDQUFDLGlCQUFjQyxVQUFVdEQsU0FBUztRQUM3Q29ELFFBQVFDLEdBQUcsQ0FBQyxnQkFBYUMsVUFBVTdFLFFBQVE7UUFDM0MyRSxRQUFRQyxHQUFHLENBQUMsU0FBU0MsVUFBVWxELElBQUk7UUFDbkNnRCxRQUFRQyxHQUFHLENBQUMsVUFBVTNEO1FBQ3RCMEQsUUFBUUMsR0FBRyxDQUFDLDRCQUF5QkMsVUFBVXJELGFBQWE7UUFDNURtRCxRQUFRQyxHQUFHLENBQUMsY0FBY0MsVUFBVUMsU0FBUztRQUU3QyxJQUFJO1lBQ0YsTUFBTUMsTUFBTSxNQUFNQyxNQUFNLHdDQUF3QztnQkFDOURDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDUjtZQUN2QjtZQUVBLHdDQUF3QztZQUN4QyxJQUFJLENBQUNFLElBQUlPLEVBQUUsRUFBRTtnQkFDWCxNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQW5EO1lBQ0EsTUFBTW9ELGVBQWUsTUFBTUM7WUFDM0JsSSxRQUFRaUk7WUFDUm5JLFFBQVE7UUFDVixFQUFFLE9BQU9zQixPQUFPO1lBQ2RnRyxRQUFRaEcsS0FBSyxDQUFDLDBCQUEwQkE7UUFDMUM7SUFDRjtJQUVBLDJDQUEyQyxHQUMzQyxNQUFNOEcsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRixNQUFNVixNQUFNLE1BQU1DLE1BQU07WUFDeEIsSUFBSSxDQUFDRCxJQUFJTyxFQUFFLEVBQUU7Z0JBQ1gsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBQ0EsTUFBTUMsZUFBZSxNQUFNVCxJQUFJVyxJQUFJO1lBQ25DLE9BQU9GO1FBQ1QsRUFBRSxPQUFPN0csT0FBTztZQUNkZ0csUUFBUWhHLEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDLG9EQUFvRDtZQUNwRCxPQUFPLEVBQUU7UUFDWDtJQUNGO0lBRUEzRCxnREFBU0EsQ0FBQztRQUNSLE1BQU0ySyxVQUFVO1lBQ2QsTUFBTUgsZUFBZSxNQUFNQztZQUMzQmxJLFFBQVFpSTtRQUNWO1FBRUFHO0lBQ0YsR0FBRyxFQUFFO0lBRUwsNEJBQTRCLEdBQzVCLE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQixNQUFNQyxjQUFjRCxNQUFNbEQsTUFBTSxDQUFDWixLQUFLO1FBQ3RDZ0UsY0FBY0Q7UUFFZCxNQUFNRSxlQUFlMUksS0FBSzJJLE1BQU0sQ0FBQyxDQUFDcEY7WUFDaEMsT0FDRUEsSUFBSVEsV0FBVyxDQUFDNkUsV0FBVyxHQUFHckQsUUFBUSxDQUFDaUQsWUFBWUksV0FBVyxPQUM5RHJGLElBQUlVLFNBQVMsQ0FBQzJFLFdBQVcsR0FBR3JELFFBQVEsQ0FBQ2lELFlBQVlJLFdBQVcsT0FDNURyRixJQUFJYixRQUFRLENBQUNrRyxXQUFXLEdBQUdyRCxRQUFRLENBQUNpRCxZQUFZSSxXQUFXLE9BQzNEckYsSUFBSWMsSUFBSSxDQUFDdUUsV0FBVyxHQUFHckQsUUFBUSxDQUFDaUQsWUFBWUksV0FBVyxPQUN2RHJGLElBQUlJLEtBQUssQ0FBQ2lGLFdBQVcsR0FBR3JELFFBQVEsQ0FBQ2lELFlBQVlJLFdBQVcsT0FDeERyRixJQUFJVyxhQUFhLENBQUMwRSxXQUFXLEdBQUdyRCxRQUFRLENBQUNpRCxZQUFZSSxXQUFXLE9BQ2hFckYsSUFBSWlFLFNBQVMsQ0FBQ29CLFdBQVcsR0FBR3JELFFBQVEsQ0FBQ2lELFlBQVlJLFdBQVc7UUFFaEU7UUFDQXJJLGdCQUFnQm1JO0lBQ2xCO0lBRUEsOEJBQThCLEdBQzlCLE1BQU1HLHNCQUFzQixPQUFPckY7UUFDakM2RCxRQUFRQyxHQUFHLENBQUMsdUJBQXVCOUQ7UUFFbkMsSUFBSTtZQUNGLE1BQU1pRSxNQUFNLE1BQU1DLE1BQU0sd0NBQTJDLE9BQUhsRSxLQUFNO2dCQUNwRW1FLFFBQVE7WUFDVjtZQUVBLElBQUlGLElBQUlPLEVBQUUsRUFBRTtnQkFDVlgsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLG1FQUFtRTtnQkFDbkUsTUFBTVksZUFBZSxNQUFNQztnQkFDM0JsSSxRQUFRaUk7WUFDVixPQUFPO2dCQUNMYixRQUFRaEcsS0FBSyxDQUFDLGlDQUFpQ21DO1lBQ2pEO1FBQ0YsRUFBRSxPQUFPbkMsT0FBTztZQUNkZ0csUUFBUWhHLEtBQUssQ0FBQyw0Q0FBeUNBO1FBQ3pEO0lBQ0Y7SUFFQSw4QkFBOEIsR0FDOUIsTUFBTXlILGFBQWEsT0FBT3RGO1FBQ3hCLElBQUk7WUFDRixNQUFNaUUsTUFBTSxNQUFNQyxNQUFNLHdDQUEyQyxPQUFIbEUsS0FBTTtnQkFDcEVtRSxRQUFRO1lBQ1Y7WUFFQSxJQUFJRixJQUFJTyxFQUFFLEVBQUU7Z0JBQ1ZYLFFBQVFDLEdBQUcsQ0FBQztnQkFFWixNQUFNeUIsYUFBYSxNQUFNdEIsSUFBSVcsSUFBSTtnQkFDakN2RSxZQUFZLENBQUNDLFdBQWM7d0JBQ3pCLEdBQUdBLFFBQVE7d0JBQ1gvQixvQkFBb0JnSCxXQUFXaEYsV0FBVzt3QkFDMUMvQixvQkFBb0IrRyxXQUFXL0UsV0FBVyxJQUFJO3dCQUM5Qy9CLGtCQUFrQjhHLFdBQVc5RSxTQUFTO3dCQUN0Qy9CLGtCQUFrQjZHLFdBQVdwRixLQUFLLENBQUNDLEtBQUssQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDL0IsSUFBSTt3QkFDdkRNLGtCQUFrQjRHLFdBQVdwRixLQUFLLENBQUNDLEtBQUssQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDL0IsSUFBSTt3QkFDdkRPLHNCQUFzQjJHLFdBQVc3RSxhQUFhO3dCQUM5QzVCLFdBQVc7NEJBQ1Q7Z0NBQ0VrQixJQUFJO2dDQUNKaEIsUUFBUXVHLFdBQVc1RSxjQUFjLElBQUk7Z0NBQ3JDMUIsT0FBT3NHLFdBQVczRSxhQUFhLElBQUk7Z0NBQ25DMUIsVUFBVXFHLFdBQVdyRyxRQUFRO2dDQUM3QkMsT0FBT29HLFdBQVcxRSxJQUFJOzRCQUN4Qjt5QkFDRDtvQkFDSDtZQUNGLE9BQU87Z0JBQ0xnRCxRQUFRaEcsS0FBSyxDQUFDLGtDQUFrQ21DO1lBQ2xEO1FBQ0YsRUFBRSxPQUFPbkMsT0FBTztZQUNkZ0csUUFBUWhHLEtBQUssQ0FBQyw0Q0FBeUNBO1FBQ3pEO1FBRUFWLGVBQWU7UUFDZlosUUFBUTtJQUNWO0lBRUEsNENBQTRDLEdBQzVDLE1BQU1pSixzQkFBc0I7WUFxQlJwSCxzQkFDREEsdUJBQ0xBLHVCQUNKQTtRQXZCUixJQUFJLENBQUNoQyxhQUFhO1FBQ2xCLE1BQU0rRCxRQUFRLEdBQWtDL0IsT0FBL0JBLFNBQVNNLGdCQUFnQixFQUFDLE9BQStCLE9BQTFCTixTQUFTTyxnQkFBZ0I7UUFDekUsTUFBTW9GLFlBQW9CO1lBQ3hCL0QsSUFBSTVELFlBQVk0RCxFQUFFO1lBRWxCLG1EQUFtRDtZQUNuRGhDLG1CQUFtQkksU0FBU0osaUJBQWlCO1lBQzdDQyxzQkFBc0JHLFNBQVNILG9CQUFvQjtZQUNuREMscUJBQXFCRSxTQUFTRixtQkFBbUI7WUFDakRDLGtCQUFrQkMsU0FBU0QsZ0JBQWdCO1lBRTNDLCtDQUErQztZQUMvQ29DLGFBQWFuQyxTQUFTRyxrQkFBa0I7WUFDeENpQyxhQUFhcEMsU0FBU0ksa0JBQWtCO1lBQ3hDaUMsV0FBV3JDLFNBQVNLLGdCQUFnQjtZQUNwQ0Msa0JBQWtCTixTQUFTTSxnQkFBZ0I7WUFDM0N3QixXQUFXOUIsU0FBU08sZ0JBQWdCO1lBQ3BDK0IsZUFBZXRDLFNBQVNRLG9CQUFvQjtZQUU1QyxxREFBcUQ7WUFDckQrQixnQkFBZ0J2QyxFQUFBQSx1QkFBQUEsU0FBU1UsU0FBUyxDQUFDLEVBQUUsY0FBckJWLDJDQUFBQSxxQkFBdUJZLE1BQU0sS0FBSTtZQUNqRDRCLGVBQWV4QyxFQUFBQSx3QkFBQUEsU0FBU1UsU0FBUyxDQUFDLEVBQUUsY0FBckJWLDRDQUFBQSxzQkFBdUJhLEtBQUssS0FBSTtZQUMvQ0MsVUFBVWQsRUFBQUEsd0JBQUFBLFNBQVNVLFNBQVMsQ0FBQyxFQUFFLGNBQXJCViw0Q0FBQUEsc0JBQXVCYyxRQUFRLEtBQUk7WUFDN0MyQixNQUFNekMsRUFBQUEsd0JBQUFBLFNBQVNVLFNBQVMsQ0FBQyxFQUFFLGNBQXJCViw0Q0FBQUEsc0JBQXVCZSxLQUFLLEtBQUk7WUFFdEMsOEJBQThCO1lBQzlCZ0IsT0FBT0E7WUFDUDZELFdBQVc1RixTQUFTSCxvQkFBb0I7UUFDMUM7UUFFQSxJQUFJO1lBQ0YsTUFBTWdHLE1BQU0sTUFBTUMsTUFBTyx3Q0FBdUM7Z0JBQzlEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ1I7WUFDdkI7WUFFQSxJQUFJLENBQUNFLElBQUlPLEVBQUUsRUFBRTtnQkFDWCxNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxpQ0FBaUM7WUFDakMsTUFBTWdCLGNBQWNqSixLQUFLa0osR0FBRyxDQUFDLENBQUMzRjtnQkFDNUIsSUFBSUEsSUFBSUMsRUFBRSxLQUFLK0QsVUFBVS9ELEVBQUUsRUFBRTtvQkFDM0IsT0FBTytEO2dCQUNUO2dCQUNBLE9BQU9oRTtZQUNUO1lBRUF0RCxRQUFRZ0o7WUFDUm5FO1lBQ0EvRSxRQUFRO1FBQ1YsRUFBRSxPQUFPc0IsT0FBTztZQUNkZ0csUUFBUWhHLEtBQUssQ0FBQywwQkFBMEJBO1FBQzFDO0lBQ0Y7SUFFQSxNQUFNOEgseUJBQXlCLENBQzdCWjtRQUVBLElBQUlBLE1BQU1hLEdBQUcsS0FBSyxXQUFXbEksbUJBQW1CbUksT0FBTyxFQUFFO1lBQ3ZEbkksbUJBQW1CbUksT0FBTyxDQUFDQyxLQUFLO1FBQ2xDO0lBQ0Y7SUFFQSxNQUFNQyx3QkFBd0IsQ0FDNUJoQjtRQUVBckQsa0JBQWtCcUQ7UUFDbEJpQixXQUFXO1lBQ1QsSUFBSXJJLGlCQUFpQmtJLE9BQU8sRUFBRTtnQkFDNUJsSSxpQkFBaUJrSSxPQUFPLENBQUNDLEtBQUs7WUFDaEM7UUFDRixHQUFHO0lBQ0w7SUFFQSxNQUFNRyxzQkFBc0IsQ0FDMUJsQjtRQUVBckQsa0JBQWtCcUQ7UUFDbEJpQixXQUFXO1lBQ1QsSUFBSXBJLGFBQWFpSSxPQUFPLEVBQUU7Z0JBQ3hCakksYUFBYWlJLE9BQU8sQ0FBQ0MsS0FBSztZQUM1QjtRQUNGLEdBQUc7SUFDTDtJQUVBLE1BQU1JLHlCQUF5QixDQUFDbEc7UUFDOUIsTUFBTW1HLHFCQUFxQjNKLEtBQUtzRCxJQUFJLENBQUMsQ0FBQ0MsTUFBUUEsSUFBSUMsRUFBRSxLQUFLQTtRQUV6RCxJQUFJbUcsb0JBQW9CO1lBQ3RCLE1BQU1aLGFBQWE7Z0JBQ2pCdkYsSUFBSW1HLG1CQUFtQm5HLEVBQUU7Z0JBQ3pCTyxhQUFhNEYsbUJBQW1CNUYsV0FBVztZQUM3QztZQUVBLHFDQUFxQztZQUNyQzZGLGFBQWFDLE9BQU8sQ0FBQyxzQkFBc0IvQixLQUFLQyxTQUFTLENBQUNnQjtZQUUxRCxxREFBcUQ7WUFDckRlLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1FBQ3pCO0lBQ0Y7SUFFQSxNQUFNLENBQUNDLFlBQVl4QixjQUFjLEdBQUdoTCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN5TSxNQUFNQyxRQUFRLEdBQUcxTSwrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNMk0scUJBQXFCLENBQUM3QjtRQUMxQkUsY0FBY0YsTUFBTWxELE1BQU0sQ0FBQ1osS0FBSztRQUNoQzBGLFFBQVE7SUFDVjtJQUVBLE1BQU1FLGNBQWM7UUFDbEJ0RCxZQUFZO1FBQ1pDLE9BQU87UUFDUHNELGNBQWM7UUFDZEMsU0FBUztRQUNUQyxZQUFZO0lBQ2Q7SUFFQSxxQkFDRTs7MEJBQ0UsOERBQUN0TCxtREFBR0E7Z0JBQ0YwSCxJQUFJO29CQUNGMkQsU0FBUztvQkFDVEUsZ0JBQWdCO29CQUNoQkMsSUFBSTtvQkFDSkMsSUFBSTtnQkFDTjs7a0NBRUEsOERBQUN6TCxtREFBR0E7OzBDQUNGLDhEQUFDSCxzRkFBVUE7Z0NBQ1QrSCxTQUFRO2dDQUNSOEQsV0FBVTtnQ0FDVmhFLElBQUk7b0NBQ0ZHLFlBQVk7b0NBQ1p5RCxZQUFZO2dDQUNkOzBDQUNEOzs7Ozs7MENBR0QsOERBQUN6TCxzRkFBVUE7Z0NBQ1QrSCxTQUFRO2dDQUNSRixJQUFJO29DQUNGSSxPQUFPO29DQUNQMkQsSUFBSTtvQ0FDSkgsWUFBWSxHQUEwQixPQUF2QmpOLDRLQUFXLENBQUNpTixVQUFVO2dDQUN2QzswQ0FDRDs7Ozs7Ozs7Ozs7O2tDQUlILDhEQUFDek0scUhBQU1BO3dCQUNMK0ksU0FBUTt3QkFDUmdFLFNBQVNqRzt3QkFDVCtCLElBQUk7NEJBQ0ZtRSxTQUFTOzRCQUNUL0QsT0FBTzs0QkFDUCxXQUFXO2dDQUFFK0QsU0FBUzs0QkFBVTs0QkFDaENDLFFBQVE7NEJBQ1JDLFdBQVc7NEJBQ1hULFlBQVksR0FBMEIsT0FBdkJqTiw0S0FBVyxDQUFDaU4sVUFBVTt3QkFDdkM7d0JBQ0FVLHlCQUFXLDhEQUFDdE4sdUVBQWVBOzs7OztrQ0FDNUI7Ozs7Ozs7Ozs7OzswQkFLSCw4REFBQ00scUhBQUtBO2dCQUFDaU4sV0FBVztnQkFBR3ZFLElBQUk7b0JBQUV3RSxHQUFHO29CQUFHVixJQUFJO29CQUFHVyxjQUFjO2dCQUFFOztrQ0FDdEQsOERBQUN2TSxzRkFBU0E7d0JBQ1J3TSxTQUFTO3dCQUNUeEUsU0FBUTt3QkFDUnlFLGFBQVk7d0JBQ1o5RyxPQUFPd0Y7d0JBQ1B1QixVQUFVbEQ7d0JBQ1ZtRCxZQUFZOzRCQUNWQyw4QkFDRSw4REFBQ3pOLHNIQUFjQTtnQ0FBQzBOLFVBQVM7MENBQ3ZCLDRFQUFDaE4sdUZBQVVBO29DQUFDbU0sU0FBUzFFOzhDQUNuQiw0RUFBQzlHLHlGQUFVQTs7Ozs7Ozs7Ozs7Ozs7O3dCQUluQjt3QkFDQXNILElBQUk7NEJBQUU4RCxJQUFJO3dCQUFFOzs7Ozs7a0NBRWQsOERBQUMxTCxtRUFBU0E7d0JBQ1JxSCxTQUFTQTt3QkFDVHJHLE1BQU1NLGFBQWFpQyxNQUFNLEdBQUcsSUFBSWpDLGVBQWVOO3dCQUMvQzRMLFFBQVE7d0JBQ1JDLHNCQUFzQmhEO3dCQUN0QmlELHNCQUFzQmhEO3dCQUN0QmpKLGdCQUFnQixDQUFDMEQsTUFBUTFELGVBQWUwRDt3QkFDeEMzRCxhQUFhQTt3QkFDYm1NLGNBQWNyQzs7Ozs7Ozs7Ozs7OzBCQUlsQiw4REFBQ3JMLG1GQUFNQTtnQkFDTHlCLE1BQU1BO2dCQUNOa00sU0FBU2pIO2dCQUNUa0gsVUFBUztnQkFDVFgsU0FBUztnQkFDVDFFLElBQUk7b0JBQ0Ysc0JBQXNCO3dCQUNwQkosT0FBTzt3QkFDUHlGLFVBQVU7d0JBQ1ZDLFdBQVc7b0JBQ2I7Z0JBQ0Y7MEJBRUEsNEVBQUNoTixtREFBR0E7b0JBQUMwSCxJQUFJO3dCQUFFd0UsR0FBRztvQkFBRTs7c0NBRWQsOERBQUNsTSxtREFBR0E7NEJBQUMwSCxJQUFJO2dDQUFFOEQsSUFBSTs0QkFBRTs7OENBQ2YsOERBQUNuTSx3RkFBV0E7b0NBQ1ZxSSxJQUFJO3dDQUNGd0UsR0FBRzt3Q0FDSFosWUFBWTt3Q0FDWnZELFVBQVU7d0NBQ1ZGLFlBQVk7d0NBQ1pDLE9BQU87b0NBQ1Q7OENBQ0Q7Ozs7Ozs4Q0FHRCw4REFBQ3hJLDhGQUFpQkE7b0NBQ2hCb0ksSUFBSTt3Q0FDRndFLEdBQUc7d0NBQ0hULElBQUk7d0NBQ0pILFlBQVk7d0NBQ1p4RCxPQUFPO29DQUNUOzhDQUNEOzs7Ozs7Ozs7Ozs7c0NBSUgsOERBQUNySSx1RkFBVUE7NEJBQ1R3TixjQUFXOzRCQUNYckIsU0FBUyxDQUFDdkMsUUFBVXhELGlCQUFpQndELE9BQU87NEJBQzVDM0IsSUFBSTtnQ0FBRStFLFVBQVU7Z0NBQVlTLE9BQU87Z0NBQUdDLEtBQUs7NEJBQUU7c0NBRTdDLDRFQUFDcE4sa0VBQVNBOzs7Ozs7Ozs7O3NDQUVaLDhEQUFDQyxtREFBR0E7NEJBQ0YwTCxXQUFVOzRCQUNWMEIsVUFBVXBHOzRCQUNWcUcsV0FBV2hQLGdMQUFlO3NDQUUxQiw0RUFBQ2UsMEZBQWFBO2dDQUFDc0ksSUFBSTtvQ0FBRXdFLEdBQUc7Z0NBQUU7O2tEQUV4Qiw4REFBQ2xNLG1EQUFHQTt3Q0FBQzBILElBQUk7NENBQUU0RixjQUFjOzRDQUFHQyxhQUFhOzRDQUFXL0IsSUFBSTt3Q0FBRTtrREFDeEQsNEVBQUN2TSxzSEFBSUE7NENBQ0hzRyxPQUFPM0Q7NENBQ1AwSyxVQUFVLENBQUNqRCxPQUFPbUU7Z0RBQ2hCLCtDQUErQztnREFDL0MsSUFBSTFMLFdBQVcsQ0FBQzBMLFNBQVMsRUFBRTtvREFDekIzTCxZQUFZMkw7Z0RBQ2Q7NENBQ0Y7NENBQ0E5RixJQUFJO2dEQUNGLGtCQUFrQjtvREFDaEI0RCxZQUFZO29EQUNaekQsWUFBWTtvREFDWjRGLGVBQWU7b0RBQ2YxRixVQUFVO2dEQUNaOzRDQUNGOzs4REFFQSw4REFBQzdJLHNIQUFHQTtvREFDRndPLE9BQU07b0RBQ05oRyxJQUFJO3dEQUFFaUcsVUFBVTtvREFBSTtvREFDcEJDLFVBQVUsQ0FBQzlMLFdBQVcsQ0FBQyxFQUFFOzs7Ozs7OERBRTNCLDhEQUFDNUMsc0hBQUdBO29EQUNGd08sT0FBTTtvREFDTmhHLElBQUk7d0RBQUVpRyxVQUFVO29EQUFJO29EQUNwQkMsVUFBVSxDQUFDOUwsV0FBVyxDQUFDLEVBQUU7Ozs7Ozs4REFFM0IsOERBQUM1QyxzSEFBR0E7b0RBQ0Z3TyxPQUFNO29EQUNOaEcsSUFBSTt3REFBRWlHLFVBQVU7b0RBQUk7b0RBQ3BCQyxVQUFVLENBQUM5TCxXQUFXLENBQUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBTTlCRixhQUFhLG1CQUNaLDhEQUFDNUIsbURBQUdBO3dDQUFDMEgsSUFBSTs0Q0FBRStELElBQUk7d0NBQUU7OzBEQUNmLDhEQUFDNUwsc0ZBQVVBO2dEQUNUK0gsU0FBUTtnREFDUkYsSUFBSTtvREFDRjhELElBQUk7b0RBQ0pGLFlBQVk7b0RBQ1p4RCxPQUFPO2dEQUNUOzBEQUNEOzs7Ozs7MERBR0QsOERBQUNoSixzSEFBSUE7Z0RBQUMrTyxTQUFTO2dEQUFDQyxTQUFTOztrRUFFdkIsOERBQUNoUCxzSEFBSUE7d0RBQUNpUCxNQUFNOzREQUFFQyxJQUFJOzREQUFJQyxJQUFJO3dEQUFFOzswRUFDMUIsOERBQUNwTyxzRkFBVUE7Z0VBQUMrSCxTQUFRO2dFQUFRRixJQUFJeUQ7MEVBQWE7Ozs7OzswRUFHN0MsOERBQUN2TCxzRkFBU0E7Z0VBQ1J5TSxhQUFZO2dFQUNaekUsU0FBUTtnRUFDUjFCLE1BQUs7Z0VBQ0xnSSxNQUFLO2dFQUNMQyxRQUFRO2dFQUNSL0IsU0FBUztnRUFDVGpLLE9BQU9pTSxRQUFRak0sTUFBTUcsaUJBQWlCO2dFQUN0QytMLFlBQVlsTSxNQUFNRyxpQkFBaUI7Z0VBQ25DZ0ssVUFBVXRHO2dFQUNWVCxPQUFPN0MsU0FBU0osaUJBQWlCO2dFQUNqQ2lLLFlBQVk7b0VBQ1YrQiw0QkFDRSw4REFBQ3ZQLHNIQUFjQTt3RUFBQzBOLFVBQVM7a0ZBQ3RCL0osU0FBU0osaUJBQWlCLElBQ3hCSCxDQUFBQSxNQUFNRyxpQkFBaUIsaUJBQ3RCLDhEQUFDcEMsa0VBQVNBOzRFQUFDNEgsT0FBTTs7Ozs7bUdBRWpCLDhEQUFDN0gsd0VBQWVBOzRFQUFDNkgsT0FBTTs7Ozs7a0ZBQ3pCOzs7Ozs7Z0VBR1I7Ozs7Ozs7Ozs7OztrRUFLSiw4REFBQ2hKLHNIQUFJQTt3REFBQ2lQLE1BQU07NERBQUVDLElBQUk7NERBQUlDLElBQUk7d0RBQUU7OzBFQUMxQiw4REFBQ3BPLHNGQUFVQTtnRUFBQytILFNBQVE7Z0VBQVFGLElBQUl5RDswRUFBYTs7Ozs7OzBFQUc3Qyw4REFBQ3ZMLHNGQUFTQTtnRUFDUnlNLGFBQVk7Z0VBQ1p6RSxTQUFRO2dFQUNSMUIsTUFBSztnRUFDTGdJLE1BQUs7Z0VBQ0xDLFFBQVE7Z0VBQ1IvQixTQUFTO2dFQUNUakssT0FBT2lNLFFBQVFqTSxNQUFNSSxvQkFBb0I7Z0VBQ3pDOEwsWUFBWWxNLE1BQU1JLG9CQUFvQjtnRUFDdEMrSixVQUFVdEc7Z0VBQ1ZULE9BQU83QyxTQUFTSCxvQkFBb0I7Z0VBQ3BDZ0ssWUFBWTtvRUFDVitCLDRCQUNFLDhEQUFDdlAsc0hBQWNBO3dFQUFDME4sVUFBUztrRkFDdEIvSixTQUFTSCxvQkFBb0IsSUFDM0JKLENBQUFBLE1BQU1JLG9CQUFvQixpQkFDekIsOERBQUNyQyxrRUFBU0E7NEVBQUM0SCxPQUFNOzs7OzttR0FFakIsOERBQUM3SCx3RUFBZUE7NEVBQUM2SCxPQUFNOzs7OztrRkFDekI7Ozs7OztnRUFHUjs7Ozs7Ozs7Ozs7O2tFQUtKLDhEQUFDaEosc0hBQUlBO3dEQUFDaVAsTUFBTTs0REFBRUMsSUFBSTs0REFBSUMsSUFBSTt3REFBRTs7MEVBQzFCLDhEQUFDcE8sc0ZBQVVBO2dFQUFDK0gsU0FBUTtnRUFBUUYsSUFBSXlEOzBFQUFhOzs7Ozs7MEVBRzdDLDhEQUFDdkwsc0ZBQVNBO2dFQUNSeU0sYUFBWTtnRUFDWnpFLFNBQVE7Z0VBQ1IxQixNQUFLO2dFQUNMZ0ksTUFBSztnRUFDTDlCLFNBQVM7Z0VBQ1RqSyxPQUFPaU0sUUFBUWpNLE1BQU1LLG1CQUFtQjtnRUFDeEM2TCxZQUFZbE0sTUFBTUssbUJBQW1CO2dFQUNyQzhKLFVBQVV0RztnRUFDVlQsT0FBTzdDLFNBQVNGLG1CQUFtQjtnRUFDbkMrSixZQUFZO29FQUNWK0IsNEJBQ0UsOERBQUN2UCxzSEFBY0E7d0VBQUMwTixVQUFTO2tGQUN0Qi9KLFNBQVNGLG1CQUFtQixJQUMxQkwsQ0FBQUEsTUFBTUssbUJBQW1CLGlCQUN4Qiw4REFBQ3RDLGtFQUFTQTs0RUFBQzRILE9BQU07Ozs7O21HQUVqQiw4REFBQzdILHdFQUFlQTs0RUFBQzZILE9BQU07Ozs7O2tGQUN6Qjs7Ozs7O2dFQUdSOzs7Ozs7Ozs7Ozs7a0VBS0osOERBQUNoSixzSEFBSUE7d0RBQUNpUCxNQUFNOzREQUFFQyxJQUFJOzREQUFJQyxJQUFJO3dEQUFFOzswRUFDMUIsOERBQUNwTyxzRkFBVUE7Z0VBQUMrSCxTQUFRO2dFQUFRRixJQUFJeUQ7MEVBQWE7Ozs7OzswRUFHN0MsOERBQUN2TCxzRkFBU0E7Z0VBQ1J5TSxhQUFZO2dFQUNaekUsU0FBUTtnRUFDUjFCLE1BQUs7Z0VBQ0xnSSxNQUFLO2dFQUNMOUIsU0FBUztnRUFDVGpLLE9BQU9pTSxRQUFRak0sTUFBTU0sZ0JBQWdCO2dFQUNyQzRMLFlBQVlsTSxNQUFNTSxnQkFBZ0I7Z0VBQ2xDNkosVUFBVXRHO2dFQUNWVCxPQUFPN0MsU0FBU0QsZ0JBQWdCO2dFQUNoQzhKLFlBQVk7b0VBQ1YrQiw0QkFDRSw4REFBQ3ZQLHNIQUFjQTt3RUFBQzBOLFVBQVM7a0ZBQ3RCL0osU0FBU0QsZ0JBQWdCLElBQ3ZCTixDQUFBQSxNQUFNTSxnQkFBZ0IsaUJBQ3JCLDhEQUFDdkMsa0VBQVNBOzRFQUFDNEgsT0FBTTs7Ozs7bUdBRWpCLDhEQUFDN0gsd0VBQWVBOzRFQUFDNkgsT0FBTTs7Ozs7a0ZBQ3pCOzs7Ozs7Z0VBR1I7Ozs7Ozs7Ozs7OztrRUFLSiw4REFBQ2hKLHNIQUFJQTt3REFBQ2lQLE1BQU07NERBQUVDLElBQUk7d0RBQUc7a0VBQ25CLDRFQUFDaE8sbURBQUdBOzREQUNGMEgsSUFBSTtnRUFDRitELElBQUk7Z0VBQ0pTLEdBQUc7Z0VBQ0hMLFNBQVM7Z0VBQ1RNLGNBQWM7NERBQ2hCOzs4RUFFQSw4REFBQ3VCO29FQUNDL0IsT0FBTzt3RUFDTE4sU0FBUzt3RUFDVGtELFlBQVk7d0VBQ1pDLFFBQVE7b0VBQ1Y7O3NGQUVBLDhEQUFDQzs0RUFDQ1AsTUFBSzs0RUFDTFEsU0FBU2hNLFNBQVMwQyxtQkFBbUI7NEVBQ3JDa0gsVUFBVSxDQUFDckc7Z0ZBQ1QsTUFBTXlJLFVBQVV6SSxFQUFFRSxNQUFNLENBQUN1SSxPQUFPO2dGQUNoQy9KLFlBQVksQ0FBQ2dLO3dGQWdCREE7MkZBaEJXO3dGQUNyQixHQUFHQSxJQUFJO3dGQUNQdkoscUJBQXFCc0o7d0ZBQ3JCLHlEQUF5RDt3RkFDekQ3TCxvQkFBb0I2TCxVQUNoQkMsS0FBS3JNLGlCQUFpQixHQUN0Qjt3RkFDSixrRUFBa0U7d0ZBQ2xFYyxXQUFXc0wsVUFDUDs0RkFDRTtnR0FDRSxHQUFHQyxLQUFLdkwsU0FBUyxDQUFDLEVBQUU7Z0dBQ3BCRSxRQUFRcUwsS0FBS3JNLGlCQUFpQjtnR0FDOUJrQixVQUFVbUwsS0FBS25NLG1CQUFtQjtnR0FDbENpQixPQUFPa0wsS0FBS2xNLGdCQUFnQjtnR0FDNUJjLE9BQ0VvTCxFQUFBQSxtQkFBQUEsS0FBS3ZMLFNBQVMsQ0FBQyxFQUFFLGNBQWpCdUwsdUNBQUFBLGlCQUFtQnBMLEtBQUssS0FDeEI7NEZBQ0o7K0ZBQ0dvTCxLQUFLdkwsU0FBUyxDQUFDdUQsS0FBSyxDQUFDO3lGQUN6QixHQUNEZ0ksS0FBS3ZMLFNBQVM7b0ZBQ3BCOzs0RUFDRjs0RUFDQXVJLE9BQU87Z0ZBQUVpRCxhQUFhOzRFQUFNOzs7Ozs7c0ZBRTlCLDhEQUFDL08sc0ZBQVVBOzRFQUNUK0gsU0FBUTs0RUFDUkYsSUFBSTtnRkFBRTRELFlBQVk7NEVBQW9CO3NGQUN2Qzs7Ozs7Ozs7Ozs7O2dFQUtGNUksU0FBUzBDLG1CQUFtQixrQkFDM0IsOERBQUN2RixzRkFBVUE7b0VBQ1QrSCxTQUFRO29FQUNSRixJQUFJO3dFQUNGSSxPQUFPO3dFQUNQMkQsSUFBSTt3RUFDSkosU0FBUzt3RUFDVHBELFdBQVc7b0VBQ2I7OEVBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQVdULDhEQUFDakksbURBQUdBO2dEQUNGMEgsSUFBSTtvREFDRjJELFNBQVM7b0RBQ1RFLGdCQUFnQjtvREFDaEJFLElBQUk7b0RBQ0pvRCxJQUFJO29EQUNKQyxXQUFXO2dEQUNiOztrRUFFQSw4REFBQ2pRLHFIQUFNQTt3REFDTCtJLFNBQVE7d0RBQ1JnRSxTQUFTLENBQUN2QyxRQUNSeEQsaUJBQWlCd0QsT0FBTzt3REFFMUIzQixJQUFJOzREQUFFaUcsVUFBVTt3REFBSTtrRUFDckI7Ozs7OztrRUFHRCw4REFBQzlPLHFIQUFNQTt3REFDTCtJLFNBQVE7d0RBQ1JnRSxTQUFTL0g7d0RBQ1Q2RCxJQUFJOzREQUNGaUcsVUFBVTs0REFDVjlCLFNBQVM7NERBQ1QsV0FBVztnRUFBRUEsU0FBUzs0REFBVTt3REFDbEM7a0VBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FRTmpLLGFBQWEsbUJBQ1osOERBQUM1QixtREFBR0E7d0NBQUMwSCxJQUFJOzRDQUFFK0QsSUFBSTt3Q0FBRTs7MERBQ2YsOERBQUM1TCxzRkFBVUE7Z0RBQ1QrSCxTQUFRO2dEQUNSRixJQUFJO29EQUNGOEQsSUFBSTtvREFDSkYsWUFBWTtvREFDWnhELE9BQU87Z0RBQ1Q7MERBQ0Q7Ozs7OzswREFHRCw4REFBQ2hKLHNIQUFJQTtnREFBQytPLFNBQVM7Z0RBQUNDLFNBQVM7O2tFQUV2Qiw4REFBQ2hQLHNIQUFJQTt3REFBQ2lQLE1BQU07NERBQUVDLElBQUk7NERBQUlDLElBQUk7d0RBQUU7OzBFQUMxQiw4REFBQ3BPLHNGQUFVQTtnRUFBQytILFNBQVE7Z0VBQVFGLElBQUl5RDswRUFBYTs7Ozs7OzBFQUc3Qyw4REFBQ3ZMLHNGQUFTQTtnRUFDUnlNLGFBQVk7Z0VBQ1p6RSxTQUFRO2dFQUNSMUIsTUFBSztnRUFDTGdJLE1BQUs7Z0VBQ0xDLFFBQVE7Z0VBQ1IvQixTQUFTO2dFQUNUakssT0FBT2lNLFFBQVFqTSxNQUFNVSxrQkFBa0I7Z0VBQ3ZDd0wsWUFBWWxNLE1BQU1VLGtCQUFrQjtnRUFDcEN5SixVQUFVdEc7Z0VBQ1ZULE9BQU83QyxTQUFTRyxrQkFBa0I7Z0VBQ2xDK0ssVUFBVWxMLFNBQVMwQyxtQkFBbUI7Z0VBQ3RDbUgsWUFBWTtvRUFDVitCLDRCQUNFLDhEQUFDdlAsc0hBQWNBO3dFQUFDME4sVUFBUztrRkFDdEIvSixTQUFTRyxrQkFBa0IsSUFDekJWLENBQUFBLE1BQU1VLGtCQUFrQixpQkFDdkIsOERBQUMzQyxrRUFBU0E7NEVBQUM0SCxPQUFNOzs7OzttR0FFakIsOERBQUM3SCx3RUFBZUE7NEVBQUM2SCxPQUFNOzs7OztrRkFDekI7Ozs7OztnRUFHUjtnRUFDQUosSUFBSTtvRUFDRix5QkFBeUI7d0VBQ3ZCcUgsaUJBQWlCck0sU0FBUzBDLG1CQUFtQixHQUN6QyxZQUNBO29FQUNOO2dFQUNGOzs7Ozs7NERBRUQxQyxTQUFTMEMsbUJBQW1CLGtCQUMzQiw4REFBQ3ZGLHNGQUFVQTtnRUFDVCtILFNBQVE7Z0VBQ1JGLElBQUk7b0VBQUVJLE9BQU87b0VBQVEyRCxJQUFJO29FQUFHSixTQUFTO2dFQUFROzBFQUM5Qzs7Ozs7Ozs7Ozs7O2tFQU9MLDhEQUFDdk0sc0hBQUlBO3dEQUFDaVAsTUFBTTs0REFBRUMsSUFBSTs0REFBSUMsSUFBSTt3REFBRTs7MEVBQzFCLDhEQUFDcE8sc0ZBQVVBO2dFQUFDK0gsU0FBUTtnRUFBUUYsSUFBSXlEOzBFQUFhOzs7Ozs7MEVBRzdDLDhEQUFDNUwsd0ZBQVdBO2dFQUNWNk0sU0FBUztnRUFDVGpLLE9BQU9pTSxRQUFRak0sTUFBTVcsa0JBQWtCOztrRkFFdkMsOERBQUNuRCxtRkFBTUE7d0VBQ0x1RyxNQUFLO3dFQUNMWCxPQUFPN0MsU0FBU0ksa0JBQWtCO3dFQUNsQ3dKLFVBQVUsQ0FBQ2pEOzRFQUNULE1BQU0yRixpQkFBaUI7Z0ZBQ3JCN0ksUUFBUTtvRkFDTkQsTUFBTTtvRkFDTlgsT0FBTzhELE1BQU1sRCxNQUFNLENBQUNaLEtBQUs7Z0ZBQzNCOzRFQUNGOzRFQUNBUyxrQkFBa0JnSjt3RUFDcEI7d0VBQ0FDLFlBQVk7d0VBQ1pDLGFBQWEsQ0FBQ0M7NEVBQ1osSUFBSSxDQUFDQSxVQUFVO2dGQUNiLHFCQUNFLDhEQUFDQztvRkFBS3pELE9BQU87d0ZBQUU3RCxPQUFPO29GQUFPOzhGQUFHOzs7Ozs7NEVBSXBDOzRFQUNBLE1BQU00RSxTQUFTcEgsbUJBQW1CbEIsSUFBSSxDQUNwQyxDQUFDaUwsTUFBUUEsSUFBSTlKLEtBQUssS0FBSzRKOzRFQUV6QixPQUFPekMsU0FDSCxHQUFrQkEsT0FBZkEsT0FBT2pILElBQUksRUFBQyxLQUFnQixPQUFiaUgsT0FBT25ILEtBQUssSUFDOUI0Sjt3RUFDTjt3RUFDQXpILElBQUk7NEVBQ0ZvRSxRQUFROzRFQUNSLHVCQUF1QjtnRkFDckJ3RCxTQUFTO2dGQUNUeEQsUUFBUTtnRkFDUlQsU0FBUztnRkFDVGtELFlBQVk7NEVBQ2Q7d0VBQ0Y7OzBGQUVBLDhEQUFDN08scUZBQVFBO2dGQUFDNkYsT0FBTTtnRkFBR3FJLFFBQVE7MEZBQUM7Ozs7Ozs0RUFHM0J0SSxtQkFBbUIwRSxHQUFHLENBQUMsQ0FBQzBDLHVCQUN2Qiw4REFBQ2hOLHFGQUFRQTtvRkFBb0I2RixPQUFPbUgsT0FBT25ILEtBQUs7OEZBQzlDLDRFQUFDdkYsbURBQUdBO3dGQUNGMEgsSUFBSTs0RkFDRjJELFNBQVM7NEZBQ1RrRCxZQUFZOzRGQUNaZ0IsS0FBSzt3RkFDUDs7MEdBRUEsOERBQUNIOzBHQUFNMUMsT0FBT2pILElBQUk7Ozs7OzswR0FDbEIsOERBQUMySjswR0FBTTFDLE9BQU9uSCxLQUFLOzs7Ozs7Ozs7Ozs7bUZBVFJtSCxPQUFPbkgsS0FBSzs7Ozs7Ozs7Ozs7b0VBYzlCcEQsTUFBTVcsa0JBQWtCLGtCQUN2Qiw4REFBQ3RELDJGQUFjQTtrRkFDWjJDLE1BQU1XLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU9qQyw4REFBQ2hFLHNIQUFJQTt3REFBQ2lQLE1BQU07NERBQUVDLElBQUk7d0RBQUc7OzBFQUNuQiw4REFBQ25PLHNGQUFVQTtnRUFBQytILFNBQVE7Z0VBQVFGLElBQUl5RDswRUFBYTs7Ozs7OzBFQUc3Qyw4REFBQ3ZMLHNGQUFTQTtnRUFDUnlNLGFBQVk7Z0VBQ1p6RSxTQUFRO2dFQUNSMUIsTUFBSztnRUFDTGdJLE1BQUs7Z0VBQ0w5QixTQUFTO2dFQUNUakssT0FBT2lNLFFBQVFqTSxNQUFNWSxnQkFBZ0I7Z0VBQ3JDc0wsWUFBWWxNLE1BQU1ZLGdCQUFnQjtnRUFDbEN1SixVQUFVdEc7Z0VBQ1ZULE9BQU83QyxTQUFTSyxnQkFBZ0I7Z0VBQ2hDd0osWUFBWTtvRUFDVitCLDRCQUNFLDhEQUFDdlAsc0hBQWNBO3dFQUFDME4sVUFBUztrRkFDdEIvSixTQUFTSyxnQkFBZ0IsSUFDdkJaLENBQUFBLE1BQU1ZLGdCQUFnQixpQkFDckIsOERBQUM3QyxrRUFBU0E7NEVBQUM0SCxPQUFNOzs7OzttR0FFakIsOERBQUM3SCx3RUFBZUE7NEVBQUM2SCxPQUFNOzs7OztrRkFDekI7Ozs7OztnRUFHUjs7Ozs7Ozs7Ozs7O2tFQUtKLDhEQUFDaEosc0hBQUlBO3dEQUFDaVAsTUFBTTs0REFBRUMsSUFBSTs0REFBSUMsSUFBSTt3REFBRTs7MEVBQzFCLDhEQUFDcE8sc0ZBQVVBO2dFQUFDK0gsU0FBUTtnRUFBUUYsSUFBSXlEOzBFQUFhOzs7Ozs7MEVBRzdDLDhEQUFDdkwsc0ZBQVNBO2dFQUNSeU0sYUFBWTtnRUFDWnpFLFNBQVE7Z0VBQ1IxQixNQUFLO2dFQUNMZ0ksTUFBSztnRUFDTEMsUUFBUTtnRUFDUi9CLFNBQVM7Z0VBQ1RqSyxPQUFPaU0sUUFBUWpNLE1BQU1hLGdCQUFnQjtnRUFDckNxTCxZQUFZbE0sTUFBTWEsZ0JBQWdCO2dFQUNsQ3NKLFVBQVV0RztnRUFDVlQsT0FBTzdDLFNBQVNNLGdCQUFnQjtnRUFDaEN1SixZQUFZO29FQUNWK0IsNEJBQ0UsOERBQUN2UCxzSEFBY0E7d0VBQUMwTixVQUFTO2tGQUN0Qi9KLFNBQVNNLGdCQUFnQixJQUN2QmIsQ0FBQUEsTUFBTWEsZ0JBQWdCLGlCQUNyQiw4REFBQzlDLGtFQUFTQTs0RUFBQzRILE9BQU07Ozs7O21HQUVqQiw4REFBQzdILHdFQUFlQTs0RUFBQzZILE9BQU07Ozs7O2tGQUN6Qjs7Ozs7O2dFQUdSOzs7Ozs7Ozs7Ozs7a0VBSUosOERBQUNoSixzSEFBSUE7d0RBQUNpUCxNQUFNOzREQUFFQyxJQUFJOzREQUFJQyxJQUFJO3dEQUFFOzswRUFDMUIsOERBQUNwTyxzRkFBVUE7Z0VBQUMrSCxTQUFRO2dFQUFRRixJQUFJeUQ7MEVBQWE7Ozs7OzswRUFHN0MsOERBQUM1TCx3RkFBV0E7Z0VBQ1Y2TSxTQUFTO2dFQUNUakssT0FBT2lNLFFBQVFqTSxNQUFNYyxnQkFBZ0I7O2tGQUVyQyw4REFBQ3RELG1GQUFNQTt3RUFDTHVHLE1BQUs7d0VBQ0xYLE9BQU83QyxTQUFTTyxnQkFBZ0I7d0VBQ2hDcUosVUFBVSxDQUFDakQ7NEVBQ1QsTUFBTTJGLGlCQUFpQjtnRkFDckI3SSxRQUFRO29GQUNORCxNQUFNO29GQUNOWCxPQUFPOEQsTUFBTWxELE1BQU0sQ0FBQ1osS0FBSztnRkFDM0I7NEVBQ0Y7NEVBQ0FTLGtCQUFrQmdKO3dFQUNwQjt3RUFDQUMsWUFBWTt3RUFDWkMsYUFBYSxDQUFDQzs0RUFDWixJQUFJLENBQUNBLFVBQVU7Z0ZBQ2IscUJBQ0UsOERBQUNDO29GQUFLekQsT0FBTzt3RkFBRTdELE9BQU87b0ZBQU87OEZBQUc7Ozs7Ozs0RUFJcEM7NEVBQ0EsT0FBT3FIO3dFQUNUO3dFQUNBekgsSUFBSTs0RUFDRm9FLFFBQVE7NEVBQ1IsdUJBQXVCO2dGQUNyQndELFNBQVM7Z0ZBQ1R4RCxRQUFRO2dGQUNSVCxTQUFTO2dGQUNUa0QsWUFBWTs0RUFDZDt3RUFDRjs7MEZBRUEsOERBQUM3TyxxRkFBUUE7Z0ZBQUM2RixPQUFNO2dGQUFHcUksUUFBUTswRkFBQzs7Ozs7OzRFQUczQnZJLFdBQVcyRSxHQUFHLENBQUMsQ0FBQ3hGLDBCQUNmLDhEQUFDOUUscUZBQVFBO29GQUFpQjZGLE9BQU9mOzhGQUM5QkE7bUZBRFlBOzs7Ozs7Ozs7OztvRUFLbEJyQyxNQUFNYyxnQkFBZ0Isa0JBQ3JCLDhEQUFDekQsMkZBQWNBO2tGQUNaMkMsTUFBTWMsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBTy9CLDhEQUFDbkUsc0hBQUlBO3dEQUFDaVAsTUFBTTs0REFBRUMsSUFBSTs0REFBSUMsSUFBSTt3REFBRTs7MEVBQzFCLDhEQUFDcE8sc0ZBQVVBO2dFQUFDK0gsU0FBUTtnRUFBUUYsSUFBSXlEOzBFQUFhOzs7Ozs7MEVBRzdDLDhEQUFDNUwsd0ZBQVdBO2dFQUNWNk0sU0FBUztnRUFDVGpLLE9BQU9pTSxRQUFRak0sTUFBTWUsb0JBQW9COztrRkFFekMsOERBQUN2RCxtRkFBTUE7d0VBQ0x1RyxNQUFLO3dFQUNMWCxPQUFPN0MsU0FBU1Esb0JBQW9CO3dFQUNwQ29KLFVBQVUsQ0FBQ2pEOzRFQUNULE1BQU0yRixpQkFBaUI7Z0ZBQ3JCN0ksUUFBUTtvRkFDTkQsTUFBTTtvRkFDTlgsT0FBTzhELE1BQU1sRCxNQUFNLENBQUNaLEtBQUs7Z0ZBQzNCOzRFQUNGOzRFQUNBUyxrQkFBa0JnSjt3RUFDcEI7d0VBQ0FDLFlBQVk7d0VBQ1pDLGFBQWEsQ0FBQ0M7NEVBQ1osSUFBSSxDQUFDQSxVQUFVO2dGQUNiLHFCQUNFLDhEQUFDQztvRkFBS3pELE9BQU87d0ZBQUU3RCxPQUFPO29GQUFPOzhGQUFHOzs7Ozs7NEVBSXBDOzRFQUNBLE9BQU9xSDt3RUFDVDt3RUFDQXpILElBQUk7NEVBQ0ZvRSxRQUFROzRFQUNSLHVCQUF1QjtnRkFDckJ3RCxTQUFTO2dGQUNUeEQsUUFBUTtnRkFDUlQsU0FBUztnRkFDVGtELFlBQVk7NEVBQ2Q7d0VBQ0Y7OzBGQUVBLDhEQUFDN08scUZBQVFBO2dGQUFDNkYsT0FBTTtnRkFBR3FJLFFBQVE7MEZBQUM7Ozs7Ozs0RUFHM0JsSSxxQkFBcUJzRSxHQUFHLENBQUMsQ0FBQ3dGLDBCQUN6Qiw4REFBQzlQLHFGQUFRQTtvRkFBaUI2RixPQUFPaUs7OEZBQzlCQTttRkFEWUE7Ozs7Ozs7Ozs7O29FQUtsQnJOLE1BQU1lLG9CQUFvQixrQkFDekIsOERBQUMxRCwyRkFBY0E7a0ZBQ1oyQyxNQUFNZSxvQkFBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFRckMsOERBQUNsRCxtREFBR0E7Z0RBQ0YwSCxJQUFJO29EQUNGMkQsU0FBUztvREFDVEUsZ0JBQWdCO29EQUNoQkUsSUFBSTtvREFDSm9ELElBQUk7b0RBQ0pDLFdBQVc7Z0RBQ2I7O2tFQUVBLDhEQUFDalEscUhBQU1BO3dEQUNMK0ksU0FBUTt3REFDUmdFLFNBQVM1SDt3REFDVDBELElBQUk7NERBQUVpRyxVQUFVO3dEQUFJO2tFQUNyQjs7Ozs7O2tFQUdELDhEQUFDOU8scUhBQU1BO3dEQUNMK0ksU0FBUTt3REFDUmdFLFNBQVMvSDt3REFDVDZELElBQUk7NERBQ0ZpRyxVQUFVOzREQUNWOUIsU0FBUzs0REFDVCxXQUFXO2dFQUFFQSxTQUFTOzREQUFVO3dEQUNsQztrRUFDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQVFOakssYUFBYSxtQkFDWiw4REFBQzVCLG1EQUFHQTt3Q0FBQzBILElBQUk7NENBQUUrRCxJQUFJO3dDQUFFOzswREFDZiw4REFBQzVMLHNGQUFVQTtnREFDVCtILFNBQVE7Z0RBQ1JGLElBQUk7b0RBQ0Y4RCxJQUFJO29EQUNKRixZQUFZO29EQUNaeEQsT0FBTztnREFDVDswREFDRDs7Ozs7OzRDQUtBcEYsU0FBU1UsU0FBUyxDQUFDNEcsR0FBRyxDQUFDLENBQUN5RixVQUFVQyxzQkFDakMsOERBQUMxUCxtREFBR0E7b0RBRUYwSCxJQUFJO3dEQUNGOEQsSUFBSTt3REFDSlUsR0FBRzt3REFDSHlELFFBQVE7d0RBQ1J4RCxjQUFjO29EQUNoQjs7c0VBRUEsOERBQUNuTSxtREFBR0E7NERBQ0YwSCxJQUFJO2dFQUNGMkQsU0FBUztnRUFDVEUsZ0JBQWdCO2dFQUNoQmdELFlBQVk7Z0VBQ1ovQyxJQUFJOzREQUNOOzs4RUFFQSw4REFBQ3hMLG1EQUFHQTtvRUFDRjBILElBQUk7d0VBQUUyRCxTQUFTO3dFQUFRa0QsWUFBWTt3RUFBVWdCLEtBQUs7b0VBQUU7O3NGQUVwRCw4REFBQ2pQLG1FQUFVQTs0RUFDVG9ILElBQUk7Z0ZBQ0ZJLE9BQU87Z0ZBQ1BDLFVBQVU7NEVBQ1o7Ozs7OztzRkFFRiw4REFBQ2xJLHNGQUFVQTs0RUFDVCtILFNBQVE7NEVBQ1JGLElBQUk7Z0ZBQUVHLFlBQVk7Z0ZBQUtDLE9BQU87NEVBQU87O2dGQUN0QztnRkFDWTRILFFBQVE7Ozs7Ozs7Ozs7Ozs7Z0VBR3RCaE4sU0FBU1UsU0FBUyxDQUFDQyxNQUFNLEdBQUcsbUJBQzNCLDhEQUFDeEUscUhBQU1BO29FQUNMK0ksU0FBUTtvRUFDUkUsT0FBTTtvRUFDTmlHLE1BQUs7b0VBQ0xuQyxTQUFTO3dFQUNQakgsWUFBWSxDQUFDZ0ssT0FBVTtnRkFDckIsR0FBR0EsSUFBSTtnRkFDUHZMLFdBQVd1TCxLQUFLdkwsU0FBUyxDQUFDcUcsTUFBTSxDQUM5QixDQUFDbUcsSUFBTUEsRUFBRXRMLEVBQUUsS0FBS21MLFNBQVNuTCxFQUFFOzRFQUUvQjtvRUFDRjs4RUFDRDs7Ozs7Ozs7Ozs7O3NFQU1MLDhEQUFDeEYsc0hBQUlBOzREQUFDK08sU0FBUzs0REFBQ0MsU0FBUzs7OEVBRXZCLDhEQUFDaFAsc0hBQUlBO29FQUFDaVAsTUFBTTt3RUFBRUMsSUFBSTt3RUFBSUMsSUFBSTtvRUFBRTs7c0ZBQzFCLDhEQUFDcE8sc0ZBQVVBOzRFQUFDK0gsU0FBUTs0RUFBUUYsSUFBSXlEO3NGQUFhOzs7Ozs7c0ZBRzdDLDhEQUFDdkwsc0ZBQVNBOzRFQUNSeU0sYUFBWTs0RUFDWnpFLFNBQVE7NEVBQ1IxQixNQUFNLFlBQXdCLE9BQVp1SixTQUFTbkwsRUFBRSxFQUFDOzRFQUM5QjRKLE1BQUs7NEVBQ0xDLFFBQVE7NEVBQ1IvQixTQUFTOzRFQUNUakssT0FBT2lNLFFBQ0xqTSxLQUFLLENBQUMsWUFBd0IsT0FBWnNOLFNBQVNuTCxFQUFFLEVBQUMsV0FBUzs0RUFFekMrSixZQUFZbE0sS0FBSyxDQUFDLFlBQXdCLE9BQVpzTixTQUFTbkwsRUFBRSxFQUFDLFdBQVM7NEVBQ25EZ0ksVUFBVSxDQUFDckc7Z0ZBQ1QsTUFBTTRKLGVBQWU7dUZBQUluTixTQUFTVSxTQUFTO2lGQUFDO2dGQUM1QyxNQUFNME0sZ0JBQWdCRCxhQUFhRSxTQUFTLENBQzFDLENBQUNILElBQU1BLEVBQUV0TCxFQUFFLEtBQUttTCxTQUFTbkwsRUFBRTtnRkFFN0J1TCxZQUFZLENBQUNDLGNBQWMsQ0FBQ3hNLE1BQU0sR0FDaEMyQyxFQUFFRSxNQUFNLENBQUNaLEtBQUs7Z0ZBQ2hCWixZQUFZLENBQUNnSyxPQUFVO3dGQUNyQixHQUFHQSxJQUFJO3dGQUNQdkwsV0FBV3lNO29GQUNiOzRFQUNGOzRFQUNBdEssT0FBT2tLLFNBQVNuTSxNQUFNOzRFQUN0QmlKLFlBQVk7Z0ZBQ1YrQiw0QkFDRSw4REFBQ3ZQLHNIQUFjQTtvRkFBQzBOLFVBQVM7OEZBQ3RCZ0QsU0FBU25NLE1BQU0sSUFDYm5CLENBQUFBLEtBQUssQ0FBQyxZQUF3QixPQUFac04sU0FBU25MLEVBQUUsRUFBQyxXQUFTLGlCQUN0Qyw4REFBQ3BFLGtFQUFTQTt3RkFBQzRILE9BQU07Ozs7OytHQUVqQiw4REFBQzdILHdFQUFlQTt3RkFBQzZILE9BQU07Ozs7OzhGQUN6Qjs7Ozs7OzRFQUdSOzs7Ozs7Ozs7Ozs7OEVBS0osOERBQUNoSixzSEFBSUE7b0VBQUNpUCxNQUFNO3dFQUFFQyxJQUFJO3dFQUFJQyxJQUFJO29FQUFFOztzRkFDMUIsOERBQUNwTyxzRkFBVUE7NEVBQUMrSCxTQUFROzRFQUFRRixJQUFJeUQ7c0ZBQWE7Ozs7OztzRkFHN0MsOERBQUN2TCxzRkFBU0E7NEVBQ1J5TSxhQUFZOzRFQUNaekUsU0FBUTs0RUFDUjFCLE1BQU0sWUFBd0IsT0FBWnVKLFNBQVNuTCxFQUFFLEVBQUM7NEVBQzlCNEosTUFBSzs0RUFDTDlCLFNBQVM7NEVBQ1RFLFVBQVUsQ0FBQ3JHO2dGQUNULE1BQU00SixlQUFlO3VGQUFJbk4sU0FBU1UsU0FBUztpRkFBQztnRkFDNUMsTUFBTTBNLGdCQUFnQkQsYUFBYUUsU0FBUyxDQUMxQyxDQUFDSCxJQUFNQSxFQUFFdEwsRUFBRSxLQUFLbUwsU0FBU25MLEVBQUU7Z0ZBRTdCdUwsWUFBWSxDQUFDQyxjQUFjLENBQUN2TSxLQUFLLEdBQy9CMEMsRUFBRUUsTUFBTSxDQUFDWixLQUFLO2dGQUNoQlosWUFBWSxDQUFDZ0ssT0FBVTt3RkFDckIsR0FBR0EsSUFBSTt3RkFDUHZMLFdBQVd5TTtvRkFDYjs0RUFDRjs0RUFDQXRLLE9BQU9rSyxTQUFTbE0sS0FBSzs7Ozs7Ozs7Ozs7OzhFQUt6Qiw4REFBQ3pFLHNIQUFJQTtvRUFBQ2lQLE1BQU07d0VBQUVDLElBQUk7d0VBQUlDLElBQUk7b0VBQUU7O3NGQUMxQiw4REFBQ3BPLHNGQUFVQTs0RUFBQytILFNBQVE7NEVBQVFGLElBQUl5RDtzRkFBYTs7Ozs7O3NGQUc3Qyw4REFBQ3ZMLHNGQUFTQTs0RUFDUnlNLGFBQVk7NEVBQ1p6RSxTQUFROzRFQUNSMUIsTUFBTSxZQUF3QixPQUFadUosU0FBU25MLEVBQUUsRUFBQzs0RUFDOUI0SixNQUFLOzRFQUNMOUIsU0FBUzs0RUFDVGpLLE9BQU9pTSxRQUNMak0sS0FBSyxDQUFDLFlBQXdCLE9BQVpzTixTQUFTbkwsRUFBRSxFQUFDLGFBQVc7NEVBRTNDK0osWUFDRWxNLEtBQUssQ0FBQyxZQUF3QixPQUFac04sU0FBU25MLEVBQUUsRUFBQyxhQUFXOzRFQUUzQ2dJLFVBQVUsQ0FBQ3JHO2dGQUNULE1BQU00SixlQUFlO3VGQUFJbk4sU0FBU1UsU0FBUztpRkFBQztnRkFDNUMsTUFBTTBNLGdCQUFnQkQsYUFBYUUsU0FBUyxDQUMxQyxDQUFDSCxJQUFNQSxFQUFFdEwsRUFBRSxLQUFLbUwsU0FBU25MLEVBQUU7Z0ZBRTdCdUwsWUFBWSxDQUFDQyxjQUFjLENBQUN0TSxRQUFRLEdBQ2xDeUMsRUFBRUUsTUFBTSxDQUFDWixLQUFLO2dGQUNoQlosWUFBWSxDQUFDZ0ssT0FBVTt3RkFDckIsR0FBR0EsSUFBSTt3RkFDUHZMLFdBQVd5TTtvRkFDYjs0RUFDRjs0RUFDQXRLLE9BQU9rSyxTQUFTak0sUUFBUTs0RUFDeEIrSSxZQUFZO2dGQUNWK0IsNEJBQ0UsOERBQUN2UCxzSEFBY0E7b0ZBQUMwTixVQUFTOzhGQUN0QmdELFNBQVNqTSxRQUFRLElBQ2ZyQixDQUFBQSxLQUFLLENBQ0osWUFBd0IsT0FBWnNOLFNBQVNuTCxFQUFFLEVBQUMsYUFDekIsaUJBQ0MsOERBQUNwRSxrRUFBU0E7d0ZBQUM0SCxPQUFNOzs7OzsrR0FFakIsOERBQUM3SCx3RUFBZUE7d0ZBQUM2SCxPQUFNOzs7Ozs4RkFDekI7Ozs7Ozs0RUFHUjs7Ozs7Ozs7Ozs7OzhFQUtKLDhEQUFDaEosc0hBQUlBO29FQUFDaVAsTUFBTTt3RUFBRUMsSUFBSTt3RUFBSUMsSUFBSTtvRUFBRTs7c0ZBQzFCLDhEQUFDcE8sc0ZBQVVBOzRFQUFDK0gsU0FBUTs0RUFBUUYsSUFBSXlEO3NGQUFhOzs7Ozs7c0ZBRzdDLDhEQUFDdkwsc0ZBQVNBOzRFQUNSeU0sYUFBWTs0RUFDWnpFLFNBQVE7NEVBQ1IxQixNQUFNLFlBQXdCLE9BQVp1SixTQUFTbkwsRUFBRSxFQUFDOzRFQUM5QjRKLE1BQUs7NEVBQ0w5QixTQUFTOzRFQUNUakssT0FBT2lNLFFBQ0xqTSxLQUFLLENBQUMsWUFBd0IsT0FBWnNOLFNBQVNuTCxFQUFFLEVBQUMsVUFBUTs0RUFFeEMrSixZQUFZbE0sS0FBSyxDQUFDLFlBQXdCLE9BQVpzTixTQUFTbkwsRUFBRSxFQUFDLFVBQVE7NEVBQ2xEZ0ksVUFBVSxDQUFDckc7Z0ZBQ1QsTUFBTTRKLGVBQWU7dUZBQUluTixTQUFTVSxTQUFTO2lGQUFDO2dGQUM1QyxNQUFNME0sZ0JBQWdCRCxhQUFhRSxTQUFTLENBQzFDLENBQUNILElBQU1BLEVBQUV0TCxFQUFFLEtBQUttTCxTQUFTbkwsRUFBRTtnRkFFN0J1TCxZQUFZLENBQUNDLGNBQWMsQ0FBQ3JNLEtBQUssR0FDL0J3QyxFQUFFRSxNQUFNLENBQUNaLEtBQUs7Z0ZBQ2hCWixZQUFZLENBQUNnSyxPQUFVO3dGQUNyQixHQUFHQSxJQUFJO3dGQUNQdkwsV0FBV3lNO29GQUNiOzRFQUNGOzRFQUNBdEssT0FBT2tLLFNBQVNoTSxLQUFLOzRFQUNyQjhJLFlBQVk7Z0ZBQ1YrQiw0QkFDRSw4REFBQ3ZQLHNIQUFjQTtvRkFBQzBOLFVBQVM7OEZBQ3RCZ0QsU0FBU2hNLEtBQUssSUFDWnRCLENBQUFBLEtBQUssQ0FBQyxZQUF3QixPQUFac04sU0FBU25MLEVBQUUsRUFBQyxVQUFRLGlCQUNyQyw4REFBQ3BFLGtFQUFTQTt3RkFBQzRILE9BQU07Ozs7OytHQUVqQiw4REFBQzdILHdFQUFlQTt3RkFBQzZILE9BQU07Ozs7OzhGQUN6Qjs7Ozs7OzRFQUdSOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O21EQWpORDJILFNBQVNuTCxFQUFFOzs7OzswREF5TnBCLDhEQUFDekYscUhBQU1BO2dEQUNMK0ksU0FBUTtnREFDUm9FLHlCQUFXLDhEQUFDM0wsc0VBQWFBOzs7OztnREFDekJ1TCxTQUFTO29EQUNQLE1BQU1vRSxRQUNKQyxLQUFLQyxHQUFHLElBQUl4TixTQUFTVSxTQUFTLENBQUM0RyxHQUFHLENBQUMsQ0FBQzRGLElBQU1BLEVBQUV0TCxFQUFFLEtBQUs7b0RBQ3JESyxZQUFZLENBQUNnSyxPQUFVOzREQUNyQixHQUFHQSxJQUFJOzREQUNQdkwsV0FBVzttRUFDTnVMLEtBQUt2TCxTQUFTO2dFQUNqQjtvRUFDRWtCLElBQUkwTDtvRUFDSjFNLFFBQVE7b0VBQ1JDLE9BQU87b0VBQ1BDLFVBQVU7b0VBQ1ZDLE9BQU87Z0VBQ1Q7NkRBQ0Q7d0RBQ0g7Z0RBQ0Y7Z0RBQ0FpRSxJQUFJO29EQUFFK0QsSUFBSTtnREFBRTswREFDYjs7Ozs7OzBEQUtELDhEQUFDekwsbURBQUdBO2dEQUNGMEgsSUFBSTtvREFDRjJELFNBQVM7b0RBQ1RFLGdCQUFnQjtvREFDaEJFLElBQUk7b0RBQ0pvRCxJQUFJO29EQUNKQyxXQUFXO2dEQUNiOztrRUFFQSw4REFBQ2pRLHFIQUFNQTt3REFDTCtJLFNBQVE7d0RBQ1JnRSxTQUFTNUg7d0RBQ1QwRCxJQUFJOzREQUFFaUcsVUFBVTt3REFBSTtrRUFDckI7Ozs7OztrRUFHRCw4REFBQzlPLHFIQUFNQTt3REFDTCtJLFNBQVE7d0RBQ1JzRyxNQUFLO3dEQUNMTixVQUFVcE47d0RBQ1ZvTCxTQUFTMUQ7d0RBQ1RSLElBQUk7NERBQ0ZpRyxVQUFVOzREQUNWOUIsU0FBUzs0REFDVCxXQUFXO2dFQUFFQSxTQUFTOzREQUFVO3dEQUNsQztrRUFFQ3JMLGVBQ0csaUJBQ0FnQixnQkFBZ0IsUUFDaEIsY0FDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVzFCO0dBNzVETWpCO0tBQUFBO0FBKzVETiwrREFBZUEsa0JBQWtCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvKHBhZ2luYXMpL2FncmljdWx0b3IvcGFnZS50c3g/YzI2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgQWRkT3V0bGluZWRJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0FkZE91dGxpbmVkXCI7XHJcbmltcG9ydCB7IERhdGFHcmlkIGFzIE11aURhdGFHcmlkIH0gZnJvbSBcIkBtdWkveC1kYXRhLWdyaWRcIjtcclxuaW1wb3J0IHtcclxuICBCdXR0b24sXHJcbiAgR3JpZCxcclxuICBJbnB1dEFkb3JubWVudCxcclxuICBQYXBlcixcclxuICBTZWxlY3RDaGFuZ2VFdmVudCxcclxuICBUYWJzLFxyXG4gIFRhYixcclxufSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBEaWFsb2cgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBEaWFsb2dDb250ZW50IH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgRGlhbG9nVGl0bGUgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBEaWFsb2dDb250ZW50VGV4dCB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IEZvcm1Db250cm9sIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgRm9ybUhlbHBlclRleHQgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5cclxuaW1wb3J0IHsgSWNvbkJ1dHRvbiB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IElucHV0TGFiZWwgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBNZW51SXRlbSB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IFNlbGVjdCB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IFRleHRGaWVsZCB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IFR5cG9ncmFwaHkgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgRGF0YXRhYmxlIGZyb20gXCIuLi8uLi9jb21wb25lbnRzL3RhYmxlL0RhdGFUYWJsZVwiO1xyXG5pbXBvcnQgQ2xvc2VJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0Nsb3NlXCI7XHJcbmltcG9ydCB7IEJveCB9IGZyb20gXCJAbXVpL3N5c3RlbVwiO1xyXG5pbXBvcnQgQ2hlY2tDaXJjbGVJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0NoZWNrQ2lyY2xlXCI7XHJcbmltcG9ydCBFcnJvckljb24gZnJvbSBcIkBtdWkvaWNvbnMtbWF0ZXJpYWwvRXJyb3JcIjtcclxuaW1wb3J0IHsgU2VhcmNoIGFzIFNlYXJjaEljb24gfSBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XHJcbmltcG9ydCBBZGRDaXJjbGVJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0FkZENpcmNsZVwiO1xyXG5pbXBvcnQgUGVyc29uSWNvbiBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbC9QZXJzb25cIjtcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcclxuXHJcbmludGVyZmFjZSBBZ3JpY3VsdG9yR2FuYWRlcm9Qcm9wcyB7XHJcbiAgb25TZWFyY2hDaGFuZ2U/OiAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB2b2lkO1xyXG4gIG9uQWRkQ2xpY2s/OiAoKSA9PiB2b2lkO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgQ2xpZW50IHtcclxuICBpZDogbnVtYmVyO1xyXG4gIHJhem9uU29jaWFsOiBzdHJpbmc7XHJcbiAgdGlwb0NsaWVudGU/OiBzdHJpbmc7XHJcbiAgbm9tYnJlQ29udGFjdG8/OiBzdHJpbmc7XHJcbiAgY2FyZ29Db250YWN0bz86IHN0cmluZztcclxuICBkaXJlY2Npb246IHN0cmluZztcclxuICB0ZWxlZm9ubzogc3RyaW5nO1xyXG4gIG1haWw6IHN0cmluZztcclxuICBsdWdhcjogc3RyaW5nO1xyXG4gIHByb3ZpbmNpYT86IHN0cmluZztcclxuICBjb25kRnJlbnRlSXZhOiBzdHJpbmc7XHJcbiAgZG9jdW1lbnRvOiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IEFncmljdWx0b3JHYW5hZGVybzogUmVhY3QuRkM8QWdyaWN1bHRvckdhbmFkZXJvUHJvcHM+ID0gKCkgPT4ge1xyXG4gIGNvbnN0IFtpc1N1Ym1pdHRpbmcsIHNldElzU3VibWl0dGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkUm93LCBzZXRTZWxlY3RlZFJvd10gPSB1c2VTdGF0ZTxDbGllbnQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbb3Blbiwgc2V0T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Jvd3MsIHNldFJvd3NdID0gdXNlU3RhdGU8Q2xpZW50W10+KFtdKTtcclxuICBjb25zdCBbaW5wdXRWYWx1ZSwgc2V0SW5wdXRWYWx1ZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbaXNTZWFyY2hCYXJPcGVuLCBzZXRJc1NlYXJjaEJhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtmaWx0ZXJlZFJvd3MsIHNldEZpbHRlcmVkUm93c10gPSB1c2VTdGF0ZTxDbGllbnRbXT4oW10pO1xyXG4gIGNvbnN0IERhdGFHcmlkID0gTXVpRGF0YUdyaWQ7XHJcbiAgY29uc3QgW3BlcnNvbklkLCBzZXRQZXJzb25JZF0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xyXG4gIGNvbnN0IFtlc3RhZG9Nb2RhbCwgc2V0RXN0YWRvTW9kYWxdID0gdXNlU3RhdGU8XCJhZGRcIiB8IFwidXBkYXRlXCI+KFwiYWRkXCIpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZENsaWVudElkLCBzZXRTZWxlY3RlZENsaWVudElkXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFt0YWJWYWx1ZSwgc2V0VGFiVmFsdWVdID0gdXNlU3RhdGUoMCk7XHJcbiAgY29uc3QgW2VuYWJsZWRUYWJzLCBzZXRFbmFibGVkVGFic10gPSB1c2VTdGF0ZTxib29sZWFuW10+KFtcclxuICAgIHRydWUsXHJcbiAgICBmYWxzZSxcclxuICAgIGZhbHNlLFxyXG4gIF0pOyAvLyBTb2xvIGxhIHByaW1lcmEgcGVzdGHDsWEgaGFiaWxpdGFkYSBpbmljaWFsbWVudGVcclxuICBjb25zdCBzZWxlY3RQcm92aW5jaWFSZWYgPSB1c2VSZWY8SFRNTElucHV0RWxlbWVudCB8IEhUTUxTZWxlY3RFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBzZWxlY3RDb25kSXZhUmVmID0gdXNlUmVmPEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudD4obnVsbCk7XHJcbiAgY29uc3QgZG9jdW1lbnRvUmVmID0gdXNlUmVmPEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudD4obnVsbCk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTx7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9Pih7fSk7XHJcblxyXG4gIC8vIEZ1bmNpb25lcyBkZSB2YWxpZGFjacOzbiBwYXJhIGNhZGEgcGVzdGHDsWFcclxuICBjb25zdCB2YWxpZGF0ZVRhYjEgPSAoKTogYm9vbGVhbiA9PiB7XHJcbiAgICBjb25zdCB7XHJcbiAgICAgIHByb3BpZXRhcmlvTm9tYnJlLFxyXG4gICAgICBwcm9waWV0YXJpb0RvY3VtZW50byxcclxuICAgICAgcHJvcGlldGFyaW9UZWxlZm9ubyxcclxuICAgICAgcHJvcGlldGFyaW9FbWFpbCxcclxuICAgIH0gPSBmb3JtRGF0YTtcclxuICAgIHJldHVybiAhIShcclxuICAgICAgcHJvcGlldGFyaW9Ob21icmUudHJpbSgpICYmXHJcbiAgICAgIHByb3BpZXRhcmlvRG9jdW1lbnRvLnRyaW0oKSAmJlxyXG4gICAgICBwcm9waWV0YXJpb1RlbGVmb25vLnRyaW0oKSAmJlxyXG4gICAgICBwcm9waWV0YXJpb0VtYWlsLnRyaW0oKVxyXG4gICAgKTtcclxuICB9O1xyXG5cclxuICBjb25zdCB2YWxpZGF0ZVRhYjIgPSAoKTogYm9vbGVhbiA9PiB7XHJcbiAgICBjb25zdCB7XHJcbiAgICAgIGVtcHJlc2FSYXpvblNvY2lhbCxcclxuICAgICAgZW1wcmVzYVRpcG9DbGllbnRlLFxyXG4gICAgICBlbXByZXNhRG9taWNpbGlvLFxyXG4gICAgICBlbXByZXNhTG9jYWxpZGFkLFxyXG4gICAgICBlbXByZXNhUHJvdmluY2lhLFxyXG4gICAgICBlbXByZXNhQ29uZEZyZW50ZUl2YSxcclxuICAgIH0gPSBmb3JtRGF0YTtcclxuICAgIHJldHVybiAhIShcclxuICAgICAgZW1wcmVzYVJhem9uU29jaWFsLnRyaW0oKSAmJlxyXG4gICAgICBlbXByZXNhVGlwb0NsaWVudGUudHJpbSgpICYmXHJcbiAgICAgIGVtcHJlc2FEb21pY2lsaW8udHJpbSgpICYmXHJcbiAgICAgIGVtcHJlc2FMb2NhbGlkYWQudHJpbSgpICYmXHJcbiAgICAgIGVtcHJlc2FQcm92aW5jaWEudHJpbSgpICYmXHJcbiAgICAgIGVtcHJlc2FDb25kRnJlbnRlSXZhLnRyaW0oKVxyXG4gICAgKTtcclxuICB9O1xyXG5cclxuICBjb25zdCB2YWxpZGF0ZVRhYjMgPSAoKTogYm9vbGVhbiA9PiB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICBmb3JtRGF0YS5jb250YWN0b3MubGVuZ3RoID4gMCAmJlxyXG4gICAgICBmb3JtRGF0YS5jb250YWN0b3NbMF0ubm9tYnJlLnRyaW0oKSAhPT0gXCJcIiAmJlxyXG4gICAgICBmb3JtRGF0YS5jb250YWN0b3NbMF0uY2FyZ28udHJpbSgpICE9PSBcIlwiICYmXHJcbiAgICAgIGZvcm1EYXRhLmNvbnRhY3Rvc1swXS50ZWxlZm9uby50cmltKCkgIT09IFwiXCIgJiZcclxuICAgICAgZm9ybURhdGEuY29udGFjdG9zWzBdLmVtYWlsLnRyaW0oKSAhPT0gXCJcIlxyXG4gICAgKTtcclxuICB9O1xyXG5cclxuICAvLyBGdW5jacOzbiBwYXJhIGhhYmlsaXRhciBsYSBzaWd1aWVudGUgcGVzdGHDsWFcclxuICBjb25zdCBlbmFibGVOZXh0VGFiID0gKGN1cnJlbnRUYWI6IG51bWJlcikgPT4ge1xyXG4gICAgY29uc3QgbmV3RW5hYmxlZFRhYnMgPSBbLi4uZW5hYmxlZFRhYnNdO1xyXG4gICAgaWYgKGN1cnJlbnRUYWIgKyAxIDwgbmV3RW5hYmxlZFRhYnMubGVuZ3RoKSB7XHJcbiAgICAgIG5ld0VuYWJsZWRUYWJzW2N1cnJlbnRUYWIgKyAxXSA9IHRydWU7XHJcbiAgICAgIHNldEVuYWJsZWRUYWJzKG5ld0VuYWJsZWRUYWJzKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBGdW5jacOzbiBwYXJhIGlyIGEgbGEgc2lndWllbnRlIHBlc3Rhw7FhXHJcbiAgY29uc3QgaGFuZGxlTmV4dFRhYiA9ICgpID0+IHtcclxuICAgIGxldCBjYW5Qcm9jZWVkID0gZmFsc2U7XHJcblxyXG4gICAgc3dpdGNoICh0YWJWYWx1ZSkge1xyXG4gICAgICBjYXNlIDA6XHJcbiAgICAgICAgY2FuUHJvY2VlZCA9IHZhbGlkYXRlVGFiMSgpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBjYXNlIDE6XHJcbiAgICAgICAgY2FuUHJvY2VlZCA9IHZhbGlkYXRlVGFiMigpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBjYXNlIDI6XHJcbiAgICAgICAgY2FuUHJvY2VlZCA9IHZhbGlkYXRlVGFiMygpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChjYW5Qcm9jZWVkKSB7XHJcbiAgICAgIGVuYWJsZU5leHRUYWIodGFiVmFsdWUpO1xyXG4gICAgICBzZXRUYWJWYWx1ZSh0YWJWYWx1ZSArIDEpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgYWxlcnQoXHJcbiAgICAgICAgXCJQb3IgZmF2b3IgY29tcGxldGUgdG9kb3MgbG9zIGNhbXBvcyByZXF1ZXJpZG9zIGFudGVzIGRlIGNvbnRpbnVhci5cIlxyXG4gICAgICApO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEZ1bmNpw7NuIHBhcmEgaXIgYSBsYSBwZXN0YcOxYSBhbnRlcmlvclxyXG4gIGNvbnN0IGhhbmRsZVByZXZpb3VzVGFiID0gKCkgPT4ge1xyXG4gICAgaWYgKHRhYlZhbHVlID4gMCkge1xyXG4gICAgICBzZXRUYWJWYWx1ZSh0YWJWYWx1ZSAtIDEpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNsaWVudFNlbGVjdCA9IChjbGllbnRJZDogbnVtYmVyKTogdm9pZCA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZENsaWVudElkKGNsaWVudElkKTtcclxuICAgIGNvbnN0IHNlbGVjdGVkQ2xpZW50ID0gKHJvd3MgYXMgQ2xpZW50W10pLmZpbmQoXHJcbiAgICAgIChyb3cpID0+IHJvdy5pZCA9PT0gY2xpZW50SWRcclxuICAgICk7XHJcbiAgICBpZiAoc2VsZWN0ZWRDbGllbnQpIHtcclxuICAgICAgLy8gU3BsaXQgdGhlIGx1Z2FyIGZpZWxkIGludG8gbG9jYWxpZGFkIGFuZCBwcm92aW5jaWFcclxuICAgICAgY29uc3QgW2xvY2FsaWRhZCwgcHJvdmluY2lhXSA9IHNlbGVjdGVkQ2xpZW50Lmx1Z2FyLnNwbGl0KFwiIC0gXCIpO1xyXG5cclxuICAgICAgc2V0Rm9ybURhdGEoKHByZXZEYXRhKSA9PiAoe1xyXG4gICAgICAgIC4uLnByZXZEYXRhLFxyXG4gICAgICAgIGVtcHJlc2FSYXpvblNvY2lhbDogc2VsZWN0ZWRDbGllbnQucmF6b25Tb2NpYWwsXHJcbiAgICAgICAgZW1wcmVzYVRpcG9DbGllbnRlOiBzZWxlY3RlZENsaWVudC50aXBvQ2xpZW50ZSB8fCBcIlwiLFxyXG4gICAgICAgIGVtcHJlc2FEb21pY2lsaW86IHNlbGVjdGVkQ2xpZW50LmRpcmVjY2lvbixcclxuICAgICAgICBlbXByZXNhTG9jYWxpZGFkOiBsb2NhbGlkYWQsXHJcbiAgICAgICAgZW1wcmVzYVByb3ZpbmNpYTogcHJvdmluY2lhLFxyXG4gICAgICAgIGVtcHJlc2FDb25kRnJlbnRlSXZhOiBzZWxlY3RlZENsaWVudC5jb25kRnJlbnRlSXZhLFxyXG4gICAgICAgIC8vIFVwZGF0ZSBjb250YWN0cyBhcnJheSB3aXRoIHRoZSBzZWxlY3RlZCBjbGllbnQncyBjb250YWN0IGluZm9ybWF0aW9uXHJcbiAgICAgICAgY29udGFjdG9zOiBbXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIGlkOiAxLFxyXG4gICAgICAgICAgICBub21icmU6IHNlbGVjdGVkQ2xpZW50Lm5vbWJyZUNvbnRhY3RvIHx8IFwiXCIsXHJcbiAgICAgICAgICAgIGNhcmdvOiBzZWxlY3RlZENsaWVudC5jYXJnb0NvbnRhY3RvIHx8IFwiXCIsXHJcbiAgICAgICAgICAgIHRlbGVmb25vOiBzZWxlY3RlZENsaWVudC50ZWxlZm9ubyxcclxuICAgICAgICAgICAgZW1haWw6IHNlbGVjdGVkQ2xpZW50Lm1haWwsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIF0sXHJcbiAgICAgIH0pKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcclxuICAgIC8vIFBlc3Rhw7FhIDE6IFByb3BpZXRhcmlvL1BlcnNvbmEgRsOtc2ljYVxyXG4gICAgcHJvcGlldGFyaW9Ob21icmU6IFwiXCIsXHJcbiAgICBwcm9waWV0YXJpb0RvY3VtZW50bzogXCJcIixcclxuICAgIHByb3BpZXRhcmlvVGVsZWZvbm86IFwiXCIsXHJcbiAgICBwcm9waWV0YXJpb0VtYWlsOiBcIlwiLFxyXG4gICAgdXNhckNvbW9SYXpvblNvY2lhbDogZmFsc2UsXHJcblxyXG4gICAgLy8gUGVzdGHDsWEgMjogRW1wcmVzYS9SYXrDs24gU29jaWFsXHJcbiAgICBlbXByZXNhUmF6b25Tb2NpYWw6IFwiXCIsXHJcbiAgICBlbXByZXNhVGlwb0NsaWVudGU6IFwiXCIsXHJcbiAgICBlbXByZXNhRG9taWNpbGlvOiBcIlwiLFxyXG4gICAgZW1wcmVzYUxvY2FsaWRhZDogXCJcIixcclxuICAgIGVtcHJlc2FQcm92aW5jaWE6IFwiXCIsXHJcbiAgICBlbXByZXNhQ29uZEZyZW50ZUl2YTogXCJcIixcclxuXHJcbiAgICAvLyBQZXN0YcOxYSAzOiBDb250YWN0b3MvRW5jYXJnYWRvcyAoYXJyYXkgZGluw6FtaWNvKVxyXG4gICAgY29udGFjdG9zOiBbXHJcbiAgICAgIHtcclxuICAgICAgICBpZDogMSxcclxuICAgICAgICBub21icmU6IFwiXCIsXHJcbiAgICAgICAgY2FyZ286IFwiXCIsXHJcbiAgICAgICAgdGVsZWZvbm86IFwiXCIsXHJcbiAgICAgICAgZW1haWw6IFwiXCIsXHJcbiAgICAgIH0sXHJcbiAgICBdLFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBwcm92aW5jaWFzID0gW1xyXG4gICAgXCJCdWVub3MgQWlyZXNcIixcclxuICAgIFwiQ2F0YW1hcmNhXCIsXHJcbiAgICBcIkNoYWNvXCIsXHJcbiAgICBcIkNodWJ1dFwiLFxyXG4gICAgXCJDw7NyZG9iYVwiLFxyXG4gICAgXCJDb3JyaWVudGVzXCIsXHJcbiAgICBcIkVudHJlIFLDrW9zXCIsXHJcbiAgICBcIkZvcm1vc2FcIixcclxuICAgIFwiSnVqdXlcIixcclxuICAgIFwiTGEgUGFtcGFcIixcclxuICAgIFwiTGEgUmlvamFcIixcclxuICAgIFwiTWVuZG96YVwiLFxyXG4gICAgXCJNaXNpb25lc1wiLFxyXG4gICAgXCJOZXVxdcOpblwiLFxyXG4gICAgXCJSw61vIE5lZ3JvXCIsXHJcbiAgICBcIlNhbHRhXCIsXHJcbiAgICBcIlNhbiBKdWFuXCIsXHJcbiAgICBcIlNhbiBMdWlzXCIsXHJcbiAgICBcIlNhbnRhIENydXpcIixcclxuICAgIFwiU2FudGEgRmVcIixcclxuICAgIFwiU2FudGlhZ28gZGVsIEVzdGVyb1wiLFxyXG4gICAgXCJUaWVycmEgZGVsIEZ1ZWdvXCIsXHJcbiAgICBcIlR1Y3Vtw6FuXCIsXHJcbiAgXTtcclxuXHJcbiAgY29uc3QgdGlwb0NsaWVudGVPcHRpb25zID0gW1xyXG4gICAgLy8gUHJvZHVjdG9yZXNcclxuICAgIHsgdmFsdWU6IFwiUHJvZHVjdG9yKGNvbWVyY2lhbClcIiwgY2F0ZWdvcnk6IFwiUHJvZHVjdG9yZXNcIiwgaWNvbjogXCLwn5Go4oCN8J+SvFwiIH0sXHJcbiAgICB7IHZhbHVlOiBcIlByb2R1Y3RvcihmYW1pbGlhcilcIiwgY2F0ZWdvcnk6IFwiUHJvZHVjdG9yZXNcIiwgaWNvbjogXCLwn5Go4oCN8J+RqeKAjfCfkafigI3wn5GmXCIgfSxcclxuICAgIHsgdmFsdWU6IFwiRXN0YW5jaWFcIiwgY2F0ZWdvcnk6IFwiUHJvZHVjdG9yZXNcIiwgaWNvbjogXCLwn4+e77iPXCIgfSxcclxuXHJcbiAgICAvLyBFbXByZXNhcyB5IE9yZ2FuaXphY2lvbmVzXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIkVtcHJlc2EgKHBlcnNvbmEganVyw61kaWNhLCBwLiBlai4gU0EgLyBTUkwpXCIsXHJcbiAgICAgIGNhdGVnb3J5OiBcIkVtcHJlc2FzXCIsXHJcbiAgICAgIGljb246IFwi8J+PolwiLFxyXG4gICAgfSxcclxuICAgIHsgdmFsdWU6IFwiQ29vcGVyYXRpdmFcIiwgY2F0ZWdvcnk6IFwiRW1wcmVzYXNcIiwgaWNvbjogXCLwn4+Y77iPXCIgfSxcclxuICAgIHtcclxuICAgICAgdmFsdWU6IFwiQXNvY2lhY2nDs24vQ29uc29yY2lvL0VudGlkYWQgR3JlbWlhbFwiLFxyXG4gICAgICBjYXRlZ29yeTogXCJFbXByZXNhc1wiLFxyXG4gICAgICBpY29uOiBcIvCfpJ1cIixcclxuICAgIH0sXHJcblxyXG4gICAgLy8gU2VydmljaW9zIHkgQ29udHJhdGlzdGFzXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIkNvbnRyYXRpc3RhKHAuIGVqLiBvdHJvIHF1ZSBjb250cmF0YSBlcXVpcG8pXCIsXHJcbiAgICAgIGNhdGVnb3J5OiBcIlNlcnZpY2lvc1wiLFxyXG4gICAgICBpY29uOiBcIvCfmpxcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIkFjb3Bpby9JbmR1c3RyaWEvRXhwb3J0YWRvcihzaWxvcywgcGxhbnRhcywgY29tcHJhZG9yYXMpXCIsXHJcbiAgICAgIGNhdGVnb3J5OiBcIlNlcnZpY2lvc1wiLFxyXG4gICAgICBpY29uOiBcIvCfj61cIixcclxuICAgIH0sXHJcblxyXG4gICAgLy8gU2VjdG9yIFDDumJsaWNvIHkgT3Ryb3NcclxuICAgIHtcclxuICAgICAgdmFsdWU6IFwiTXVuaWNpcGFsaWRhZC9Fc3RhdGFsL0d1YmVybmFtZW50YWxcIixcclxuICAgICAgY2F0ZWdvcnk6IFwiUMO6YmxpY29cIixcclxuICAgICAgaWNvbjogXCLvv71cIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIlBhcnRpY3VsYXIocGVxdWXDsW9zIGNsaWVudGVzIGRvbcOpc3RpY29zKVwiLFxyXG4gICAgICBjYXRlZ29yeTogXCJPdHJvc1wiLFxyXG4gICAgICBpY29uOiBcIvCfkaRcIixcclxuICAgIH0sXHJcbiAgICB7IHZhbHVlOiBcIk90cm8ocGFyYSBjYXNvcyBubyBwcmV2aXN0b3MpXCIsIGNhdGVnb3J5OiBcIk90cm9zXCIsIGljb246IFwi4p2TXCIgfSxcclxuICAgIHsgdmFsdWU6IFwiTm8gRXNwZWNpZmljYWRvXCIsIGNhdGVnb3J5OiBcIk90cm9zXCIsIGljb246IFwi4p6WXCIgfSxcclxuICBdO1xyXG5cclxuICBjb25zdCBjb25kRnJlbnRlSXZhT3B0aW9ucyA9IFtcclxuICAgIFwiSVZBIFJlc3BvbnNhYmxlIEluc2NyaXB0b1wiLFxyXG4gICAgXCJJVkEgUmVzcG9uc2FibGUgbm8gSW5zY3JpcHRvXCIsXHJcbiAgICBcIklWQSBubyBSZXNwb25zYWJsZVwiLFxyXG4gICAgXCJJVkEgU3VqZXRvIEV4ZW50b1wiLFxyXG4gICAgXCJDb25zdW1pZG9yIEZpbmFsXCIsXHJcbiAgICBcIlJlc3BvbnNhYmxlIE1vbm90cmlidXRvXCIsXHJcbiAgICBcIlN1amV0byBubyBDYXRlZ29yaXphZG9cIixcclxuICAgIFwiUHJvdmVlZG9yIGRlbCBFeHRlcmlvclwiLFxyXG4gICAgXCJDbGllbnRlIGRlbCBFeHRlcmlvclwiLFxyXG4gICAgXCJJVkEgTGliZXJhZG9cIixcclxuICAgIFwiUGVxdWXDsW8gQ29udHJpYnV5ZW50ZSBTb2NpYWxcIixcclxuICAgIFwiTW9ub3RyaWJ1dGlzdGEgU29jaWFsXCIsXHJcbiAgICBcIlBlcXVlw7FvIENvbnRyaWJ1eWVudGUgRXZlbnR1YWxcIixcclxuICBdO1xyXG5cclxuICBjb25zdCBoYW5kbGVPcGVuQWRkID0gKCkgPT4ge1xyXG4gICAgc2V0RXN0YWRvTW9kYWwoXCJhZGRcIik7XHJcbiAgICBjbGVhckZyb20oKTtcclxuICAgIHNldE9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY2xlYXJGcm9tID0gKCkgPT4ge1xyXG4gICAgc2V0Rm9ybURhdGEoe1xyXG4gICAgICAvLyBQZXN0YcOxYSAxOiBQcm9waWV0YXJpby9QZXJzb25hIEbDrXNpY2FcclxuICAgICAgcHJvcGlldGFyaW9Ob21icmU6IFwiXCIsXHJcbiAgICAgIHByb3BpZXRhcmlvRG9jdW1lbnRvOiBcIlwiLFxyXG4gICAgICBwcm9waWV0YXJpb1RlbGVmb25vOiBcIlwiLFxyXG4gICAgICBwcm9waWV0YXJpb0VtYWlsOiBcIlwiLFxyXG4gICAgICB1c2FyQ29tb1Jhem9uU29jaWFsOiBmYWxzZSxcclxuXHJcbiAgICAgIC8vIFBlc3Rhw7FhIDI6IEVtcHJlc2EvUmF6w7NuIFNvY2lhbFxyXG4gICAgICBlbXByZXNhUmF6b25Tb2NpYWw6IFwiXCIsXHJcbiAgICAgIGVtcHJlc2FUaXBvQ2xpZW50ZTogXCJcIixcclxuICAgICAgZW1wcmVzYURvbWljaWxpbzogXCJcIixcclxuICAgICAgZW1wcmVzYUxvY2FsaWRhZDogXCJcIixcclxuICAgICAgZW1wcmVzYVByb3ZpbmNpYTogXCJcIixcclxuICAgICAgZW1wcmVzYUNvbmRGcmVudGVJdmE6IFwiXCIsXHJcblxyXG4gICAgICAvLyBQZXN0YcOxYSAzOiBDb250YWN0b3MvRW5jYXJnYWRvc1xyXG4gICAgICBjb250YWN0b3M6IFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICBpZDogMSxcclxuICAgICAgICAgIG5vbWJyZTogXCJcIixcclxuICAgICAgICAgIGNhcmdvOiBcIlwiLFxyXG4gICAgICAgICAgdGVsZWZvbm86IFwiXCIsXHJcbiAgICAgICAgICBlbWFpbDogXCJcIixcclxuICAgICAgICB9LFxyXG4gICAgICBdLFxyXG4gICAgfSk7XHJcbiAgICBzZXRFcnJvcih7fSk7XHJcbiAgICBzZXRUYWJWYWx1ZSgwKTsgLy8gUmVzZXRlYXIgYWwgcHJpbWVyIHRhYlxyXG4gICAgc2V0RW5hYmxlZFRhYnMoW3RydWUsIGZhbHNlLCBmYWxzZV0pOyAvLyBSZXNldGVhciBwZXN0YcOxYXMgaGFiaWxpdGFkYXNcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDbGlja0Nsb3NlID0gKFxyXG4gICAgX2V2ZW50OiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxFbGVtZW50PixcclxuICAgIHJlYXNvbj86IHN0cmluZ1xyXG4gICkgPT4ge1xyXG4gICAgaWYgKHJlYXNvbiAmJiByZWFzb24gPT09IFwiYmFja2Ryb3BDbGlja1wiKSByZXR1cm47XHJcbiAgICBzZXRPcGVuKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChcclxuICAgIGU6IFJlYWN0LkNoYW5nZUV2ZW50PFxyXG4gICAgICBIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFNlbGVjdEVsZW1lbnQgfCBIVE1MVGV4dEFyZWFFbGVtZW50XHJcbiAgICA+XHJcbiAgKSA9PiB7XHJcbiAgICBjb25zdCB7IG5hbWUsIHZhbHVlIH0gPSBlLnRhcmdldDtcclxuXHJcbiAgICAvLyBWYWxpZGFjaW9uZXMgcGFyYSBjYW1wb3MgZGUgdGV4dG8gKG5vbWJyZXMsIHJhesOzbiBzb2NpYWwsIGV0Yy4pXHJcbiAgICBpZiAoXHJcbiAgICAgIG5hbWUgPT09IFwicHJvcGlldGFyaW9Ob21icmVcIiB8fFxyXG4gICAgICBuYW1lID09PSBcImVtcHJlc2FSYXpvblNvY2lhbFwiIHx8XHJcbiAgICAgIG5hbWUgPT09IFwiZW1wcmVzYUxvY2FsaWRhZFwiIHx8XHJcbiAgICAgIChuYW1lLnN0YXJ0c1dpdGgoXCJjb250YWN0b1wiKSAmJiBuYW1lLmluY2x1ZGVzKFwibm9tYnJlXCIpKVxyXG4gICAgKSB7XHJcbiAgICAgIGlmICghL15bYS16QS1aw4Atw79cXHNdKiQvLnRlc3QodmFsdWUpKSB7XHJcbiAgICAgICAgc2V0RXJyb3IoKHByZXZFcnJvcikgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICAgIFtuYW1lXTogXCJTb2xvIHNlIHBlcm1pdGVuIGxldHJhcyB5IGVzcGFjaW9zXCIsXHJcbiAgICAgICAgfSkpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRFcnJvcigocHJldkVycm9yKSA9PiAoe1xyXG4gICAgICAgICAgLi4ucHJldkVycm9yLFxyXG4gICAgICAgICAgW25hbWVdOiBcIlwiLFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFZhbGlkYWNpb25lcyBwYXJhIGRvbWljaWxpb1xyXG4gICAgaWYgKG5hbWUgPT09IFwiZW1wcmVzYURvbWljaWxpb1wiKSB7XHJcbiAgICAgIGlmICghL15bYS16QS1aw4Atw78wLTlcXHMuXSokLy50ZXN0KHZhbHVlKSkge1xyXG4gICAgICAgIHNldEVycm9yKChwcmV2RXJyb3IpID0+ICh7XHJcbiAgICAgICAgICAuLi5wcmV2RXJyb3IsXHJcbiAgICAgICAgICBbbmFtZV06IFwiU29sbyBzZSBwZXJtaXRlbiBsZXRyYXMsIG7Dum1lcm9zLCBlc3BhY2lvcyB5IHB1bnRvc1wiLFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0RXJyb3IoKHByZXZFcnJvcikgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICAgIFtuYW1lXTogXCJcIixcclxuICAgICAgICB9KSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBWYWxpZGFjaW9uZXMgcGFyYSB0ZWzDqWZvbm9zXHJcbiAgICBpZiAobmFtZSA9PT0gXCJwcm9waWV0YXJpb1RlbGVmb25vXCIgfHwgbmFtZS5pbmNsdWRlcyhcInRlbGVmb25vXCIpKSB7XHJcbiAgICAgIC8vIEVsaW1pbmFyIHRvZG8gbG8gcXVlIG5vIHNlYSBuw7ptZXJvXHJcbiAgICAgIGNvbnN0IGNsZWFuZWQgPSB2YWx1ZS5yZXBsYWNlKC9cXEQvZywgXCJcIik7XHJcblxyXG4gICAgICAvLyBMaW1pdGFyIGEgMTAgZMOtZ2l0b3NcclxuICAgICAgaWYgKGNsZWFuZWQubGVuZ3RoID4gMTApIHJldHVybjtcclxuXHJcbiAgICAgIGxldCBmb3JtYXR0ZWQ7XHJcbiAgICAgIGlmIChjbGVhbmVkLmxlbmd0aCA8PSA0KSB7XHJcbiAgICAgICAgZm9ybWF0dGVkID0gY2xlYW5lZDtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBmb3JtYXR0ZWQgPSBgJHtjbGVhbmVkLnNsaWNlKDAsIDQpfS0ke2NsZWFuZWQuc2xpY2UoNCl9YDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVmFsaWRhciBlbCBmb3JtYXRvIGNvbXBsZXRvXHJcbiAgICAgIGNvbnN0IGlzVmFsaWRGb3JtYXQgPSAvXlxcZHs0fS1cXGR7Nn0kLy50ZXN0KGZvcm1hdHRlZCk7XHJcblxyXG4gICAgICBzZXRFcnJvcigocHJldkVycm9yKSA9PiAoe1xyXG4gICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICBbbmFtZV06XHJcbiAgICAgICAgICBmb3JtYXR0ZWQubGVuZ3RoID09PSAxMSAmJiAhaXNWYWxpZEZvcm1hdFxyXG4gICAgICAgICAgICA/IFwiRm9ybWF0byBpbnbDoWxpZG8uIERlYmUgc2VyIDAwMDAtMDAwMDAwXCJcclxuICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICB9KSk7XHJcblxyXG4gICAgICBzZXRGb3JtRGF0YSgocHJldlN0YXRlKSA9PiAoe1xyXG4gICAgICAgIC4uLnByZXZTdGF0ZSxcclxuICAgICAgICBbbmFtZV06IGZvcm1hdHRlZCxcclxuICAgICAgfSkpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKG5hbWUgPT09IFwicGVyc29uTWFpbFwiKSB7XHJcbiAgICAgIC8vIEV4cHJlc2nDs24gcmVndWxhciBwYXJhIHZhbGlkYXIgZW1haWxcclxuICAgICAgY29uc3QgZW1haWxSZWdleCA9IC9eW1xcdy1dKyhcXC5bXFx3LV0rKSpAKFtcXHctXStcXC4pK1thLXpBLVpdezIsN30kLztcclxuXHJcbiAgICAgIC8vIFNpIGVsIGNhbXBvIG5vIGVzdMOhIHZhY8OtbywgdmFsaWRhciBlbCBmb3JtYXRvXHJcbiAgICAgIGlmICh2YWx1ZSAmJiAhZW1haWxSZWdleC50ZXN0KHZhbHVlKSkge1xyXG4gICAgICAgIHNldEVycm9yKChwcmV2RXJyb3IpID0+ICh7XHJcbiAgICAgICAgICAuLi5wcmV2RXJyb3IsXHJcbiAgICAgICAgICBbbmFtZV06IFwiRm9ybWF0byBkZSBlbWFpbCBpbnbDoWxpZG8uIEVqZW1wbG86IHVzdWFyaW9AZG9taW5pby5jb21cIixcclxuICAgICAgICB9KSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0RXJyb3IoKHByZXZFcnJvcikgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICAgIFtuYW1lXTogXCJcIixcclxuICAgICAgICB9KSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAobmFtZSA9PT0gXCJwcm9waWV0YXJpb0RvY3VtZW50b1wiKSB7XHJcbiAgICAgIC8vIEVsaW1pbmFyIHRvZG8gbG8gcXVlIG5vIHNlYSBuw7ptZXJvXHJcbiAgICAgIGNvbnN0IGNsZWFuZWQgPSB2YWx1ZS5yZXBsYWNlKC9cXEQvZywgXCJcIik7XHJcblxyXG4gICAgICAvLyBMaW1pdGFyIGEgMTEgZMOtZ2l0b3MgZW4gdG90YWxcclxuICAgICAgaWYgKGNsZWFuZWQubGVuZ3RoID4gMTEpIHJldHVybjtcclxuXHJcbiAgICAgIGxldCBmb3JtYXR0ZWQ7XHJcbiAgICAgIGlmIChjbGVhbmVkLmxlbmd0aCA8PSAyKSB7XHJcbiAgICAgICAgZm9ybWF0dGVkID0gY2xlYW5lZDtcclxuICAgICAgfSBlbHNlIGlmIChjbGVhbmVkLmxlbmd0aCA8PSAxMCkge1xyXG4gICAgICAgIGZvcm1hdHRlZCA9IGAke2NsZWFuZWQuc2xpY2UoMCwgMil9LSR7Y2xlYW5lZC5zbGljZSgyKX1gO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGZvcm1hdHRlZCA9IGAke2NsZWFuZWQuc2xpY2UoMCwgMil9LSR7Y2xlYW5lZC5zbGljZShcclxuICAgICAgICAgIDIsXHJcbiAgICAgICAgICAxMFxyXG4gICAgICAgICl9LSR7Y2xlYW5lZC5zbGljZSgxMCwgMTEpfWA7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFZhbGlkYXIgZWwgZm9ybWF0byBjb21wbGV0b1xyXG4gICAgICBjb25zdCBpc1ZhbGlkRm9ybWF0ID0gL15cXGR7Mn0tXFxkezh9LVxcZHsxfSQvLnRlc3QoZm9ybWF0dGVkKTtcclxuXHJcbiAgICAgIHNldEVycm9yKChwcmV2RXJyb3IpID0+ICh7XHJcbiAgICAgICAgLi4ucHJldkVycm9yLFxyXG4gICAgICAgIFtuYW1lXTpcclxuICAgICAgICAgIGZvcm1hdHRlZC5sZW5ndGggPT09IDEyICYmICFpc1ZhbGlkRm9ybWF0XHJcbiAgICAgICAgICAgID8gXCJGb3JtYXRvIGludsOhbGlkby4gRGViZSBzZXIgMDAtMDAwMDAwMDAtMFwiXHJcbiAgICAgICAgICAgIDogXCJcIixcclxuICAgICAgfSkpO1xyXG5cclxuICAgICAgc2V0Rm9ybURhdGEoKHByZXZTdGF0ZSkgPT4gKHtcclxuICAgICAgICAuLi5wcmV2U3RhdGUsXHJcbiAgICAgICAgW25hbWVdOiBmb3JtYXR0ZWQsXHJcbiAgICAgIH0pKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIHNldEZvcm1EYXRhKChwcmV2U3RhdGUpID0+IHtcclxuICAgICAgY29uc3QgbmV3U3RhdGUgPSB7XHJcbiAgICAgICAgLi4ucHJldlN0YXRlLFxyXG4gICAgICAgIFtuYW1lXTogdmFsdWUsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICAvLyBTaSBlc3TDoSBtYXJjYWRhIGxhIG9wY2nDs24gZGUgdXNhciBjb21vIHJhesOzbiBzb2NpYWwsIHNpbmNyb25pemFyIGRhdG9zXHJcbiAgICAgIGlmIChwcmV2U3RhdGUudXNhckNvbW9SYXpvblNvY2lhbCkge1xyXG4gICAgICAgIGlmIChuYW1lID09PSBcInByb3BpZXRhcmlvTm9tYnJlXCIpIHtcclxuICAgICAgICAgIG5ld1N0YXRlLmVtcHJlc2FSYXpvblNvY2lhbCA9IHZhbHVlO1xyXG4gICAgICAgICAgLy8gVGFtYmnDqW4gYWN0dWFsaXphciBlbCBwcmltZXIgY29udGFjdG9cclxuICAgICAgICAgIG5ld1N0YXRlLmNvbnRhY3RvcyA9IFtcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIC4uLnByZXZTdGF0ZS5jb250YWN0b3NbMF0sXHJcbiAgICAgICAgICAgICAgbm9tYnJlOiB2YWx1ZSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgLi4ucHJldlN0YXRlLmNvbnRhY3Rvcy5zbGljZSgxKSxcclxuICAgICAgICAgIF07XHJcbiAgICAgICAgfSBlbHNlIGlmIChuYW1lID09PSBcInByb3BpZXRhcmlvVGVsZWZvbm9cIikge1xyXG4gICAgICAgICAgLy8gQWN0dWFsaXphciBlbCB0ZWzDqWZvbm8gZGVsIHByaW1lciBjb250YWN0b1xyXG4gICAgICAgICAgbmV3U3RhdGUuY29udGFjdG9zID0gW1xyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgLi4ucHJldlN0YXRlLmNvbnRhY3Rvc1swXSxcclxuICAgICAgICAgICAgICB0ZWxlZm9ubzogdmFsdWUsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIC4uLnByZXZTdGF0ZS5jb250YWN0b3Muc2xpY2UoMSksXHJcbiAgICAgICAgICBdO1xyXG4gICAgICAgIH0gZWxzZSBpZiAobmFtZSA9PT0gXCJwcm9waWV0YXJpb0VtYWlsXCIpIHtcclxuICAgICAgICAgIC8vIEFjdHVhbGl6YXIgZWwgZW1haWwgZGVsIHByaW1lciBjb250YWN0b1xyXG4gICAgICAgICAgbmV3U3RhdGUuY29udGFjdG9zID0gW1xyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgLi4ucHJldlN0YXRlLmNvbnRhY3Rvc1swXSxcclxuICAgICAgICAgICAgICBlbWFpbDogdmFsdWUsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIC4uLnByZXZTdGF0ZS5jb250YWN0b3Muc2xpY2UoMSksXHJcbiAgICAgICAgICBdO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIG5ld1N0YXRlO1xyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gKGU6IFJlYWN0LkZvcm1FdmVudDxIVE1MRm9ybUVsZW1lbnQ+KSA9PiB7XHJcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VhcmNoQ2xpY2sgPSAoKSA9PiB7XHJcbiAgICBzZXRJc1NlYXJjaEJhck9wZW4oIWlzU2VhcmNoQmFyT3Blbik7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY29sdW1ucyA9IFtcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiZW1wcmVzYVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkVtcHJlc2FcIixcclxuICAgICAgd2lkdGg6IDI4MCxcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImN1c3RvbS1oZWFkZXJcIixcclxuICAgICAgcmVuZGVyQ2VsbDogKHBhcmFtczogYW55KSA9PiAoXHJcbiAgICAgICAgPEJveCBzeD17eyBweTogMSB9fT5cclxuICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgIHZhcmlhbnQ9XCJib2R5MlwiXHJcbiAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogXCI2MDBcIixcclxuICAgICAgICAgICAgICBjb2xvcjogXCIjMzMzXCIsXHJcbiAgICAgICAgICAgICAgZm9udFNpemU6IFwiMC44NzVyZW1cIixcclxuICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAxLjIsXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHtwYXJhbXMucm93LnJhem9uU29jaWFsfVxyXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAge3BhcmFtcy5yb3cudGlwb0NsaWVudGUgJiYgKFxyXG4gICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJjYXB0aW9uXCJcclxuICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgY29sb3I6IFwiIzY2NlwiLFxyXG4gICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMC43NXJlbVwiLFxyXG4gICAgICAgICAgICAgICAgZm9udFN0eWxlOiBcIml0YWxpY1wiLFxyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7cGFyYW1zLnJvdy50aXBvQ2xpZW50ZX1cclxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L0JveD5cclxuICAgICAgKSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcImNvbnRhY3RvXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiQ29udGFjdG9cIixcclxuICAgICAgd2lkdGg6IDIyMCxcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImN1c3RvbS1oZWFkZXJcIixcclxuICAgICAgcmVuZGVyQ2VsbDogKHBhcmFtczogYW55KSA9PiAoXHJcbiAgICAgICAgPEJveCBzeD17eyBweTogMSB9fT5cclxuICAgICAgICAgIHtwYXJhbXMucm93Lm5vbWJyZUNvbnRhY3RvID8gKFxyXG4gICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiYm9keTJcIlxyXG4gICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogXCI2MDBcIixcclxuICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzMzM1wiLFxyXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIwLjg3NXJlbVwiLFxyXG4gICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAxLjIsXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtwYXJhbXMucm93Lm5vbWJyZUNvbnRhY3RvfVxyXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICB7cGFyYW1zLnJvdy5jYXJnb0NvbnRhY3RvICYmIChcclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJjYXB0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjNjY2XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMC43NXJlbVwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTdHlsZTogXCJpdGFsaWNcIixcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge3BhcmFtcy5yb3cuY2FyZ29Db250YWN0b31cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cImJvZHkyXCJcclxuICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgY29sb3I6IFwiIzk5OVwiLFxyXG4gICAgICAgICAgICAgICAgZm9udFN0eWxlOiBcIml0YWxpY1wiLFxyXG4gICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMC44NzVyZW1cIixcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgU2luIGNvbnRhY3RvXHJcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9Cb3g+XHJcbiAgICAgICksXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJ0ZWxlZm9ub1wiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIlRlbMOpZm9ub1wiLFxyXG4gICAgICB3aWR0aDogMTQwLFxyXG4gICAgICBoZWFkZXJDbGFzc05hbWU6IFwiY3VzdG9tLWhlYWRlclwiLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwibWFpbFwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkVtYWlsXCIsXHJcbiAgICAgIHdpZHRoOiAxODAsXHJcbiAgICAgIGhlYWRlckNsYXNzTmFtZTogXCJjdXN0b20taGVhZGVyXCIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJsdWdhclwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIlViaWNhY2nDs25cIixcclxuICAgICAgd2lkdGg6IDIwMCxcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImN1c3RvbS1oZWFkZXJcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcImRvY3VtZW50b1wiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkRvY3VtZW50b1wiLFxyXG4gICAgICB3aWR0aDogMTQwLFxyXG4gICAgICBoZWFkZXJDbGFzc05hbWU6IFwiY3VzdG9tLWhlYWRlclwiLFxyXG4gICAgfSxcclxuICBdO1xyXG5cclxuICAvKkFHUkVHQVIgQUdSSUNVTFRPUi9HQU5BREVSTyovXHJcbiAgY29uc3QgaGFuZGxlQWRkQ2xpZW50ZSA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFwiSW5pY2lhbmRvIGVudsOtby4uLlwiKTtcclxuICAgIHNldElzU3VibWl0dGluZyh0cnVlKTtcclxuICAgIGNvbnN0IGx1Z2FyID0gYCR7Zm9ybURhdGEuZW1wcmVzYUxvY2FsaWRhZH0gLSAke2Zvcm1EYXRhLmVtcHJlc2FQcm92aW5jaWF9YDtcclxuICAgIGNvbnN0IG5ld1BlcnNvbiA9IHtcclxuICAgICAgLy8gRGF0b3MgZGVsIFByb3BpZXRhcmlvL1BlcnNvbmEgRsOtc2ljYSAoUGVzdGHDsWEgMSlcclxuICAgICAgcHJvcGlldGFyaW9Ob21icmU6IGZvcm1EYXRhLnByb3BpZXRhcmlvTm9tYnJlLFxyXG4gICAgICBwcm9waWV0YXJpb0RvY3VtZW50bzogZm9ybURhdGEucHJvcGlldGFyaW9Eb2N1bWVudG8sXHJcbiAgICAgIHByb3BpZXRhcmlvVGVsZWZvbm86IGZvcm1EYXRhLnByb3BpZXRhcmlvVGVsZWZvbm8sXHJcbiAgICAgIHByb3BpZXRhcmlvRW1haWw6IGZvcm1EYXRhLnByb3BpZXRhcmlvRW1haWwsXHJcblxyXG4gICAgICAvLyBEYXRvcyBkZSBsYSBFbXByZXNhL1JhesOzbiBTb2NpYWwgKFBlc3Rhw7FhIDIpXHJcbiAgICAgIHJhem9uU29jaWFsOiBmb3JtRGF0YS5lbXByZXNhUmF6b25Tb2NpYWwsXHJcbiAgICAgIHRpcG9DbGllbnRlOiBmb3JtRGF0YS5lbXByZXNhVGlwb0NsaWVudGUsXHJcbiAgICAgIGRpcmVjY2lvbjogZm9ybURhdGEuZW1wcmVzYURvbWljaWxpbyxcclxuICAgICAgZW1wcmVzYUxvY2FsaWRhZDogZm9ybURhdGEuZW1wcmVzYUxvY2FsaWRhZCxcclxuICAgICAgcHJvdmluY2lhOiBmb3JtRGF0YS5lbXByZXNhUHJvdmluY2lhLFxyXG4gICAgICBjb25kRnJlbnRlSXZhOiBmb3JtRGF0YS5lbXByZXNhQ29uZEZyZW50ZUl2YSxcclxuXHJcbiAgICAgIC8vIERhdG9zIGRlbCBDb250YWN0by9FbmNhcmdhZG8gUHJpbmNpcGFsIChQZXN0YcOxYSAzKVxyXG4gICAgICBub21icmVDb250YWN0bzogZm9ybURhdGEuY29udGFjdG9zWzBdPy5ub21icmUgfHwgXCJcIixcclxuICAgICAgY2FyZ29Db250YWN0bzogZm9ybURhdGEuY29udGFjdG9zWzBdPy5jYXJnbyB8fCBcIlwiLFxyXG4gICAgICB0ZWxlZm9ubzogZm9ybURhdGEuY29udGFjdG9zWzBdPy50ZWxlZm9ubyB8fCBcIlwiLFxyXG4gICAgICBtYWlsOiBmb3JtRGF0YS5jb250YWN0b3NbMF0/LmVtYWlsIHx8IFwiXCIsXHJcblxyXG4gICAgICAvLyBDYW1wb3MgY2FsY3VsYWRvcy9kZXJpdmFkb3NcclxuICAgICAgbHVnYXI6IGx1Z2FyLFxyXG4gICAgICBkb2N1bWVudG86IGZvcm1EYXRhLnByb3BpZXRhcmlvRG9jdW1lbnRvLCAvLyBVc2FyIGVsIGRvY3VtZW50byBkZWwgcHJvcGlldGFyaW9cclxuICAgIH07XHJcblxyXG4gICAgLy8gVE9ETzogQWRkIHByb3BlciBmb3JtIHZhbGlkYXRpb24gaWYgbmVlZGVkXHJcbiAgICAvLyBGb3Igbm93LCBpbmRpdmlkdWFsIGZpZWxkIHZhbGlkYXRpb25zIGFyZSBoYW5kbGVkIGluIHRoZSBvbkNoYW5nZSBoYW5kbGVyc1xyXG5cclxuICAgIC8vIE1vc3RyYXIgY2FkYSBkYXRvIGluZGl2aWR1YWwgZW4gbGEgY29uc29sYVxyXG4gICAgY29uc29sZS5sb2coXCJSYXrDs24gU29jaWFsOlwiLCBuZXdQZXJzb24ucmF6b25Tb2NpYWwpO1xyXG4gICAgY29uc29sZS5sb2coXCJEaXJlY2Npw7NuOlwiLCBuZXdQZXJzb24uZGlyZWNjaW9uKTtcclxuICAgIGNvbnNvbGUubG9nKFwiVGVsw6lmb25vOlwiLCBuZXdQZXJzb24udGVsZWZvbm8pO1xyXG4gICAgY29uc29sZS5sb2coXCJNYWlsOlwiLCBuZXdQZXJzb24ubWFpbCk7XHJcbiAgICBjb25zb2xlLmxvZyhcIkx1Z2FyOlwiLCBsdWdhcik7XHJcbiAgICBjb25zb2xlLmxvZyhcIkNvbmRpY2nDs24gRnJlbnRlIElWQTpcIiwgbmV3UGVyc29uLmNvbmRGcmVudGVJdmEpO1xyXG4gICAgY29uc29sZS5sb2coXCJEb2N1bWVudG86XCIsIG5ld1BlcnNvbi5kb2N1bWVudG8pO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDo4MDgwL2FwaS9hZ3JpY3VsdG9yXCIsIHtcclxuICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkobmV3UGVyc29uKSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBWZXJpZmljYXIgc2kgbGEgc29saWNpdHVkIGZ1ZSBleGl0b3NhXHJcbiAgICAgIGlmICghcmVzLm9rKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRXJyb3IgYWwgZ3VhcmRhciBlbCBjbGllbnRlXCIpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjbGVhckZyb20oKTtcclxuICAgICAgY29uc3QgZGF0YUNsaWVudGVzID0gYXdhaXQgZmV0Y2hDbGllbnRlcygpO1xyXG4gICAgICBzZXRSb3dzKGRhdGFDbGllbnRlcyk7XHJcbiAgICAgIHNldE9wZW4oZmFsc2UpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGVuIGxhIHNvbGljaXR1ZDpcIiwgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8qQ0FSR0FSIEFHUklDVUxUT1IvR0FOQURFUk8gRU4gRUwgREFUQUdSSUQqL1xyXG4gIGNvbnN0IGZldGNoQ2xpZW50ZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9hcGkvYWdyaWN1bHRvclwiKTtcclxuICAgICAgaWYgKCFyZXMub2spIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJFcnJvciBhbCBvYnRlbmVyIGxvcyBjbGllbnRlc1wiKTtcclxuICAgICAgfVxyXG4gICAgICBjb25zdCBkYXRhQ2xpZW50ZXMgPSBhd2FpdCByZXMuanNvbigpO1xyXG4gICAgICByZXR1cm4gZGF0YUNsaWVudGVzO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGVuIGxhIHNvbGljaXR1ZDpcIiwgZXJyb3IpO1xyXG4gICAgICAvLyBEZXZvbHZlciB1biB2YWxvciBwcmVkZXRlcm1pbmFkbyBlbiBjYXNvIGRlIGVycm9yXHJcbiAgICAgIHJldHVybiBbXTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZ2V0RGF0YSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgZGF0YUNsaWVudGVzID0gYXdhaXQgZmV0Y2hDbGllbnRlcygpO1xyXG4gICAgICBzZXRSb3dzKGRhdGFDbGllbnRlcyk7XHJcbiAgICB9O1xyXG5cclxuICAgIGdldERhdGEoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8qQlVTQ0FSIEFHUklDVUxUT1IvR0FOQURFUk8qL1xyXG4gIGNvbnN0IGhhbmRsZVNlYXJoQ2xpZW50ZSA9IChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IHNlYXJjaFZhbHVlID0gZXZlbnQudGFyZ2V0LnZhbHVlO1xyXG4gICAgc2V0U2VhcmNoVGVybShzZWFyY2hWYWx1ZSk7XHJcblxyXG4gICAgY29uc3QgZmlsdGVyZWREYXRhID0gcm93cy5maWx0ZXIoKHJvdzogQ2xpZW50KSA9PiB7XHJcbiAgICAgIHJldHVybiAoXHJcbiAgICAgICAgcm93LnJhem9uU29jaWFsLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICByb3cuZGlyZWNjaW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICByb3cudGVsZWZvbm8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hWYWx1ZS50b0xvd2VyQ2FzZSgpKSB8fFxyXG4gICAgICAgIHJvdy5tYWlsLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICByb3cubHVnYXIudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hWYWx1ZS50b0xvd2VyQ2FzZSgpKSB8fFxyXG4gICAgICAgIHJvdy5jb25kRnJlbnRlSXZhLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICByb3cuZG9jdW1lbnRvLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSlcclxuICAgICAgKTtcclxuICAgIH0pO1xyXG4gICAgc2V0RmlsdGVyZWRSb3dzKGZpbHRlcmVkRGF0YSk7XHJcbiAgfTtcclxuXHJcbiAgLypFTElNSU5BUiBBR1JJQ1VMVE9SL0dBTkFERVJPKi9cclxuICBjb25zdCBoYW5kbGVEZWxldGVDbGllbnRlID0gYXN5bmMgKGlkOiBudW1iZXIpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFwiQ2xpZW50ZSBhIGVsaW1pbmFyOlwiLCBpZCk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9hcGkvYWdyaWN1bHRvci8ke2lkfWAsIHtcclxuICAgICAgICBtZXRob2Q6IFwiREVMRVRFXCIsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlcy5vaykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiQ2xpZW50ZSBlbGltaW5hZG8gZXhpdG9zYW1lbnRlLlwiKTtcclxuICAgICAgICAvLyBBY3R1YWxpemFyIGVsIGVzdGFkbyBkZSBsYXMgZmlsYXMgZGVzcHXDqXMgZGUgZWxpbWluYXIgdW4gY2xpZW50ZVxyXG4gICAgICAgIGNvbnN0IGRhdGFDbGllbnRlcyA9IGF3YWl0IGZldGNoQ2xpZW50ZXMoKTtcclxuICAgICAgICBzZXRSb3dzKGRhdGFDbGllbnRlcyk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGFsIGVsaW1pbmFyIGVsIGNsaWVudGU6XCIsIGlkKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGVuIGxhIHNvbGljaXR1ZCBkZSBlbGltaW5hY2nDs246XCIsIGVycm9yKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvKkNMSUNLIEJPVE9OIE1PRElGSUNBUihMQVBJWikqL1xyXG4gIGNvbnN0IGhhbmRsZUVkaXQgPSBhc3luYyAoaWQ6IGFueSkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9hcGkvYWdyaWN1bHRvci8ke2lkfWAsIHtcclxuICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlcy5vaykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiQ2xpZW50ZSBvYnRlbmlkbyBleGl0b3NhbWVudGUuXCIpO1xyXG5cclxuICAgICAgICBjb25zdCBhZ3JpY3VsdG9yID0gYXdhaXQgcmVzLmpzb24oKTtcclxuICAgICAgICBzZXRGb3JtRGF0YSgocHJldkRhdGEpID0+ICh7XHJcbiAgICAgICAgICAuLi5wcmV2RGF0YSxcclxuICAgICAgICAgIGVtcHJlc2FSYXpvblNvY2lhbDogYWdyaWN1bHRvci5yYXpvblNvY2lhbCxcclxuICAgICAgICAgIGVtcHJlc2FUaXBvQ2xpZW50ZTogYWdyaWN1bHRvci50aXBvQ2xpZW50ZSB8fCBcIlwiLFxyXG4gICAgICAgICAgZW1wcmVzYURvbWljaWxpbzogYWdyaWN1bHRvci5kaXJlY2Npb24sXHJcbiAgICAgICAgICBlbXByZXNhTG9jYWxpZGFkOiBhZ3JpY3VsdG9yLmx1Z2FyLnNwbGl0KFwiIC0gXCIpWzBdLnRyaW0oKSxcclxuICAgICAgICAgIGVtcHJlc2FQcm92aW5jaWE6IGFncmljdWx0b3IubHVnYXIuc3BsaXQoXCIgLSBcIilbMV0udHJpbSgpLFxyXG4gICAgICAgICAgZW1wcmVzYUNvbmRGcmVudGVJdmE6IGFncmljdWx0b3IuY29uZEZyZW50ZUl2YSxcclxuICAgICAgICAgIGNvbnRhY3RvczogW1xyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgaWQ6IDEsXHJcbiAgICAgICAgICAgICAgbm9tYnJlOiBhZ3JpY3VsdG9yLm5vbWJyZUNvbnRhY3RvIHx8IFwiXCIsXHJcbiAgICAgICAgICAgICAgY2FyZ286IGFncmljdWx0b3IuY2FyZ29Db250YWN0byB8fCBcIlwiLFxyXG4gICAgICAgICAgICAgIHRlbGVmb25vOiBhZ3JpY3VsdG9yLnRlbGVmb25vLFxyXG4gICAgICAgICAgICAgIGVtYWlsOiBhZ3JpY3VsdG9yLm1haWwsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICBdLFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgYWwgbW9kaWZpY2FyIGVsIGNsaWVudGU6XCIsIGlkKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGVuIGxhIHNvbGljaXR1ZCBkZSBlbGltaW5hY2nDs246XCIsIGVycm9yKTtcclxuICAgIH1cclxuXHJcbiAgICBzZXRFc3RhZG9Nb2RhbChcInVwZGF0ZVwiKTtcclxuICAgIHNldE9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgLypNT0RJRklDQVIgQUdSSUNVTFRPUi9HQU5BREVSTyBQQVJBIEdBVVJEQVIqL1xyXG4gIGNvbnN0IGhhbmRsZVVwZGF0ZUNsaWVudGUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIXNlbGVjdGVkUm93KSByZXR1cm47XHJcbiAgICBjb25zdCBsdWdhciA9IGAke2Zvcm1EYXRhLmVtcHJlc2FMb2NhbGlkYWR9IC0gJHtmb3JtRGF0YS5lbXByZXNhUHJvdmluY2lhfWA7XHJcbiAgICBjb25zdCBuZXdQZXJzb246IENsaWVudCA9IHtcclxuICAgICAgaWQ6IHNlbGVjdGVkUm93LmlkLFxyXG5cclxuICAgICAgLy8gRGF0b3MgZGVsIFByb3BpZXRhcmlvL1BlcnNvbmEgRsOtc2ljYSAoUGVzdGHDsWEgMSlcclxuICAgICAgcHJvcGlldGFyaW9Ob21icmU6IGZvcm1EYXRhLnByb3BpZXRhcmlvTm9tYnJlLFxyXG4gICAgICBwcm9waWV0YXJpb0RvY3VtZW50bzogZm9ybURhdGEucHJvcGlldGFyaW9Eb2N1bWVudG8sXHJcbiAgICAgIHByb3BpZXRhcmlvVGVsZWZvbm86IGZvcm1EYXRhLnByb3BpZXRhcmlvVGVsZWZvbm8sXHJcbiAgICAgIHByb3BpZXRhcmlvRW1haWw6IGZvcm1EYXRhLnByb3BpZXRhcmlvRW1haWwsXHJcblxyXG4gICAgICAvLyBEYXRvcyBkZSBsYSBFbXByZXNhL1JhesOzbiBTb2NpYWwgKFBlc3Rhw7FhIDIpXHJcbiAgICAgIHJhem9uU29jaWFsOiBmb3JtRGF0YS5lbXByZXNhUmF6b25Tb2NpYWwsXHJcbiAgICAgIHRpcG9DbGllbnRlOiBmb3JtRGF0YS5lbXByZXNhVGlwb0NsaWVudGUsXHJcbiAgICAgIGRpcmVjY2lvbjogZm9ybURhdGEuZW1wcmVzYURvbWljaWxpbyxcclxuICAgICAgZW1wcmVzYUxvY2FsaWRhZDogZm9ybURhdGEuZW1wcmVzYUxvY2FsaWRhZCxcclxuICAgICAgcHJvdmluY2lhOiBmb3JtRGF0YS5lbXByZXNhUHJvdmluY2lhLFxyXG4gICAgICBjb25kRnJlbnRlSXZhOiBmb3JtRGF0YS5lbXByZXNhQ29uZEZyZW50ZUl2YSxcclxuXHJcbiAgICAgIC8vIERhdG9zIGRlbCBDb250YWN0by9FbmNhcmdhZG8gUHJpbmNpcGFsIChQZXN0YcOxYSAzKVxyXG4gICAgICBub21icmVDb250YWN0bzogZm9ybURhdGEuY29udGFjdG9zWzBdPy5ub21icmUgfHwgXCJcIixcclxuICAgICAgY2FyZ29Db250YWN0bzogZm9ybURhdGEuY29udGFjdG9zWzBdPy5jYXJnbyB8fCBcIlwiLFxyXG4gICAgICB0ZWxlZm9ubzogZm9ybURhdGEuY29udGFjdG9zWzBdPy50ZWxlZm9ubyB8fCBcIlwiLFxyXG4gICAgICBtYWlsOiBmb3JtRGF0YS5jb250YWN0b3NbMF0/LmVtYWlsIHx8IFwiXCIsXHJcblxyXG4gICAgICAvLyBDYW1wb3MgY2FsY3VsYWRvcy9kZXJpdmFkb3NcclxuICAgICAgbHVnYXI6IGx1Z2FyLFxyXG4gICAgICBkb2N1bWVudG86IGZvcm1EYXRhLnByb3BpZXRhcmlvRG9jdW1lbnRvLCAvLyBVc2FyIGVsIGRvY3VtZW50byBkZWwgcHJvcGlldGFyaW9cclxuICAgIH07XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9hcGkvYWdyaWN1bHRvcmAsIHtcclxuICAgICAgICBtZXRob2Q6IFwiUFVUXCIsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShuZXdQZXJzb24pLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmICghcmVzLm9rKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRXJyb3IgYWwgZ3VhcmRhciBlbCBjbGllbnRlXCIpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBVcGRhdGUgcm93cyB3aXRoIHByb3BlciB0eXBpbmdcclxuICAgICAgY29uc3QgdXBkYXRlZFJvd3MgPSByb3dzLm1hcCgocm93OiBDbGllbnQpID0+IHtcclxuICAgICAgICBpZiAocm93LmlkID09PSBuZXdQZXJzb24uaWQpIHtcclxuICAgICAgICAgIHJldHVybiBuZXdQZXJzb247XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiByb3c7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgc2V0Um93cyh1cGRhdGVkUm93cyk7XHJcbiAgICAgIGNsZWFyRnJvbSgpO1xyXG4gICAgICBzZXRPcGVuKGZhbHNlKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBlbiBsYSBzb2xpY2l0dWQ6XCIsIGVycm9yKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVMb2NhbGlkYWRLZXlEb3duID0gKFxyXG4gICAgZXZlbnQ6IFJlYWN0LktleWJvYXJkRXZlbnQ8SFRNTElucHV0RWxlbWVudD5cclxuICApID0+IHtcclxuICAgIGlmIChldmVudC5rZXkgPT09IFwiRW50ZXJcIiAmJiBzZWxlY3RQcm92aW5jaWFSZWYuY3VycmVudCkge1xyXG4gICAgICBzZWxlY3RQcm92aW5jaWFSZWYuY3VycmVudC5mb2N1cygpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVByb3ZpbmNpYUNoYW5nZSA9IChcclxuICAgIGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFNlbGVjdEVsZW1lbnQ+XHJcbiAgKSA9PiB7XHJcbiAgICBoYW5kbGVJbnB1dENoYW5nZShldmVudCk7XHJcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgaWYgKHNlbGVjdENvbmRJdmFSZWYuY3VycmVudCkge1xyXG4gICAgICAgIHNlbGVjdENvbmRJdmFSZWYuY3VycmVudC5mb2N1cygpO1xyXG4gICAgICB9XHJcbiAgICB9LCAwKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDb25kSXZhQ2hhbmdlID0gKFxyXG4gICAgZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudD5cclxuICApID0+IHtcclxuICAgIGhhbmRsZUlucHV0Q2hhbmdlKGV2ZW50KTtcclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICBpZiAoZG9jdW1lbnRvUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBkb2N1bWVudG9SZWYuY3VycmVudC5mb2N1cygpO1xyXG4gICAgICB9XHJcbiAgICB9LCAwKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTZWxlY3RBZ3JpY3VsdG9yID0gKGlkOiBudW1iZXIpID0+IHtcclxuICAgIGNvbnN0IHNlbGVjdGVkQWdyaWN1bHRvciA9IHJvd3MuZmluZCgocm93KSA9PiByb3cuaWQgPT09IGlkKTtcclxuXHJcbiAgICBpZiAoc2VsZWN0ZWRBZ3JpY3VsdG9yKSB7XHJcbiAgICAgIGNvbnN0IGFncmljdWx0b3IgPSB7XHJcbiAgICAgICAgaWQ6IHNlbGVjdGVkQWdyaWN1bHRvci5pZCxcclxuICAgICAgICByYXpvblNvY2lhbDogc2VsZWN0ZWRBZ3JpY3VsdG9yLnJhem9uU29jaWFsLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8gR3VhcmRhciBlbCBhZ3JpY3VsdG9yIHNlbGVjY2lvbmFkb1xyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcInNlbGVjdGVkQWdyaWN1bHRvclwiLCBKU09OLnN0cmluZ2lmeShhZ3JpY3VsdG9yKSk7XHJcblxyXG4gICAgICAvLyBSZWRpcmlnaXIgZGUgdnVlbHRhIGEgbGEgcMOhZ2luYSBkZSBlc3RhYmxlY2ltaWVudG9cclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBcIi9lc3RhYmxlY2ltaWVudG9cIjtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbcGFnZSwgc2V0UGFnZV0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBoYW5kbGVTZWFyY2hDaGFuZ2UgPSAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XHJcbiAgICBzZXRTZWFyY2hUZXJtKGV2ZW50LnRhcmdldC52YWx1ZSk7XHJcbiAgICBzZXRQYWdlKDApO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGxhYmVsU3R5bGVzID0ge1xyXG4gICAgZm9udFdlaWdodDogNjAwLFxyXG4gICAgY29sb3I6IFwiIzMzM1wiLFxyXG4gICAgbWFyZ2luQm90dG9tOiBcIjhweFwiLFxyXG4gICAgZGlzcGxheTogXCJibG9ja1wiLFxyXG4gICAgZm9udEZhbWlseTogXCJMZXhlbmQsIHNhbnMtc2VyaWZcIiwgLy8gQ2FtYmlhZG8gYSBMZXhlbmRcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPEJveFxyXG4gICAgICAgIHN4PXt7XHJcbiAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcInNwYWNlLWJldHdlZW5cIixcclxuICAgICAgICAgIG1iOiAzLFxyXG4gICAgICAgICAgbXQ6IDMsXHJcbiAgICAgICAgfX1cclxuICAgICAgPlxyXG4gICAgICAgIDxCb3g+XHJcbiAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICB2YXJpYW50PVwiaDRcIlxyXG4gICAgICAgICAgICBjb21wb25lbnQ9XCJkaXZcIlxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IFwiYm9sZFwiLFxyXG4gICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiTGV4ZW5kLCBzYW5zLXNlcmlmXCIsIC8vIENhbWJpYWRvIGEgTGV4ZW5kXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIEFncmljdWx0b3Jlc1xyXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgdmFyaWFudD1cInN1YnRpdGxlMVwiXHJcbiAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgY29sb3I6IFwidGV4dC5zZWNvbmRhcnlcIixcclxuICAgICAgICAgICAgICBtdDogMSxcclxuICAgICAgICAgICAgICBmb250RmFtaWx5OiBgJHtpbnRlci5zdHlsZS5mb250RmFtaWx5fWAsIC8vIENhbWJpYWRvIGEgSW50ZXJcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgR2VzdGlvbmUgbGEgaW5mb3JtYWNpw7NuIGRlIHN1cyBBZ3JpY3VsdG9yZXNcclxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICA8L0JveD5cclxuICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICB2YXJpYW50PVwiY29udGFpbmVkXCJcclxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU9wZW5BZGR9XHJcbiAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICBiZ2NvbG9yOiBcIiMyRTdEMzJcIiwgLy8gQ29sb3IgdmVyZGUgbcOhcyBvc2N1cm9cclxuICAgICAgICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxyXG4gICAgICAgICAgICBcIiY6aG92ZXJcIjogeyBiZ2NvbG9yOiBcIiMwRDlBMEFcIiB9LFxyXG4gICAgICAgICAgICBoZWlnaHQ6IFwiZml0LWNvbnRlbnRcIixcclxuICAgICAgICAgICAgYWxpZ25TZWxmOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICBmb250RmFtaWx5OiBgJHtpbnRlci5zdHlsZS5mb250RmFtaWx5fWAsIC8vIEFncmVnYWRvIHBhcmEgdXNhciBJbnRlclxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIHN0YXJ0SWNvbj17PEFkZE91dGxpbmVkSWNvbiAvPn1cclxuICAgICAgICA+XHJcbiAgICAgICAgICBOdWV2byBBZ3JpY3VsdG9yXHJcbiAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgIDwvQm94PlxyXG5cclxuICAgICAgPFBhcGVyIGVsZXZhdGlvbj17Mn0gc3g9e3sgcDogMiwgbWI6IDMsIGJvcmRlclJhZGl1czogMiB9fT5cclxuICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cIkJ1c2Nhci4uLlwiXHJcbiAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cclxuICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVTZWFyaENsaWVudGV9XHJcbiAgICAgICAgICBJbnB1dFByb3BzPXt7XHJcbiAgICAgICAgICAgIHN0YXJ0QWRvcm5tZW50OiAoXHJcbiAgICAgICAgICAgICAgPElucHV0QWRvcm5tZW50IHBvc2l0aW9uPVwic3RhcnRcIj5cclxuICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uIG9uQ2xpY2s9e2hhbmRsZVNlYXJjaENsaWNrfT5cclxuICAgICAgICAgICAgICAgICAgPFNlYXJjaEljb24gLz5cclxuICAgICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cclxuICAgICAgICAgICAgICA8L0lucHV0QWRvcm5tZW50PlxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIHN4PXt7IG1iOiAyIH19XHJcbiAgICAgICAgLz5cclxuICAgICAgICA8RGF0YXRhYmxlXHJcbiAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgcm93cz17ZmlsdGVyZWRSb3dzLmxlbmd0aCA+IDAgPyBmaWx0ZXJlZFJvd3MgOiByb3dzfVxyXG4gICAgICAgICAgb3B0aW9uPXt0cnVlfVxyXG4gICAgICAgICAgb3B0aW9uRGVsZXRlRnVuY3Rpb249e2hhbmRsZURlbGV0ZUNsaWVudGV9XHJcbiAgICAgICAgICBvcHRpb25VcGRhdGVGdW5jdGlvbj17aGFuZGxlRWRpdH1cclxuICAgICAgICAgIHNldFNlbGVjdGVkUm93PXsocm93KSA9PiBzZXRTZWxlY3RlZFJvdyhyb3cgYXMgQ2xpZW50IHwgbnVsbCl9XHJcbiAgICAgICAgICBzZWxlY3RlZFJvdz17c2VsZWN0ZWRSb3d9XHJcbiAgICAgICAgICBvcHRpb25TZWxlY3Q9e2hhbmRsZVNlbGVjdEFncmljdWx0b3J9XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9QYXBlcj5cclxuXHJcbiAgICAgIDxEaWFsb2dcclxuICAgICAgICBvcGVuPXtvcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9e2hhbmRsZUNsaWNrQ2xvc2V9XHJcbiAgICAgICAgbWF4V2lkdGg9XCJsZ1wiXHJcbiAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgc3g9e3tcclxuICAgICAgICAgIFwiJiAuTXVpRGlhbG9nLXBhcGVyXCI6IHtcclxuICAgICAgICAgICAgd2lkdGg6IFwiMTEwMHB4XCIsIC8vIEFuY2hvIGFtcGxpYWRvIGRlIDgyNXB4IGEgMTEwMHB4XHJcbiAgICAgICAgICAgIG1heFdpZHRoOiBcIjk1dndcIiwgLy8gTWVqb3IgYXByb3ZlY2hhbWllbnRvIGRlbCB2aWV3cG9ydFxyXG4gICAgICAgICAgICBtaW5IZWlnaHQ6IFwiNjAwcHhcIiwgLy8gQWx0dXJhIG3DrW5pbWEgcGFyYSBtZWpvciBwcm9wb3JjacOzblxyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAgPEJveCBzeD17eyBwOiA0IH19PlxyXG4gICAgICAgICAgey8qIFBhZGRpbmcgYW1wbGlhZG8gcGFyYSBtZWpvciBlc3BhY2lhZG8gKi99XHJcbiAgICAgICAgICA8Qm94IHN4PXt7IG1iOiAyIH19PlxyXG4gICAgICAgICAgICA8RGlhbG9nVGl0bGVcclxuICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgcDogMCxcclxuICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiTGV4ZW5kLCBzYW5zLXNlcmlmXCIsXHJcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxLjVyZW1cIixcclxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IFwiYm9sZFwiLFxyXG4gICAgICAgICAgICAgICAgY29sb3I6IFwiIzMzM1wiLFxyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBSZWdpc3RyYXIgbnVldm8gYWdyaWN1bHRvci9nYW5hZGVyb1xyXG4gICAgICAgICAgICA8L0RpYWxvZ1RpdGxlPlxyXG4gICAgICAgICAgICA8RGlhbG9nQ29udGVudFRleHRcclxuICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgcDogMCxcclxuICAgICAgICAgICAgICAgIG10OiAxLFxyXG4gICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiLFxyXG4gICAgICAgICAgICAgICAgY29sb3I6IFwiIzY2NlwiLFxyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBDb21wbGV0ZSBsYSBpbmZvcm1hY2nDs24gZGVsIG51ZXZvIGFncmljdWx0b3IvZ2FuYWRlcm8gYSByZWdpc3RyYXIuXHJcbiAgICAgICAgICAgIDwvRGlhbG9nQ29udGVudFRleHQ+XHJcbiAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgIDxJY29uQnV0dG9uXHJcbiAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJjbG9zZVwiXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eyhldmVudCkgPT4gaGFuZGxlQ2xpY2tDbG9zZShldmVudCwgXCJjbG9zZUJ1dHRvbkNsaWNrXCIpfVxyXG4gICAgICAgICAgICBzeD17eyBwb3NpdGlvbjogXCJhYnNvbHV0ZVwiLCByaWdodDogOCwgdG9wOiA4IH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxDbG9zZUljb24gLz5cclxuICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cclxuICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgY29tcG9uZW50PVwiZm9ybVwiXHJcbiAgICAgICAgICAgIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8RGlhbG9nQ29udGVudCBzeD17eyBwOiAwIH19PlxyXG4gICAgICAgICAgICAgIHsvKiBUYWJzIHBhcmEgb3JnYW5pemFyIGxhIGluZm9ybWFjacOzbiAqL31cclxuICAgICAgICAgICAgICA8Qm94IHN4PXt7IGJvcmRlckJvdHRvbTogMSwgYm9yZGVyQ29sb3I6IFwiZGl2aWRlclwiLCBtYjogMyB9fT5cclxuICAgICAgICAgICAgICAgIDxUYWJzXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXt0YWJWYWx1ZX1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhldmVudCwgbmV3VmFsdWUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBTb2xvIHBlcm1pdGlyIGNhbWJpYXIgYSBwZXN0YcOxYXMgaGFiaWxpdGFkYXNcclxuICAgICAgICAgICAgICAgICAgICBpZiAoZW5hYmxlZFRhYnNbbmV3VmFsdWVdKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzZXRUYWJWYWx1ZShuZXdWYWx1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgIFwiJiAuTXVpVGFiLXJvb3RcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJMZXhlbmQsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgICAgICAgICAgICAgICAgIHRleHRUcmFuc2Zvcm06IFwibm9uZVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMXJlbVwiLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJcclxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIvCfkaQgUHJvcGlldGFyaW8vUGVyc29uYSBGw61zaWNhXCJcclxuICAgICAgICAgICAgICAgICAgICBzeD17eyBtaW5XaWR0aDogMjIwIH19XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFlbmFibGVkVGFic1swXX1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPFRhYlxyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwi8J+PoiBFbXByZXNhL1JhesOzbiBTb2NpYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgIHN4PXt7IG1pbldpZHRoOiAyMDAgfX1cclxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWVuYWJsZWRUYWJzWzFdfVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8VGFiXHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCLwn5OeIENvbnRhY3RvL0VuY2FyZ2Fkb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgc3g9e3sgbWluV2lkdGg6IDIwMCB9fVxyXG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshZW5hYmxlZFRhYnNbMl19XHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L1RhYnM+XHJcbiAgICAgICAgICAgICAgPC9Cb3g+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBDb250ZW5pZG8gZGUgbGFzIHBlc3Rhw7FhcyAqL31cclxuICAgICAgICAgICAgICB7dGFiVmFsdWUgPT09IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgPEJveCBzeD17eyBtdDogMyB9fT5cclxuICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiaDZcIlxyXG4gICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICBtYjogMyxcclxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiTGV4ZW5kLCBzYW5zLXNlcmlmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjMzMzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIERhdG9zIGRlbCBQcm9waWV0YXJpby9QZXJzb25hIEbDrXNpY2FcclxuICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICA8R3JpZCBjb250YWluZXIgc3BhY2luZz17M30+XHJcbiAgICAgICAgICAgICAgICAgICAgey8qIE5vbWJyZSBkZWwgUHJvcGlldGFyaW8gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgc3g9e2xhYmVsU3R5bGVzfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgTm9tYnJlIENvbXBsZXRvICpcclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0RmllbGRcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFajogSnVhbiBDYXJsb3MgUMOpcmV6XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInByb3BpZXRhcmlvTm9tYnJlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3I9e0Jvb2xlYW4oZXJyb3IucHJvcGlldGFyaW9Ob21icmUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWxwZXJUZXh0PXtlcnJvci5wcm9waWV0YXJpb05vbWJyZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucHJvcGlldGFyaW9Ob21icmV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cImVuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybURhdGEucHJvcGlldGFyaW9Ob21icmUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZXJyb3IucHJvcGlldGFyaW9Ob21icmUgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXJyb3JJY29uIGNvbG9yPVwiZXJyb3JcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNvbG9yPVwic3VjY2Vzc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0lucHV0QWRvcm5tZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIERvY3VtZW50byBkZWwgUHJvcGlldGFyaW8gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgc3g9e2xhYmVsU3R5bGVzfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgRE5JL0NVSVQgKlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVqOiAyMC0xMjM0NTY3OC05XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInByb3BpZXRhcmlvRG9jdW1lbnRvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3I9e0Jvb2xlYW4oZXJyb3IucHJvcGlldGFyaW9Eb2N1bWVudG8pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWxwZXJUZXh0PXtlcnJvci5wcm9waWV0YXJpb0RvY3VtZW50b31cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucHJvcGlldGFyaW9Eb2N1bWVudG99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cImVuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybURhdGEucHJvcGlldGFyaW9Eb2N1bWVudG8gJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZXJyb3IucHJvcGlldGFyaW9Eb2N1bWVudG8gPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXJyb3JJY29uIGNvbG9yPVwiZXJyb3JcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNvbG9yPVwic3VjY2Vzc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0lucHV0QWRvcm5tZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIFRlbMOpZm9ubyBkZWwgUHJvcGlldGFyaW8gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgc3g9e2xhYmVsU3R5bGVzfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgVGVsw6lmb25vIFBlcnNvbmFsXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWo6IDAwMDAtMDAwMDAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInByb3BpZXRhcmlvVGVsZWZvbm9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihlcnJvci5wcm9waWV0YXJpb1RlbGVmb25vKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaGVscGVyVGV4dD17ZXJyb3IucHJvcGlldGFyaW9UZWxlZm9ub31cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucHJvcGlldGFyaW9UZWxlZm9ub31cclxuICAgICAgICAgICAgICAgICAgICAgICAgSW5wdXRQcm9wcz17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGVuZEFkb3JubWVudDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0QWRvcm5tZW50IHBvc2l0aW9uPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS5wcm9waWV0YXJpb1RlbGVmb25vICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVycm9yLnByb3BpZXRhcmlvVGVsZWZvbm8gPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXJyb3JJY29uIGNvbG9yPVwiZXJyb3JcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNvbG9yPVwic3VjY2Vzc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0lucHV0QWRvcm5tZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIEVtYWlsIGRlbCBQcm9waWV0YXJpbyAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBFbWFpbCBQZXJzb25hbFxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVqOiBqdWFuLnBlcmV6QGdtYWlsLmNvbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwcm9waWV0YXJpb0VtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKGVycm9yLnByb3BpZXRhcmlvRW1haWwpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWxwZXJUZXh0PXtlcnJvci5wcm9waWV0YXJpb0VtYWlsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wcm9waWV0YXJpb0VtYWlsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBJbnB1dFByb3BzPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZW5kQWRvcm5tZW50OiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRBZG9ybm1lbnQgcG9zaXRpb249XCJlbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1EYXRhLnByb3BpZXRhcmlvRW1haWwgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZXJyb3IucHJvcGlldGFyaW9FbWFpbCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFcnJvckljb24gY29sb3I9XCJlcnJvclwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY29sb3I9XCJzdWNjZXNzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5wdXRBZG9ybm1lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogQ2hlY2tib3ggcGFyYSB1c2FyIGNvbW8gcmF6w7NuIHNvY2lhbCAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtdDogMixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBwOiAyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJnY29sb3I6IFwiI2Y1ZjVmNVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogMSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogXCJwb2ludGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2Zvcm1EYXRhLnVzYXJDb21vUmF6b25Tb2NpYWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY2hlY2tlZCA9IGUudGFyZ2V0LmNoZWNrZWQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKChwcmV2KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNhckNvbW9SYXpvblNvY2lhbDogY2hlY2tlZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBTaSBzZSBtYXJjYSwgY29waWFyIGRhdG9zIGRlbCBwcm9waWV0YXJpbyBhIGxhIGVtcHJlc2FcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbXByZXNhUmF6b25Tb2NpYWw6IGNoZWNrZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcHJldi5wcm9waWV0YXJpb05vbWJyZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRhbWJpw6luIGNvcGlhciBlbCBwcmltZXIgY29udGFjdG8gY29uIGxvcyBkYXRvcyBkZWwgcHJvcGlldGFyaW9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250YWN0b3M6IGNoZWNrZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYuY29udGFjdG9zWzBdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbm9tYnJlOiBwcmV2LnByb3BpZXRhcmlvTm9tYnJlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGVsZWZvbm86IHByZXYucHJvcGlldGFyaW9UZWxlZm9ubyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVtYWlsOiBwcmV2LnByb3BpZXRhcmlvRW1haWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXJnbzpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJldi5jb250YWN0b3NbMF0/LmNhcmdvIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiUHJvcGlldGFyaW9cIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYuY29udGFjdG9zLnNsaWNlKDEpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHByZXYuY29udGFjdG9zLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgbWFyZ2luUmlnaHQ6IFwiOHB4XCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiYm9keTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAg4pyFIFVzYXIgZXN0b3MgZGF0b3MgZGVsIHByb3BpZXRhcmlvIHBhcmEgbGEgZW1wcmVzYVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeSBjb250YWN0byBwcmluY2lwYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS51c2FyQ29tb1Jhem9uU29jaWFsICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImNhcHRpb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzJFN0QzMlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtdDogMSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJibG9ja1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U3R5bGU6IFwiaXRhbGljXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfkqEgTG9zIGRhdG9zIGRlbCBwcm9waWV0YXJpbyBzZSBzaW5jcm9uaXphcsOhblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b23DoXRpY2FtZW50ZSBjb24gbGEgcmF6w7NuIHNvY2lhbCBkZSBsYSBlbXByZXNhIHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsIGNvbnRhY3RvIHByaW5jaXBhbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIEJvdG9uZXMgZGUgbmF2ZWdhY2nDs24gcGFyYSBwZXN0YcOxYSAxICovfVxyXG4gICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwic3BhY2UtYmV0d2VlblwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgbXQ6IDMsXHJcbiAgICAgICAgICAgICAgICAgICAgICBwdDogMixcclxuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclRvcDogXCIxcHggc29saWQgI2UwZTBlMFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGV2ZW50KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVDbGlja0Nsb3NlKGV2ZW50LCBcImNsb3NlQnV0dG9uQ2xpY2tcIilcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IG1pbldpZHRoOiAxMjAgfX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxhclxyXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJjb250YWluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTmV4dFRhYn1cclxuICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiAxMjAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJnY29sb3I6IFwiIzJFN0QzMlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBcIiY6aG92ZXJcIjogeyBiZ2NvbG9yOiBcIiMxQjVFMjBcIiB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBTaWd1aWVudGVcclxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICB7LyogUGVzdGHDsWEgMjogRW1wcmVzYS9SYXrDs24gU29jaWFsICovfVxyXG4gICAgICAgICAgICAgIHt0YWJWYWx1ZSA9PT0gMSAmJiAoXHJcbiAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IG10OiAzIH19PlxyXG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJoNlwiXHJcbiAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgIG1iOiAzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJMZXhlbmQsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiMzMzNcIixcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgRGF0b3MgZGUgbGEgRW1wcmVzYS9SYXrDs24gU29jaWFsXHJcbiAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezN9PlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBSYXrDs24gU29jaWFsICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxHcmlkIHNpemU9e3sgeHM6IDEyLCBzbTogNiB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFJhesOzbiBTb2NpYWwgKlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVqOiBBZ3JvcGVjdWFyaWEgU2FuIEp1YW4gUy5BLlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJlbXByZXNhUmF6b25Tb2NpYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihlcnJvci5lbXByZXNhUmF6b25Tb2NpYWwpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWxwZXJUZXh0PXtlcnJvci5lbXByZXNhUmF6b25Tb2NpYWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmVtcHJlc2FSYXpvblNvY2lhbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2Zvcm1EYXRhLnVzYXJDb21vUmF6b25Tb2NpYWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cImVuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybURhdGEuZW1wcmVzYVJhem9uU29jaWFsICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVycm9yLmVtcHJlc2FSYXpvblNvY2lhbCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFcnJvckljb24gY29sb3I9XCJlcnJvclwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY29sb3I9XCJzdWNjZXNzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5wdXRBZG9ybm1lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aUlucHV0QmFzZS1pbnB1dFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGZvcm1EYXRhLnVzYXJDb21vUmF6b25Tb2NpYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIiNmNWY1ZjVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidHJhbnNwYXJlbnRcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS51c2FyQ29tb1Jhem9uU29jaWFsICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY2FwdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgY29sb3I6IFwiIzY2NlwiLCBtdDogMSwgZGlzcGxheTogXCJibG9ja1wiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICDwn5KhIEF1dG9jb21wbGV0YWRvIGRlc2RlIGRhdG9zIGRlbCBwcm9waWV0YXJpb1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIFRpcG8gZGUgQ2xpZW50ZSAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBUaXBvIGRlIENsaWVudGUgKlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihlcnJvci5lbXByZXNhVGlwb0NsaWVudGUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImVtcHJlc2FUaXBvQ2xpZW50ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmVtcHJlc2FUaXBvQ2xpZW50ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGV2ZW50OiBTZWxlY3RDaGFuZ2VFdmVudDxzdHJpbmc+KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzeW50aGV0aWNFdmVudCA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogXCJlbXByZXNhVGlwb0NsaWVudGVcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogZXZlbnQudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBhcyBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MU2VsZWN0RWxlbWVudD47XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJbnB1dENoYW5nZShzeW50aGV0aWNFdmVudCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5RW1wdHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICByZW5kZXJWYWx1ZT17KHNlbGVjdGVkKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXNlbGVjdGVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgY29sb3I6IFwiIzk5OVwiIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU2VsZWNjaW9uZSB0aXBvIGRlIGNsaWVudGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBvcHRpb24gPSB0aXBvQ2xpZW50ZU9wdGlvbnMuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKG9wdCkgPT4gb3B0LnZhbHVlID09PSBzZWxlY3RlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBvcHRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgJHtvcHRpb24uaWNvbn0gJHtvcHRpb24udmFsdWV9YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHNlbGVjdGVkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogXCI1NnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aVNlbGVjdC1zZWxlY3RcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjE2LjVweCAxNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogXCIxLjQzNzVlbVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSB2YWx1ZT1cIlwiIGRpc2FibGVkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgU2VsZWNjaW9uZSB0aXBvIGRlIGNsaWVudGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0aXBvQ2xpZW50ZU9wdGlvbnMubWFwKChvcHRpb24pID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSBrZXk9e29wdGlvbi52YWx1ZX0gdmFsdWU9e29wdGlvbi52YWx1ZX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAxLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57b3B0aW9uLmljb259PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntvcHRpb24udmFsdWV9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3IuZW1wcmVzYVRpcG9DbGllbnRlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUhlbHBlclRleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3IuZW1wcmVzYVRpcG9DbGllbnRlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvRm9ybUhlbHBlclRleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0Zvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIERvbWljaWxpbyAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIERvbWljaWxpbyBkZSBsYSBFbXByZXNhXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWo6IEF2LiBTYW4gTWFydMOtbiAxMjM0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImVtcHJlc2FEb21pY2lsaW9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihlcnJvci5lbXByZXNhRG9taWNpbGlvKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaGVscGVyVGV4dD17ZXJyb3IuZW1wcmVzYURvbWljaWxpb31cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZW1wcmVzYURvbWljaWxpb31cclxuICAgICAgICAgICAgICAgICAgICAgICAgSW5wdXRQcm9wcz17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGVuZEFkb3JubWVudDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0QWRvcm5tZW50IHBvc2l0aW9uPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS5lbXByZXNhRG9taWNpbGlvICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVycm9yLmVtcHJlc2FEb21pY2lsaW8gPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXJyb3JJY29uIGNvbG9yPVwiZXJyb3JcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNvbG9yPVwic3VjY2Vzc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0lucHV0QWRvcm5tZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIExvY2FsaWRhZCB5IFByb3ZpbmNpYSAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBMb2NhbGlkYWQgKlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVqOiBNZXJjZWRlc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJlbXByZXNhTG9jYWxpZGFkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3I9e0Jvb2xlYW4oZXJyb3IuZW1wcmVzYUxvY2FsaWRhZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlbHBlclRleHQ9e2Vycm9yLmVtcHJlc2FMb2NhbGlkYWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmVtcHJlc2FMb2NhbGlkYWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cImVuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybURhdGEuZW1wcmVzYUxvY2FsaWRhZCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlcnJvci5lbXByZXNhTG9jYWxpZGFkID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVycm9ySWNvbiBjb2xvcj1cImVycm9yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjb2xvcj1cInN1Y2Nlc3NcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxHcmlkIHNpemU9e3sgeHM6IDEyLCBzbTogNiB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFByb3ZpbmNpYSAqXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUNvbnRyb2xcclxuICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKGVycm9yLmVtcHJlc2FQcm92aW5jaWEpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImVtcHJlc2FQcm92aW5jaWFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbXByZXNhUHJvdmluY2lhfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZXZlbnQ6IFNlbGVjdENoYW5nZUV2ZW50PHN0cmluZz4pID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHN5bnRoZXRpY0V2ZW50ID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBcImVtcHJlc2FQcm92aW5jaWFcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogZXZlbnQudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBhcyBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MU2VsZWN0RWxlbWVudD47XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJbnB1dENoYW5nZShzeW50aGV0aWNFdmVudCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5RW1wdHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICByZW5kZXJWYWx1ZT17KHNlbGVjdGVkKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXNlbGVjdGVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgY29sb3I6IFwiIzk5OVwiIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU2VsZWNjaW9uZSBwcm92aW5jaWFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VsZWN0ZWQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBcIjU2cHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiJiAuTXVpU2VsZWN0LXNlbGVjdFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IFwiMTYuNXB4IDE0cHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBcIjEuNDM3NWVtXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIHZhbHVlPVwiXCIgZGlzYWJsZWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBTZWxlY2Npb25lIHByb3ZpbmNpYVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3ZpbmNpYXMubWFwKChwcm92aW5jaWEpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSBrZXk9e3Byb3ZpbmNpYX0gdmFsdWU9e3Byb3ZpbmNpYX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm92aW5jaWF9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2Vycm9yLmVtcHJlc2FQcm92aW5jaWEgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxGb3JtSGVscGVyVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtlcnJvci5lbXByZXNhUHJvdmluY2lhfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvRm9ybUhlbHBlclRleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0Zvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIENvbmRpY2nDs24gZnJlbnRlIGFsIElWQSAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBDb25kaWNpw7NuIGZyZW50ZSBhbCBJVkEgKlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihlcnJvci5lbXByZXNhQ29uZEZyZW50ZUl2YSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwiZW1wcmVzYUNvbmRGcmVudGVJdmFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbXByZXNhQ29uZEZyZW50ZUl2YX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGV2ZW50OiBTZWxlY3RDaGFuZ2VFdmVudDxzdHJpbmc+KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzeW50aGV0aWNFdmVudCA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogXCJlbXByZXNhQ29uZEZyZW50ZUl2YVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBldmVudC50YXJnZXQudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGFzIFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxTZWxlY3RFbGVtZW50PjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUlucHV0Q2hhbmdlKHN5bnRoZXRpY0V2ZW50KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXlFbXB0eVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJlbmRlclZhbHVlPXsoc2VsZWN0ZWQpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghc2VsZWN0ZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBjb2xvcjogXCIjOTk5XCIgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTZWxlY2Npb25lIGNvbmRpY2nDs24gSVZBXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNlbGVjdGVkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogXCI1NnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aVNlbGVjdC1zZWxlY3RcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjE2LjVweCAxNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogXCIxLjQzNzVlbVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSB2YWx1ZT1cIlwiIGRpc2FibGVkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgU2VsZWNjaW9uZSBjb25kaWNpw7NuIGZyZW50ZSBhbCBJVkFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb25kRnJlbnRlSXZhT3B0aW9ucy5tYXAoKGNvbmRpY2lvbikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIGtleT17Y29uZGljaW9ufSB2YWx1ZT17Y29uZGljaW9ufT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbmRpY2lvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3IuZW1wcmVzYUNvbmRGcmVudGVJdmEgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxGb3JtSGVscGVyVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtlcnJvci5lbXByZXNhQ29uZEZyZW50ZUl2YX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0Zvcm1IZWxwZXJUZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgIHsvKiBCb3RvbmVzIGRlIG5hdmVnYWNpw7NuIHBhcmEgcGVzdGHDsWEgMiAqL31cclxuICAgICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcInNwYWNlLWJldHdlZW5cIixcclxuICAgICAgICAgICAgICAgICAgICAgIG10OiAzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgcHQ6IDIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXJUb3A6IFwiMXB4IHNvbGlkICNlMGUwZTBcIixcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVByZXZpb3VzVGFifVxyXG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3sgbWluV2lkdGg6IDEyMCB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIEFudGVyaW9yXHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImNvbnRhaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVOZXh0VGFifVxyXG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IDEyMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmdjb2xvcjogXCIjMkU3RDMyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiJjpob3ZlclwiOiB7IGJnY29sb3I6IFwiIzFCNUUyMFwiIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIFNpZ3VpZW50ZVxyXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBQZXN0YcOxYSAzOiBDb250YWN0by9FbmNhcmdhZG8gKi99XHJcbiAgICAgICAgICAgICAge3RhYlZhbHVlID09PSAyICYmIChcclxuICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgbXQ6IDMgfX0+XHJcbiAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImg2XCJcclxuICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgbWI6IDMsXHJcbiAgICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIkxleGVuZCwgc2Fucy1zZXJpZlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzMzM1wiLFxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBDb250YWN0b3MvRW5jYXJnYWRvc1xyXG4gICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcblxyXG4gICAgICAgICAgICAgICAgICB7LyogTGlzdGEgZGluw6FtaWNhIGRlIGNvbnRhY3RvcyAqL31cclxuICAgICAgICAgICAgICAgICAge2Zvcm1EYXRhLmNvbnRhY3Rvcy5tYXAoKGNvbnRhY3RvLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgIGtleT17Y29udGFjdG8uaWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYjogNCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcDogMyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiBcIjFweCBzb2xpZCAjZTBlMGUwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogMixcclxuICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcInNwYWNlLWJldHdlZW5cIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1iOiAyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgZGlzcGxheTogXCJmbGV4XCIsIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsIGdhcDogMSB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFBlcnNvbkljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiMyRTdEMzJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMS4ycmVtXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdWJ0aXRsZTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgZm9udFdlaWdodDogNjAwLCBjb2xvcjogXCIjMzMzXCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDb250YWN0byAje2luZGV4ICsgMX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybURhdGEuY29udGFjdG9zLmxlbmd0aCA+IDEgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cImVycm9yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKChwcmV2KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGFjdG9zOiBwcmV2LmNvbnRhY3Rvcy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoYykgPT4gYy5pZCAhPT0gY29udGFjdG8uaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEVsaW1pbmFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICA8R3JpZCBjb250YWluZXIgc3BhY2luZz17M30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBOb21icmUgZGVsIGNvbnRhY3RvICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgc3g9e2xhYmVsU3R5bGVzfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5vbWJyZSBkZWwgRW5jYXJnYWRvICpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFajogTWFyw61hIEdvbnrDoWxlelwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT17YGNvbnRhY3RvXyR7Y29udGFjdG8uaWR9X25vbWJyZWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JbYGNvbnRhY3RvXyR7Y29udGFjdG8uaWR9X25vbWJyZWBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVscGVyVGV4dD17ZXJyb3JbYGNvbnRhY3RvXyR7Y29udGFjdG8uaWR9X25vbWJyZWBdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0NvbnRhY3RvcyA9IFsuLi5mb3JtRGF0YS5jb250YWN0b3NdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb250YWN0b0luZGV4ID0gbmV3Q29udGFjdG9zLmZpbmRJbmRleChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoYykgPT4gYy5pZCA9PT0gY29udGFjdG8uaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3Q29udGFjdG9zW2NvbnRhY3RvSW5kZXhdLm5vbWJyZSA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQudmFsdWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKChwcmV2KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGFjdG9zOiBuZXdDb250YWN0b3MsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGFjdG8ubm9tYnJlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgSW5wdXRQcm9wcz17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRBZG9ybm1lbnQgcG9zaXRpb249XCJlbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb250YWN0by5ub21icmUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVycm9yW2Bjb250YWN0b18ke2NvbnRhY3RvLmlkfV9ub21icmVgXSA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXJyb3JJY29uIGNvbG9yPVwiZXJyb3JcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY29sb3I9XCJzdWNjZXNzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIENhcmdvIGRlbCBjb250YWN0byAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDYXJnbyAoT3BjaW9uYWwpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0RmllbGRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWo6IEFkbWluaXN0cmFkb3IsIEVuY2FyZ2Fkb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT17YGNvbnRhY3RvXyR7Y29udGFjdG8uaWR9X2NhcmdvYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0NvbnRhY3RvcyA9IFsuLi5mb3JtRGF0YS5jb250YWN0b3NdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb250YWN0b0luZGV4ID0gbmV3Q29udGFjdG9zLmZpbmRJbmRleChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoYykgPT4gYy5pZCA9PT0gY29udGFjdG8uaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3Q29udGFjdG9zW2NvbnRhY3RvSW5kZXhdLmNhcmdvID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC52YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEoKHByZXYpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250YWN0b3M6IG5ld0NvbnRhY3RvcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250YWN0by5jYXJnb31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogVGVsw6lmb25vIGRlbCBjb250YWN0byAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBUZWzDqWZvbm8gZGUgQ29udGFjdG9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFajogMDAwMC0wMDAwMDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9e2Bjb250YWN0b18ke2NvbnRhY3RvLmlkfV90ZWxlZm9ub2B9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcltgY29udGFjdG9fJHtjb250YWN0by5pZH1fdGVsZWZvbm9gXVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlbHBlclRleHQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcltgY29udGFjdG9fJHtjb250YWN0by5pZH1fdGVsZWZvbm9gXVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0NvbnRhY3RvcyA9IFsuLi5mb3JtRGF0YS5jb250YWN0b3NdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb250YWN0b0luZGV4ID0gbmV3Q29udGFjdG9zLmZpbmRJbmRleChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoYykgPT4gYy5pZCA9PT0gY29udGFjdG8uaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3Q29udGFjdG9zW2NvbnRhY3RvSW5kZXhdLnRlbGVmb25vID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC52YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEoKHByZXYpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250YWN0b3M6IG5ld0NvbnRhY3RvcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250YWN0by50ZWxlZm9ub31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW5kQWRvcm5tZW50OiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0QWRvcm5tZW50IHBvc2l0aW9uPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29udGFjdG8udGVsZWZvbm8gJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVycm9yW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGBjb250YWN0b18ke2NvbnRhY3RvLmlkfV90ZWxlZm9ub2BcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXSA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXJyb3JJY29uIGNvbG9yPVwiZXJyb3JcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY29sb3I9XCJzdWNjZXNzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIEVtYWlsIGRlbCBjb250YWN0byAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBFbWFpbCBkZSBDb250YWN0b1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVqOiBtYXJpYUBlbXByZXNhLmNvbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT17YGNvbnRhY3RvXyR7Y29udGFjdG8uaWR9X2VtYWlsYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcltgY29udGFjdG9fJHtjb250YWN0by5pZH1fZW1haWxgXVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlbHBlclRleHQ9e2Vycm9yW2Bjb250YWN0b18ke2NvbnRhY3RvLmlkfV9lbWFpbGBdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0NvbnRhY3RvcyA9IFsuLi5mb3JtRGF0YS5jb250YWN0b3NdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb250YWN0b0luZGV4ID0gbmV3Q29udGFjdG9zLmZpbmRJbmRleChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoYykgPT4gYy5pZCA9PT0gY29udGFjdG8uaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3Q29udGFjdG9zW2NvbnRhY3RvSW5kZXhdLmVtYWlsID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC52YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEoKHByZXYpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250YWN0b3M6IG5ld0NvbnRhY3RvcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250YWN0by5lbWFpbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW5kQWRvcm5tZW50OiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0QWRvcm5tZW50IHBvc2l0aW9uPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29udGFjdG8uZW1haWwgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVycm9yW2Bjb250YWN0b18ke2NvbnRhY3RvLmlkfV9lbWFpbGBdID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFcnJvckljb24gY29sb3I9XCJlcnJvclwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjb2xvcj1cInN1Y2Nlc3NcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0lucHV0QWRvcm5tZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIEJvdMOzbiBwYXJhIGFncmVnYXIgbcOhcyBjb250YWN0b3MgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgIHN0YXJ0SWNvbj17PEFkZENpcmNsZUljb24gLz59XHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3SWQgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBNYXRoLm1heCguLi5mb3JtRGF0YS5jb250YWN0b3MubWFwKChjKSA9PiBjLmlkKSkgKyAxO1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEoKHByZXYpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRhY3RvczogW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYuY29udGFjdG9zLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBuZXdJZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5vbWJyZTogXCJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhcmdvOiBcIlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGVsZWZvbm86IFwiXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbWFpbDogXCJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgfSkpO1xyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgc3g9e3sgbXQ6IDIgfX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIEFncmVnYXIgb3RybyBjb250YWN0b1xyXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIHsvKiBCb3RvbmVzIGRlIG5hdmVnYWNpw7NuIHBhcmEgcGVzdGHDsWEgMyAqL31cclxuICAgICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcInNwYWNlLWJldHdlZW5cIixcclxuICAgICAgICAgICAgICAgICAgICAgIG10OiAzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgcHQ6IDIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXJUb3A6IFwiMXB4IHNvbGlkICNlMGUwZTBcIixcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVByZXZpb3VzVGFifVxyXG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3sgbWluV2lkdGg6IDEyMCB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIEFudGVyaW9yXHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImNvbnRhaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcclxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZGRDbGllbnRlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IDEyMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmdjb2xvcjogXCIjMkU3RDMyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiJjpob3ZlclwiOiB7IGJnY29sb3I6IFwiIzFCNUUyMFwiIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHtpc1N1Ym1pdHRpbmdcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcIkd1YXJkYW5kby4uLlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogZXN0YWRvTW9kYWwgPT09IFwiYWRkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcIlJlZ2lzdHJhclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJBY3R1YWxpemFyXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgPC9Cb3g+XHJcbiAgICAgIDwvRGlhbG9nPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFncmljdWx0b3JHYW5hZGVybztcclxuIl0sIm5hbWVzIjpbImludGVyIiwiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIkFkZE91dGxpbmVkSWNvbiIsIkRhdGFHcmlkIiwiTXVpRGF0YUdyaWQiLCJCdXR0b24iLCJHcmlkIiwiSW5wdXRBZG9ybm1lbnQiLCJQYXBlciIsIlRhYnMiLCJUYWIiLCJEaWFsb2ciLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nVGl0bGUiLCJEaWFsb2dDb250ZW50VGV4dCIsIkZvcm1Db250cm9sIiwiRm9ybUhlbHBlclRleHQiLCJJY29uQnV0dG9uIiwiTWVudUl0ZW0iLCJTZWxlY3QiLCJUZXh0RmllbGQiLCJUeXBvZ3JhcGh5IiwiRGF0YXRhYmxlIiwiQ2xvc2VJY29uIiwiQm94IiwiQ2hlY2tDaXJjbGVJY29uIiwiRXJyb3JJY29uIiwiU2VhcmNoIiwiU2VhcmNoSWNvbiIsIkFkZENpcmNsZUljb24iLCJQZXJzb25JY29uIiwiQWdyaWN1bHRvckdhbmFkZXJvIiwiaXNTdWJtaXR0aW5nIiwic2V0SXNTdWJtaXR0aW5nIiwic2VsZWN0ZWRSb3ciLCJzZXRTZWxlY3RlZFJvdyIsIm9wZW4iLCJzZXRPcGVuIiwicm93cyIsInNldFJvd3MiLCJpbnB1dFZhbHVlIiwic2V0SW5wdXRWYWx1ZSIsImlzU2VhcmNoQmFyT3BlbiIsInNldElzU2VhcmNoQmFyT3BlbiIsImZpbHRlcmVkUm93cyIsInNldEZpbHRlcmVkUm93cyIsInBlcnNvbklkIiwic2V0UGVyc29uSWQiLCJlc3RhZG9Nb2RhbCIsInNldEVzdGFkb01vZGFsIiwic2VsZWN0ZWRDbGllbnRJZCIsInNldFNlbGVjdGVkQ2xpZW50SWQiLCJ0YWJWYWx1ZSIsInNldFRhYlZhbHVlIiwiZW5hYmxlZFRhYnMiLCJzZXRFbmFibGVkVGFicyIsInNlbGVjdFByb3ZpbmNpYVJlZiIsInNlbGVjdENvbmRJdmFSZWYiLCJkb2N1bWVudG9SZWYiLCJlcnJvciIsInNldEVycm9yIiwidmFsaWRhdGVUYWIxIiwicHJvcGlldGFyaW9Ob21icmUiLCJwcm9waWV0YXJpb0RvY3VtZW50byIsInByb3BpZXRhcmlvVGVsZWZvbm8iLCJwcm9waWV0YXJpb0VtYWlsIiwiZm9ybURhdGEiLCJ0cmltIiwidmFsaWRhdGVUYWIyIiwiZW1wcmVzYVJhem9uU29jaWFsIiwiZW1wcmVzYVRpcG9DbGllbnRlIiwiZW1wcmVzYURvbWljaWxpbyIsImVtcHJlc2FMb2NhbGlkYWQiLCJlbXByZXNhUHJvdmluY2lhIiwiZW1wcmVzYUNvbmRGcmVudGVJdmEiLCJ2YWxpZGF0ZVRhYjMiLCJjb250YWN0b3MiLCJsZW5ndGgiLCJub21icmUiLCJjYXJnbyIsInRlbGVmb25vIiwiZW1haWwiLCJlbmFibGVOZXh0VGFiIiwiY3VycmVudFRhYiIsIm5ld0VuYWJsZWRUYWJzIiwiaGFuZGxlTmV4dFRhYiIsImNhblByb2NlZWQiLCJhbGVydCIsImhhbmRsZVByZXZpb3VzVGFiIiwiaGFuZGxlQ2xpZW50U2VsZWN0IiwiY2xpZW50SWQiLCJzZWxlY3RlZENsaWVudCIsImZpbmQiLCJyb3ciLCJpZCIsImxvY2FsaWRhZCIsInByb3ZpbmNpYSIsImx1Z2FyIiwic3BsaXQiLCJzZXRGb3JtRGF0YSIsInByZXZEYXRhIiwicmF6b25Tb2NpYWwiLCJ0aXBvQ2xpZW50ZSIsImRpcmVjY2lvbiIsImNvbmRGcmVudGVJdmEiLCJub21icmVDb250YWN0byIsImNhcmdvQ29udGFjdG8iLCJtYWlsIiwidXNhckNvbW9SYXpvblNvY2lhbCIsInByb3ZpbmNpYXMiLCJ0aXBvQ2xpZW50ZU9wdGlvbnMiLCJ2YWx1ZSIsImNhdGVnb3J5IiwiaWNvbiIsImNvbmRGcmVudGVJdmFPcHRpb25zIiwiaGFuZGxlT3BlbkFkZCIsImNsZWFyRnJvbSIsImhhbmRsZUNsaWNrQ2xvc2UiLCJfZXZlbnQiLCJyZWFzb24iLCJoYW5kbGVJbnB1dENoYW5nZSIsImUiLCJuYW1lIiwidGFyZ2V0Iiwic3RhcnRzV2l0aCIsImluY2x1ZGVzIiwidGVzdCIsInByZXZFcnJvciIsImNsZWFuZWQiLCJyZXBsYWNlIiwiZm9ybWF0dGVkIiwic2xpY2UiLCJpc1ZhbGlkRm9ybWF0IiwicHJldlN0YXRlIiwiZW1haWxSZWdleCIsIm5ld1N0YXRlIiwiaGFuZGxlU3VibWl0IiwicHJldmVudERlZmF1bHQiLCJoYW5kbGVTZWFyY2hDbGljayIsImNvbHVtbnMiLCJmaWVsZCIsImhlYWRlck5hbWUiLCJ3aWR0aCIsImhlYWRlckNsYXNzTmFtZSIsInJlbmRlckNlbGwiLCJwYXJhbXMiLCJzeCIsInB5IiwidmFyaWFudCIsImZvbnRXZWlnaHQiLCJjb2xvciIsImZvbnRTaXplIiwibGluZUhlaWdodCIsImZvbnRTdHlsZSIsImhhbmRsZUFkZENsaWVudGUiLCJjb25zb2xlIiwibG9nIiwibmV3UGVyc29uIiwiZG9jdW1lbnRvIiwicmVzIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvayIsIkVycm9yIiwiZGF0YUNsaWVudGVzIiwiZmV0Y2hDbGllbnRlcyIsImpzb24iLCJnZXREYXRhIiwiaGFuZGxlU2VhcmhDbGllbnRlIiwiZXZlbnQiLCJzZWFyY2hWYWx1ZSIsInNldFNlYXJjaFRlcm0iLCJmaWx0ZXJlZERhdGEiLCJmaWx0ZXIiLCJ0b0xvd2VyQ2FzZSIsImhhbmRsZURlbGV0ZUNsaWVudGUiLCJoYW5kbGVFZGl0IiwiYWdyaWN1bHRvciIsImhhbmRsZVVwZGF0ZUNsaWVudGUiLCJ1cGRhdGVkUm93cyIsIm1hcCIsImhhbmRsZUxvY2FsaWRhZEtleURvd24iLCJrZXkiLCJjdXJyZW50IiwiZm9jdXMiLCJoYW5kbGVQcm92aW5jaWFDaGFuZ2UiLCJzZXRUaW1lb3V0IiwiaGFuZGxlQ29uZEl2YUNoYW5nZSIsImhhbmRsZVNlbGVjdEFncmljdWx0b3IiLCJzZWxlY3RlZEFncmljdWx0b3IiLCJsb2NhbFN0b3JhZ2UiLCJzZXRJdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwic2VhcmNoVGVybSIsInBhZ2UiLCJzZXRQYWdlIiwiaGFuZGxlU2VhcmNoQ2hhbmdlIiwibGFiZWxTdHlsZXMiLCJtYXJnaW5Cb3R0b20iLCJkaXNwbGF5IiwiZm9udEZhbWlseSIsImp1c3RpZnlDb250ZW50IiwibWIiLCJtdCIsImNvbXBvbmVudCIsInN0eWxlIiwib25DbGljayIsImJnY29sb3IiLCJoZWlnaHQiLCJhbGlnblNlbGYiLCJzdGFydEljb24iLCJlbGV2YXRpb24iLCJwIiwiYm9yZGVyUmFkaXVzIiwiZnVsbFdpZHRoIiwicGxhY2Vob2xkZXIiLCJvbkNoYW5nZSIsIklucHV0UHJvcHMiLCJzdGFydEFkb3JubWVudCIsInBvc2l0aW9uIiwib3B0aW9uIiwib3B0aW9uRGVsZXRlRnVuY3Rpb24iLCJvcHRpb25VcGRhdGVGdW5jdGlvbiIsIm9wdGlvblNlbGVjdCIsIm9uQ2xvc2UiLCJtYXhXaWR0aCIsIm1pbkhlaWdodCIsImFyaWEtbGFiZWwiLCJyaWdodCIsInRvcCIsIm9uU3VibWl0IiwiY2xhc3NOYW1lIiwiYm9yZGVyQm90dG9tIiwiYm9yZGVyQ29sb3IiLCJuZXdWYWx1ZSIsInRleHRUcmFuc2Zvcm0iLCJsYWJlbCIsIm1pbldpZHRoIiwiZGlzYWJsZWQiLCJjb250YWluZXIiLCJzcGFjaW5nIiwic2l6ZSIsInhzIiwic20iLCJ0eXBlIiwicmVxdWlyZWQiLCJCb29sZWFuIiwiaGVscGVyVGV4dCIsImVuZEFkb3JubWVudCIsImFsaWduSXRlbXMiLCJjdXJzb3IiLCJpbnB1dCIsImNoZWNrZWQiLCJwcmV2IiwibWFyZ2luUmlnaHQiLCJwdCIsImJvcmRlclRvcCIsImJhY2tncm91bmRDb2xvciIsInN5bnRoZXRpY0V2ZW50IiwiZGlzcGxheUVtcHR5IiwicmVuZGVyVmFsdWUiLCJzZWxlY3RlZCIsInNwYW4iLCJvcHQiLCJwYWRkaW5nIiwiZ2FwIiwiY29uZGljaW9uIiwiY29udGFjdG8iLCJpbmRleCIsImJvcmRlciIsImMiLCJuZXdDb250YWN0b3MiLCJjb250YWN0b0luZGV4IiwiZmluZEluZGV4IiwibmV3SWQiLCJNYXRoIiwibWF4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});