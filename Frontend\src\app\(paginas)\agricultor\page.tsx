"use client";
import React, { useState, useEffect, useRef } from "react";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import { DataGrid as MuiDataGrid } from "@mui/x-data-grid";
import {
  Button,
  Grid,
  InputAdornment,
  Paper,
  SelectChangeEvent,
  Tabs,
  Tab,
} from "@mui/material";
import { Dialog } from "@mui/material";
import { DialogContent } from "@mui/material";
import { DialogTitle } from "@mui/material";
import { DialogContentText } from "@mui/material";
import { FormControl } from "@mui/material";
import { FormHelperText } from "@mui/material";

import { IconButton } from "@mui/material";
import { InputLabel } from "@mui/material";
import { MenuItem } from "@mui/material";
import { Select } from "@mui/material";
import { TextField } from "@mui/material";
import { Typography } from "@mui/material";
import Datatable from "../../components/table/DataTable";
import CloseIcon from "@mui/icons-material/Close";
import { Box } from "@mui/system";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import { Search as SearchIcon } from "@mui/icons-material";
import { Inter } from "next/font/google";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import PersonIcon from "@mui/icons-material/Person";

const inter = Inter({ subsets: ["latin"] });

interface AgricultorGanaderoProps {
  onSearchChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onAddClick?: () => void;
}

interface Client {
  id: number;

  // Datos del Propietario/Persona Física (Pestaña 1)
  propietarioNombre?: string;
  propietarioDocumento?: string;
  propietarioTelefono?: string;
  propietarioEmail?: string;

  // Datos de la Empresa/Razón Social (Pestaña 2)
  razonSocial: string;
  tipoCliente?: string;
  direccion: string;
  empresaLocalidad?: string;
  provincia?: string;
  condFrenteIva: string;

  // Datos del Contacto/Encargado Principal (Pestaña 3)
  nombreContacto?: string;
  cargoContacto?: string;
  telefono: string;
  mail: string;

  // Campos calculados/derivados
  lugar: string;
  documento: string;
}

const AgricultorGanadero: React.FC<AgricultorGanaderoProps> = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedRow, setSelectedRow] = useState<Client | null>(null);
  const [open, setOpen] = useState(false);
  const [rows, setRows] = useState<Client[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isSearchBarOpen, setIsSearchBarOpen] = useState(false);
  const [filteredRows, setFilteredRows] = useState<Client[]>([]);
  const DataGrid = MuiDataGrid;
  const [personId, setPersonId] = useState<string>("");
  const [estadoModal, setEstadoModal] = useState<"add" | "update">("add");
  const [selectedClientId, setSelectedClientId] = useState<number | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [enabledTabs, setEnabledTabs] = useState<boolean[]>([
    true,
    false,
    false,
  ]); // Solo la primera pestaña habilitada inicialmente
  const selectProvinciaRef = useRef<HTMLInputElement | HTMLSelectElement>(null);
  const selectCondIvaRef = useRef<HTMLInputElement | HTMLSelectElement>(null);
  const documentoRef = useRef<HTMLInputElement | HTMLSelectElement>(null);
  const [error, setError] = useState<{ [key: string]: string }>({});

  // Funciones de validación para cada pestaña
  const validateTab1 = (): boolean => {
    const {
      propietarioNombre,
      propietarioDocumento,
      propietarioTelefono,
      propietarioEmail,
    } = formData;
    return !!(
      propietarioNombre.trim() &&
      propietarioDocumento.trim() &&
      propietarioTelefono.trim() &&
      propietarioEmail.trim()
    );
  };

  const validateTab2 = (): boolean => {
    const {
      empresaRazonSocial,
      empresaTipoCliente,
      empresaDomicilio,
      empresaLocalidad,
      empresaProvincia,
      empresaCondFrenteIva,
    } = formData;
    return !!(
      empresaRazonSocial.trim() &&
      empresaTipoCliente.trim() &&
      empresaDomicilio.trim() &&
      empresaLocalidad.trim() &&
      empresaProvincia.trim() &&
      empresaCondFrenteIva.trim()
    );
  };

  const validateTab3 = (): boolean => {
    return (
      formData.contactos.length > 0 &&
      formData.contactos[0].nombre.trim() !== "" &&
      formData.contactos[0].cargo.trim() !== "" &&
      formData.contactos[0].telefono.trim() !== "" &&
      formData.contactos[0].email.trim() !== ""
    );
  };

  // Función para habilitar la siguiente pestaña
  const enableNextTab = (currentTab: number) => {
    const newEnabledTabs = [...enabledTabs];
    if (currentTab + 1 < newEnabledTabs.length) {
      newEnabledTabs[currentTab + 1] = true;
      setEnabledTabs(newEnabledTabs);
    }
  };

  // Función para ir a la siguiente pestaña
  const handleNextTab = () => {
    let canProceed = false;

    switch (tabValue) {
      case 0:
        canProceed = validateTab1();
        break;
      case 1:
        canProceed = validateTab2();
        break;
      case 2:
        canProceed = validateTab3();
        break;
    }

    if (canProceed) {
      enableNextTab(tabValue);
      setTabValue(tabValue + 1);
    } else {
      alert(
        "Por favor complete todos los campos requeridos antes de continuar."
      );
    }
  };

  // Función para ir a la pestaña anterior
  const handlePreviousTab = () => {
    if (tabValue > 0) {
      setTabValue(tabValue - 1);
    }
  };

  const handleClientSelect = (clientId: number): void => {
    setSelectedClientId(clientId);
    const selectedClient = (rows as Client[]).find(
      (row) => row.id === clientId
    );
    if (selectedClient) {
      // Split the lugar field into localidad and provincia
      const [localidad, provincia] = selectedClient.lugar.split(" - ");

      setFormData((prevData) => ({
        ...prevData,
        // Datos del Propietario/Persona Física (Pestaña 1)
        propietarioNombre: selectedClient.propietarioNombre || "",
        propietarioDocumento: selectedClient.propietarioDocumento || "",
        propietarioTelefono: selectedClient.propietarioTelefono || "",
        propietarioEmail: selectedClient.propietarioEmail || "",

        // Datos de la Empresa/Razón Social (Pestaña 2)
        empresaRazonSocial: selectedClient.razonSocial,
        empresaTipoCliente: selectedClient.tipoCliente || "",
        empresaDomicilio: selectedClient.direccion,
        empresaLocalidad: selectedClient.empresaLocalidad || localidad,
        empresaProvincia: selectedClient.provincia || provincia,
        empresaCondFrenteIva: selectedClient.condFrenteIva,

        // Datos del Contacto/Encargado Principal (Pestaña 3)
        contactos: [
          {
            id: 1,
            nombre: selectedClient.nombreContacto || "",
            cargo: selectedClient.cargoContacto || "",
            telefono: selectedClient.telefono,
            email: selectedClient.mail,
          },
        ],
      }));
    }
  };

  const [formData, setFormData] = useState({
    // Pestaña 1: Propietario/Persona Física
    propietarioNombre: "",
    propietarioDocumento: "",
    propietarioTelefono: "",
    propietarioEmail: "",
    usarComoRazonSocial: false,

    // Pestaña 2: Empresa/Razón Social
    empresaRazonSocial: "",
    empresaTipoCliente: "",
    empresaDomicilio: "",
    empresaLocalidad: "",
    empresaProvincia: "",
    empresaCondFrenteIva: "",

    // Pestaña 3: Contactos/Encargados (array dinámico)
    contactos: [
      {
        id: 1,
        nombre: "",
        cargo: "",
        telefono: "",
        email: "",
      },
    ],
  });

  const provincias = [
    "Buenos Aires",
    "Catamarca",
    "Chaco",
    "Chubut",
    "Córdoba",
    "Corrientes",
    "Entre Ríos",
    "Formosa",
    "Jujuy",
    "La Pampa",
    "La Rioja",
    "Mendoza",
    "Misiones",
    "Neuquén",
    "Río Negro",
    "Salta",
    "San Juan",
    "San Luis",
    "Santa Cruz",
    "Santa Fe",
    "Santiago del Estero",
    "Tierra del Fuego",
    "Tucumán",
  ];

  const tipoClienteOptions = [
    // Productores
    { value: "Productor(comercial)", category: "Productores", icon: "👨‍💼" },
    { value: "Productor(familiar)", category: "Productores", icon: "👨‍👩‍👧‍👦" },
    { value: "Estancia", category: "Productores", icon: "🏞️" },

    // Empresas y Organizaciones
    {
      value: "Empresa (persona jurídica, p. ej. SA / SRL)",
      category: "Empresas",
      icon: "🏢",
    },
    { value: "Cooperativa", category: "Empresas", icon: "🏘️" },
    {
      value: "Asociación/Consorcio/Entidad Gremial",
      category: "Empresas",
      icon: "🤝",
    },

    // Servicios y Contratistas
    {
      value: "Contratista(p. ej. otro que contrata equipo)",
      category: "Servicios",
      icon: "🚜",
    },
    {
      value: "Acopio/Industria/Exportador(silos, plantas, compradoras)",
      category: "Servicios",
      icon: "🏭",
    },

    // Sector Público y Otros
    {
      value: "Municipalidad/Estatal/Gubernamental",
      category: "Público",
      icon: "�",
    },
    {
      value: "Particular(pequeños clientes domésticos)",
      category: "Otros",
      icon: "👤",
    },
    { value: "Otro(para casos no previstos)", category: "Otros", icon: "❓" },
    { value: "No Especificado", category: "Otros", icon: "➖" },
  ];

  const condFrenteIvaOptions = [
    "IVA Responsable Inscripto",
    "IVA Responsable no Inscripto",
    "IVA no Responsable",
    "IVA Sujeto Exento",
    "Consumidor Final",
    "Responsable Monotributo",
    "Sujeto no Categorizado",
    "Proveedor del Exterior",
    "Cliente del Exterior",
    "IVA Liberado",
    "Pequeño Contribuyente Social",
    "Monotributista Social",
    "Pequeño Contribuyente Eventual",
  ];

  const handleOpenAdd = () => {
    setEstadoModal("add");
    clearFrom();
    setOpen(true);
  };

  const clearFrom = () => {
    setFormData({
      // Pestaña 1: Propietario/Persona Física
      propietarioNombre: "",
      propietarioDocumento: "",
      propietarioTelefono: "",
      propietarioEmail: "",
      usarComoRazonSocial: false,

      // Pestaña 2: Empresa/Razón Social
      empresaRazonSocial: "",
      empresaTipoCliente: "",
      empresaDomicilio: "",
      empresaLocalidad: "",
      empresaProvincia: "",
      empresaCondFrenteIva: "",

      // Pestaña 3: Contactos/Encargados
      contactos: [
        {
          id: 1,
          nombre: "",
          cargo: "",
          telefono: "",
          email: "",
        },
      ],
    });
    setError({});
    setTabValue(0); // Resetear al primer tab
    setEnabledTabs([true, false, false]); // Resetear pestañas habilitadas
  };

  const handleClickClose = (
    _event: React.MouseEvent<HTMLElement>,
    reason?: string
  ) => {
    if (reason && reason === "backdropClick") return;
    setOpen(false);
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;

    // Validaciones para campos de texto (nombres, razón social, etc.)
    if (
      name === "propietarioNombre" ||
      name === "empresaRazonSocial" ||
      name === "empresaLocalidad" ||
      (name.startsWith("contacto") && name.includes("nombre"))
    ) {
      if (!/^[a-zA-ZÀ-ÿ\s]*$/.test(value)) {
        setError((prevError) => ({
          ...prevError,
          [name]: "Solo se permiten letras y espacios",
        }));
        return;
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    // Validaciones para domicilio
    if (name === "empresaDomicilio") {
      if (!/^[a-zA-ZÀ-ÿ0-9\s.]*$/.test(value)) {
        setError((prevError) => ({
          ...prevError,
          [name]: "Solo se permiten letras, números, espacios y puntos",
        }));
        return;
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    // Validaciones para teléfonos
    if (name === "propietarioTelefono" || name.includes("telefono")) {
      // Eliminar todo lo que no sea número
      const cleaned = value.replace(/\D/g, "");

      // Limitar a 10 dígitos
      if (cleaned.length > 10) return;

      let formatted;
      if (cleaned.length <= 4) {
        formatted = cleaned;
      } else {
        formatted = `${cleaned.slice(0, 4)}-${cleaned.slice(4)}`;
      }

      // Validar el formato completo
      const isValidFormat = /^\d{4}-\d{6}$/.test(formatted);

      setError((prevError) => ({
        ...prevError,
        [name]:
          formatted.length === 11 && !isValidFormat
            ? "Formato inválido. Debe ser 0000-000000"
            : "",
      }));

      setFormData((prevState) => ({
        ...prevState,
        [name]: formatted,
      }));
      return;
    }

    if (name === "personMail") {
      // Expresión regular para validar email
      const emailRegex = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;

      // Si el campo no está vacío, validar el formato
      if (value && !emailRegex.test(value)) {
        setError((prevError) => ({
          ...prevError,
          [name]: "Formato de email inválido. Ejemplo: <EMAIL>",
        }));
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    if (name === "propietarioDocumento") {
      // Eliminar todo lo que no sea número
      const cleaned = value.replace(/\D/g, "");

      // Limitar a 11 dígitos en total
      if (cleaned.length > 11) return;

      let formatted;
      if (cleaned.length <= 2) {
        formatted = cleaned;
      } else if (cleaned.length <= 10) {
        formatted = `${cleaned.slice(0, 2)}-${cleaned.slice(2)}`;
      } else {
        formatted = `${cleaned.slice(0, 2)}-${cleaned.slice(
          2,
          10
        )}-${cleaned.slice(10, 11)}`;
      }

      // Validar el formato completo
      const isValidFormat = /^\d{2}-\d{8}-\d{1}$/.test(formatted);

      setError((prevError) => ({
        ...prevError,
        [name]:
          formatted.length === 12 && !isValidFormat
            ? "Formato inválido. Debe ser 00-00000000-0"
            : "",
      }));

      setFormData((prevState) => ({
        ...prevState,
        [name]: formatted,
      }));
      return;
    }

    setFormData((prevState) => {
      const newState = {
        ...prevState,
        [name]: value,
      };

      // Si está marcada la opción de usar como razón social, sincronizar datos
      if (prevState.usarComoRazonSocial) {
        if (name === "propietarioNombre") {
          newState.empresaRazonSocial = value;
          // También actualizar el primer contacto
          newState.contactos = [
            {
              ...prevState.contactos[0],
              nombre: value,
            },
            ...prevState.contactos.slice(1),
          ];
        } else if (name === "propietarioTelefono") {
          // Actualizar el teléfono del primer contacto
          newState.contactos = [
            {
              ...prevState.contactos[0],
              telefono: value,
            },
            ...prevState.contactos.slice(1),
          ];
        } else if (name === "propietarioEmail") {
          // Actualizar el email del primer contacto
          newState.contactos = [
            {
              ...prevState.contactos[0],
              email: value,
            },
            ...prevState.contactos.slice(1),
          ];
        }
      }

      return newState;
    });
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  const handleSearchClick = () => {
    setIsSearchBarOpen(!isSearchBarOpen);
  };

  const columns = [
    {
      field: "empresa",
      headerName: "Empresa",
      width: 280,
      headerClassName: "custom-header",
      renderCell: (params: any) => (
        <Box sx={{ py: 1 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: "600",
              color: "#333",
              fontSize: "0.875rem",
              lineHeight: 1.2,
            }}
          >
            {params.row.razonSocial}
          </Typography>
          {params.row.tipoCliente && (
            <Typography
              variant="caption"
              sx={{
                color: "#666",
                fontSize: "0.75rem",
                fontStyle: "italic",
              }}
            >
              {params.row.tipoCliente}
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: "contacto",
      headerName: "Contacto",
      width: 220,
      headerClassName: "custom-header",
      renderCell: (params: any) => (
        <Box sx={{ py: 1 }}>
          {params.row.nombreContacto ? (
            <>
              <Typography
                variant="body2"
                sx={{
                  fontWeight: "600",
                  color: "#333",
                  fontSize: "0.875rem",
                  lineHeight: 1.2,
                }}
              >
                {params.row.nombreContacto}
              </Typography>
              {params.row.cargoContacto && (
                <Typography
                  variant="caption"
                  sx={{
                    color: "#666",
                    fontSize: "0.75rem",
                    fontStyle: "italic",
                  }}
                >
                  {params.row.cargoContacto}
                </Typography>
              )}
            </>
          ) : (
            <Typography
              variant="body2"
              sx={{
                color: "#999",
                fontStyle: "italic",
                fontSize: "0.875rem",
              }}
            >
              Sin contacto
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: "telefono",
      headerName: "Teléfono",
      width: 140,
      headerClassName: "custom-header",
    },
    {
      field: "mail",
      headerName: "Email",
      width: 180,
      headerClassName: "custom-header",
    },
    {
      field: "lugar",
      headerName: "Ubicación",
      width: 200,
      headerClassName: "custom-header",
    },
    {
      field: "documento",
      headerName: "Documento",
      width: 140,
      headerClassName: "custom-header",
    },
  ];

  /*AGREGAR AGRICULTOR/GANADERO*/
  const handleAddCliente = async () => {
    console.log("Iniciando envío...");
    setIsSubmitting(true);
    const lugar = `${formData.empresaLocalidad} - ${formData.empresaProvincia}`;
    const newPerson = {
      // Datos del Propietario/Persona Física (Pestaña 1)
      propietarioNombre: formData.propietarioNombre,
      propietarioDocumento: formData.propietarioDocumento,
      propietarioTelefono: formData.propietarioTelefono,
      propietarioEmail: formData.propietarioEmail,

      // Datos de la Empresa/Razón Social (Pestaña 2)
      razonSocial: formData.empresaRazonSocial,
      tipoCliente: formData.empresaTipoCliente,
      direccion: formData.empresaDomicilio,
      empresaLocalidad: formData.empresaLocalidad,
      provincia: formData.empresaProvincia,
      condFrenteIva: formData.empresaCondFrenteIva,

      // Datos del Contacto/Encargado Principal (Pestaña 3)
      nombreContacto: formData.contactos[0]?.nombre || "",
      cargoContacto: formData.contactos[0]?.cargo || "",
      telefono: formData.contactos[0]?.telefono || "",
      mail: formData.contactos[0]?.email || "",

      // Campos calculados/derivados
      lugar: lugar,
      documento: formData.propietarioDocumento, // Usar el documento del propietario
    };

    // TODO: Add proper form validation if needed
    // For now, individual field validations are handled in the onChange handlers

    // Mostrar cada dato individual en la consola
    console.log("Razón Social:", newPerson.razonSocial);
    console.log("Dirección:", newPerson.direccion);
    console.log("Teléfono:", newPerson.telefono);
    console.log("Mail:", newPerson.mail);
    console.log("Lugar:", lugar);
    console.log("Condición Frente IVA:", newPerson.condFrenteIva);
    console.log("Documento:", newPerson.documento);

    try {
      const res = await fetch("http://localhost:8080/api/agricultor", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newPerson),
      });

      // Verificar si la solicitud fue exitosa
      if (!res.ok) {
        throw new Error("Error al guardar el cliente");
      }

      clearFrom();
      const dataClientes = await fetchClientes();
      setRows(dataClientes);
      setOpen(false);
    } catch (error) {
      console.error("Error en la solicitud:", error);
    }
  };

  /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/
  const fetchClientes = async () => {
    try {
      const res = await fetch("http://localhost:8080/api/agricultor");
      if (!res.ok) {
        throw new Error("Error al obtener los clientes");
      }
      const dataClientes = await res.json();
      return dataClientes;
    } catch (error) {
      console.error("Error en la solicitud:", error);
      // Devolver un valor predeterminado en caso de error
      return [];
    }
  };

  useEffect(() => {
    const getData = async () => {
      const dataClientes = await fetchClientes();
      setRows(dataClientes);
    };

    getData();
  }, []);

  /*BUSCAR AGRICULTOR/GANADERO*/
  const handleSearhCliente = (event: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = event.target.value;
    setSearchTerm(searchValue);

    const filteredData = rows.filter((row: Client) => {
      return (
        row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.direccion.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.telefono.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.mail.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.lugar.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.documento.toLowerCase().includes(searchValue.toLowerCase())
      );
    });
    setFilteredRows(filteredData);
  };

  /*ELIMINAR AGRICULTOR/GANADERO*/
  const handleDeleteCliente = async (id: number) => {
    console.log("Cliente a eliminar:", id);

    try {
      const res = await fetch(`http://localhost:8080/api/agricultor/${id}`, {
        method: "DELETE",
      });

      if (res.ok) {
        console.log("Cliente eliminado exitosamente.");
        // Actualizar el estado de las filas después de eliminar un cliente
        const dataClientes = await fetchClientes();
        setRows(dataClientes);
      } else {
        console.error("Error al eliminar el cliente:", id);
      }
    } catch (error) {
      console.error("Error en la solicitud de eliminación:", error);
    }
  };

  /*CLICK BOTON MODIFICAR(LAPIZ)*/
  const handleEdit = async (id: any) => {
    try {
      const res = await fetch(`http://localhost:8080/api/agricultor/${id}`, {
        method: "GET",
      });

      if (res.ok) {
        console.log("Cliente obtenido exitosamente.");

        const agricultor = await res.json();
        setFormData((prevData) => ({
          ...prevData,
          // Datos del Propietario/Persona Física (Pestaña 1)
          propietarioNombre: agricultor.propietarioNombre || "",
          propietarioDocumento: agricultor.propietarioDocumento || "",
          propietarioTelefono: agricultor.propietarioTelefono || "",
          propietarioEmail: agricultor.propietarioEmail || "",

          // Datos de la Empresa/Razón Social (Pestaña 2)
          empresaRazonSocial: agricultor.razonSocial,
          empresaTipoCliente: agricultor.tipoCliente || "",
          empresaDomicilio: agricultor.direccion,
          empresaLocalidad:
            agricultor.empresaLocalidad ||
            agricultor.lugar.split(" - ")[0]?.trim() ||
            "",
          empresaProvincia:
            agricultor.provincia ||
            agricultor.lugar.split(" - ")[1]?.trim() ||
            "",
          empresaCondFrenteIva: agricultor.condFrenteIva,

          // Datos del Contacto/Encargado Principal (Pestaña 3)
          contactos: [
            {
              id: 1,
              nombre: agricultor.nombreContacto || "",
              cargo: agricultor.cargoContacto || "",
              telefono: agricultor.telefono,
              email: agricultor.mail,
            },
          ],
        }));
      } else {
        console.error("Error al modificar el cliente:", id);
      }
    } catch (error) {
      console.error("Error en la solicitud de eliminación:", error);
    }

    setEstadoModal("update");
    setOpen(true);
  };

  /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/
  const handleUpdateCliente = async () => {
    if (!selectedRow) return;
    const lugar = `${formData.empresaLocalidad} - ${formData.empresaProvincia}`;
    const newPerson: Client = {
      id: selectedRow.id,

      // Datos del Propietario/Persona Física (Pestaña 1)
      propietarioNombre: formData.propietarioNombre,
      propietarioDocumento: formData.propietarioDocumento,
      propietarioTelefono: formData.propietarioTelefono,
      propietarioEmail: formData.propietarioEmail,

      // Datos de la Empresa/Razón Social (Pestaña 2)
      razonSocial: formData.empresaRazonSocial,
      tipoCliente: formData.empresaTipoCliente,
      direccion: formData.empresaDomicilio,
      empresaLocalidad: formData.empresaLocalidad,
      provincia: formData.empresaProvincia,
      condFrenteIva: formData.empresaCondFrenteIva,

      // Datos del Contacto/Encargado Principal (Pestaña 3)
      nombreContacto: formData.contactos[0]?.nombre || "",
      cargoContacto: formData.contactos[0]?.cargo || "",
      telefono: formData.contactos[0]?.telefono || "",
      mail: formData.contactos[0]?.email || "",

      // Campos calculados/derivados
      lugar: lugar,
      documento: formData.propietarioDocumento, // Usar el documento del propietario
    };

    try {
      const res = await fetch(`http://localhost:8080/api/agricultor`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newPerson),
      });

      if (!res.ok) {
        throw new Error("Error al guardar el cliente");
      }

      // Update rows with proper typing
      const updatedRows = rows.map((row: Client) => {
        if (row.id === newPerson.id) {
          return newPerson;
        }
        return row;
      });

      setRows(updatedRows);
      clearFrom();
      setOpen(false);
    } catch (error) {
      console.error("Error en la solicitud:", error);
    }
  };

  const handleLocalidadKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key === "Enter" && selectProvinciaRef.current) {
      selectProvinciaRef.current.focus();
    }
  };

  const handleProvinciaChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    handleInputChange(event);
    setTimeout(() => {
      if (selectCondIvaRef.current) {
        selectCondIvaRef.current.focus();
      }
    }, 0);
  };

  const handleCondIvaChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    handleInputChange(event);
    setTimeout(() => {
      if (documentoRef.current) {
        documentoRef.current.focus();
      }
    }, 0);
  };

  const handleSelectAgricultor = (id: number) => {
    const selectedAgricultor = rows.find((row) => row.id === id);

    if (selectedAgricultor) {
      const agricultor = {
        id: selectedAgricultor.id,
        razonSocial: selectedAgricultor.razonSocial,
      };

      // Guardar el agricultor seleccionado
      localStorage.setItem("selectedAgricultor", JSON.stringify(agricultor));

      // Redirigir de vuelta a la página de establecimiento
      window.location.href = "/establecimiento";
    }
  };

  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const labelStyles = {
    fontWeight: 600,
    color: "#333",
    marginBottom: "8px",
    display: "block",
    fontFamily: "Lexend, sans-serif", // Cambiado a Lexend
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mb: 3,
          mt: 3,
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="div"
            sx={{
              fontWeight: "bold",
              fontFamily: "Lexend, sans-serif", // Cambiado a Lexend
            }}
          >
            Agricultores
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: "text.secondary",
              mt: 1,
              fontFamily: `${inter.style.fontFamily}`, // Cambiado a Inter
            }}
          >
            Gestione la información de sus Agricultores
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={handleOpenAdd}
          sx={{
            bgcolor: "#2E7D32", // Color verde más oscuro
            color: "#ffffff",
            "&:hover": { bgcolor: "#0D9A0A" },
            height: "fit-content",
            alignSelf: "center",
            fontFamily: `${inter.style.fontFamily}`, // Agregado para usar Inter
          }}
          startIcon={<AddOutlinedIcon />}
        >
          Nuevo Agricultor
        </Button>
      </Box>

      <Paper elevation={2} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Buscar..."
          value={searchTerm}
          onChange={handleSearhCliente}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <IconButton onClick={handleSearchClick}>
                  <SearchIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />
        <Datatable
          columns={columns}
          rows={filteredRows.length > 0 ? filteredRows : rows}
          option={true}
          optionDeleteFunction={handleDeleteCliente}
          optionUpdateFunction={handleEdit}
          setSelectedRow={(row) => setSelectedRow(row as Client | null)}
          selectedRow={selectedRow}
          optionSelect={handleSelectAgricultor}
        />
      </Paper>

      <Dialog
        open={open}
        onClose={handleClickClose}
        maxWidth="lg"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            width: "1100px", // Ancho ampliado de 825px a 1100px
            maxWidth: "95vw", // Mejor aprovechamiento del viewport
            minHeight: "600px", // Altura mínima para mejor proporción
          },
        }}
      >
        <Box sx={{ p: 4 }}>
          {/* Padding ampliado para mejor espaciado */}
          <Box sx={{ mb: 2 }}>
            <DialogTitle
              sx={{
                p: 0,
                fontFamily: "Lexend, sans-serif",
                fontSize: "1.5rem",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Registrar nuevo agricultor/ganadero
            </DialogTitle>
            <DialogContentText
              sx={{
                p: 0,
                mt: 1,
                fontFamily: "Inter, sans-serif",
                color: "#666",
              }}
            >
              Complete la información del nuevo agricultor/ganadero a registrar.
            </DialogContentText>
          </Box>
          <IconButton
            aria-label="close"
            onClick={(event) => handleClickClose(event, "closeButtonClick")}
            sx={{ position: "absolute", right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
          <Box
            component="form"
            onSubmit={handleSubmit}
            className={inter.className}
          >
            <DialogContent sx={{ p: 0 }}>
              {/* Tabs para organizar la información */}
              <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 3 }}>
                <Tabs
                  value={tabValue}
                  onChange={(event, newValue) => {
                    // Solo permitir cambiar a pestañas habilitadas
                    if (enabledTabs[newValue]) {
                      setTabValue(newValue);
                    }
                  }}
                  sx={{
                    "& .MuiTab-root": {
                      fontFamily: "Lexend, sans-serif",
                      fontWeight: 600,
                      textTransform: "none",
                      fontSize: "1rem",
                    },
                  }}
                >
                  <Tab
                    label="👤 Propietario/Persona Física"
                    sx={{ minWidth: 220 }}
                    disabled={!enabledTabs[0]}
                  />
                  <Tab
                    label="🏢 Empresa/Razón Social"
                    sx={{ minWidth: 200 }}
                    disabled={!enabledTabs[1]}
                  />
                  <Tab
                    label="📞 Contacto/Encargado"
                    sx={{ minWidth: 200 }}
                    disabled={!enabledTabs[2]}
                  />
                </Tabs>
              </Box>

              {/* Contenido de las pestañas */}
              {tabValue === 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 3,
                      fontFamily: "Lexend, sans-serif",
                      color: "#333",
                    }}
                  >
                    Datos del Propietario/Persona Física
                  </Typography>
                  <Grid container spacing={3}>
                    {/* Nombre del Propietario */}
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Nombre Completo *
                      </Typography>
                      <TextField
                        placeholder="Ej: Juan Carlos Pérez"
                        variant="outlined"
                        name="propietarioNombre"
                        type="text"
                        required
                        fullWidth
                        error={Boolean(error.propietarioNombre)}
                        helperText={error.propietarioNombre}
                        onChange={handleInputChange}
                        value={formData.propietarioNombre}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.propietarioNombre &&
                                (error.propietarioNombre ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    {/* Documento del Propietario */}
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        DNI/CUIT *
                      </Typography>
                      <TextField
                        placeholder="Ej: 20-12345678-9"
                        variant="outlined"
                        name="propietarioDocumento"
                        type="text"
                        required
                        fullWidth
                        error={Boolean(error.propietarioDocumento)}
                        helperText={error.propietarioDocumento}
                        onChange={handleInputChange}
                        value={formData.propietarioDocumento}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.propietarioDocumento &&
                                (error.propietarioDocumento ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    {/* Teléfono del Propietario */}
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Teléfono Personal
                      </Typography>
                      <TextField
                        placeholder="Ej: 0000-000000"
                        variant="outlined"
                        name="propietarioTelefono"
                        type="text"
                        fullWidth
                        error={Boolean(error.propietarioTelefono)}
                        helperText={error.propietarioTelefono}
                        onChange={handleInputChange}
                        value={formData.propietarioTelefono}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.propietarioTelefono &&
                                (error.propietarioTelefono ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    {/* Email del Propietario */}
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Email Personal
                      </Typography>
                      <TextField
                        placeholder="Ej: <EMAIL>"
                        variant="outlined"
                        name="propietarioEmail"
                        type="email"
                        fullWidth
                        error={Boolean(error.propietarioEmail)}
                        helperText={error.propietarioEmail}
                        onChange={handleInputChange}
                        value={formData.propietarioEmail}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.propietarioEmail &&
                                (error.propietarioEmail ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    {/* Checkbox para usar como razón social */}
                    <Grid size={{ xs: 12 }}>
                      <Box
                        sx={{
                          mt: 2,
                          p: 2,
                          bgcolor: "#f5f5f5",
                          borderRadius: 1,
                        }}
                      >
                        <label
                          style={{
                            display: "flex",
                            alignItems: "center",
                            cursor: "pointer",
                          }}
                        >
                          <input
                            type="checkbox"
                            checked={formData.usarComoRazonSocial}
                            onChange={(e) => {
                              const checked = e.target.checked;
                              setFormData((prev) => ({
                                ...prev,
                                usarComoRazonSocial: checked,
                                // Si se marca, copiar datos del propietario a la empresa
                                empresaRazonSocial: checked
                                  ? prev.propietarioNombre
                                  : "",
                                // También copiar el primer contacto con los datos del propietario
                                contactos: checked
                                  ? [
                                      {
                                        ...prev.contactos[0],
                                        nombre: prev.propietarioNombre,
                                        telefono: prev.propietarioTelefono,
                                        email: prev.propietarioEmail,
                                        cargo:
                                          prev.contactos[0]?.cargo ||
                                          "Propietario",
                                      },
                                      ...prev.contactos.slice(1),
                                    ]
                                  : prev.contactos,
                              }));
                            }}
                            style={{ marginRight: "8px" }}
                          />
                          <Typography
                            variant="body2"
                            sx={{ fontFamily: "Inter, sans-serif" }}
                          >
                            ✅ Usar estos datos del propietario para la empresa
                            y contacto principal
                          </Typography>
                        </label>
                        {formData.usarComoRazonSocial && (
                          <Typography
                            variant="caption"
                            sx={{
                              color: "#2E7D32",
                              mt: 1,
                              display: "block",
                              fontStyle: "italic",
                            }}
                          >
                            💡 Los datos del propietario se sincronizarán
                            automáticamente con la razón social de la empresa y
                            el contacto principal
                          </Typography>
                        )}
                      </Box>
                    </Grid>
                  </Grid>

                  {/* Botones de navegación para pestaña 1 */}
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mt: 3,
                      pt: 2,
                      borderTop: "1px solid #e0e0e0",
                    }}
                  >
                    <Button
                      variant="outlined"
                      onClick={(event) =>
                        handleClickClose(event, "closeButtonClick")
                      }
                      sx={{ minWidth: 120 }}
                    >
                      Cancelar
                    </Button>
                    <Button
                      variant="contained"
                      onClick={handleNextTab}
                      sx={{
                        minWidth: 120,
                        bgcolor: "#2E7D32",
                        "&:hover": { bgcolor: "#1B5E20" },
                      }}
                    >
                      Siguiente
                    </Button>
                  </Box>
                </Box>
              )}

              {/* Pestaña 2: Empresa/Razón Social */}
              {tabValue === 1 && (
                <Box sx={{ mt: 3 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 3,
                      fontFamily: "Lexend, sans-serif",
                      color: "#333",
                    }}
                  >
                    Datos de la Empresa/Razón Social
                  </Typography>
                  <Grid container spacing={3}>
                    {/* Razón Social */}
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Razón Social *
                      </Typography>
                      <TextField
                        placeholder="Ej: Agropecuaria San Juan S.A."
                        variant="outlined"
                        name="empresaRazonSocial"
                        type="text"
                        required
                        fullWidth
                        error={Boolean(error.empresaRazonSocial)}
                        helperText={error.empresaRazonSocial}
                        onChange={handleInputChange}
                        value={formData.empresaRazonSocial}
                        disabled={formData.usarComoRazonSocial}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.empresaRazonSocial &&
                                (error.empresaRazonSocial ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                        sx={{
                          "& .MuiInputBase-input": {
                            backgroundColor: formData.usarComoRazonSocial
                              ? "#f5f5f5"
                              : "transparent",
                          },
                        }}
                      />
                      {formData.usarComoRazonSocial && (
                        <Typography
                          variant="caption"
                          sx={{ color: "#666", mt: 1, display: "block" }}
                        >
                          💡 Autocompletado desde datos del propietario
                        </Typography>
                      )}
                    </Grid>

                    {/* Tipo de Cliente */}
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Tipo de Cliente *
                      </Typography>
                      <FormControl
                        fullWidth
                        error={Boolean(error.empresaTipoCliente)}
                      >
                        <Select
                          name="empresaTipoCliente"
                          value={formData.empresaTipoCliente}
                          onChange={(event: SelectChangeEvent<string>) => {
                            const syntheticEvent = {
                              target: {
                                name: "empresaTipoCliente",
                                value: event.target.value,
                              },
                            } as React.ChangeEvent<HTMLSelectElement>;
                            handleInputChange(syntheticEvent);
                          }}
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected) {
                              return (
                                <span style={{ color: "#999" }}>
                                  Seleccione tipo de cliente
                                </span>
                              );
                            }
                            const option = tipoClienteOptions.find(
                              (opt) => opt.value === selected
                            );
                            return option
                              ? `${option.icon} ${option.value}`
                              : selected;
                          }}
                          sx={{
                            height: "56px",
                            "& .MuiSelect-select": {
                              padding: "16.5px 14px",
                              height: "1.4375em",
                              display: "flex",
                              alignItems: "center",
                            },
                          }}
                        >
                          <MenuItem value="" disabled>
                            Seleccione tipo de cliente
                          </MenuItem>
                          {tipoClienteOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <span>{option.icon}</span>
                                <span>{option.value}</span>
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                        {error.empresaTipoCliente && (
                          <FormHelperText>
                            {error.empresaTipoCliente}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    {/* Domicilio */}
                    <Grid size={{ xs: 12 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Domicilio de la Empresa
                      </Typography>
                      <TextField
                        placeholder="Ej: Av. San Martín 1234"
                        variant="outlined"
                        name="empresaDomicilio"
                        type="text"
                        fullWidth
                        error={Boolean(error.empresaDomicilio)}
                        helperText={error.empresaDomicilio}
                        onChange={handleInputChange}
                        value={formData.empresaDomicilio}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.empresaDomicilio &&
                                (error.empresaDomicilio ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    {/* Localidad y Provincia */}
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Localidad *
                      </Typography>
                      <TextField
                        placeholder="Ej: Mercedes"
                        variant="outlined"
                        name="empresaLocalidad"
                        type="text"
                        required
                        fullWidth
                        error={Boolean(error.empresaLocalidad)}
                        helperText={error.empresaLocalidad}
                        onChange={handleInputChange}
                        value={formData.empresaLocalidad}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.empresaLocalidad &&
                                (error.empresaLocalidad ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Provincia *
                      </Typography>
                      <FormControl
                        fullWidth
                        error={Boolean(error.empresaProvincia)}
                      >
                        <Select
                          name="empresaProvincia"
                          value={formData.empresaProvincia}
                          onChange={(event: SelectChangeEvent<string>) => {
                            const syntheticEvent = {
                              target: {
                                name: "empresaProvincia",
                                value: event.target.value,
                              },
                            } as React.ChangeEvent<HTMLSelectElement>;
                            handleInputChange(syntheticEvent);
                          }}
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected) {
                              return (
                                <span style={{ color: "#999" }}>
                                  Seleccione provincia
                                </span>
                              );
                            }
                            return selected;
                          }}
                          sx={{
                            height: "56px",
                            "& .MuiSelect-select": {
                              padding: "16.5px 14px",
                              height: "1.4375em",
                              display: "flex",
                              alignItems: "center",
                            },
                          }}
                        >
                          <MenuItem value="" disabled>
                            Seleccione provincia
                          </MenuItem>
                          {provincias.map((provincia) => (
                            <MenuItem key={provincia} value={provincia}>
                              {provincia}
                            </MenuItem>
                          ))}
                        </Select>
                        {error.empresaProvincia && (
                          <FormHelperText>
                            {error.empresaProvincia}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    {/* Condición frente al IVA */}
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Typography variant="body2" sx={labelStyles}>
                        Condición frente al IVA *
                      </Typography>
                      <FormControl
                        fullWidth
                        error={Boolean(error.empresaCondFrenteIva)}
                      >
                        <Select
                          name="empresaCondFrenteIva"
                          value={formData.empresaCondFrenteIva}
                          onChange={(event: SelectChangeEvent<string>) => {
                            const syntheticEvent = {
                              target: {
                                name: "empresaCondFrenteIva",
                                value: event.target.value,
                              },
                            } as React.ChangeEvent<HTMLSelectElement>;
                            handleInputChange(syntheticEvent);
                          }}
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected) {
                              return (
                                <span style={{ color: "#999" }}>
                                  Seleccione condición IVA
                                </span>
                              );
                            }
                            return selected;
                          }}
                          sx={{
                            height: "56px",
                            "& .MuiSelect-select": {
                              padding: "16.5px 14px",
                              height: "1.4375em",
                              display: "flex",
                              alignItems: "center",
                            },
                          }}
                        >
                          <MenuItem value="" disabled>
                            Seleccione condición frente al IVA
                          </MenuItem>
                          {condFrenteIvaOptions.map((condicion) => (
                            <MenuItem key={condicion} value={condicion}>
                              {condicion}
                            </MenuItem>
                          ))}
                        </Select>
                        {error.empresaCondFrenteIva && (
                          <FormHelperText>
                            {error.empresaCondFrenteIva}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>
                  </Grid>

                  {/* Botones de navegación para pestaña 2 */}
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mt: 3,
                      pt: 2,
                      borderTop: "1px solid #e0e0e0",
                    }}
                  >
                    <Button
                      variant="outlined"
                      onClick={handlePreviousTab}
                      sx={{ minWidth: 120 }}
                    >
                      Anterior
                    </Button>
                    <Button
                      variant="contained"
                      onClick={handleNextTab}
                      sx={{
                        minWidth: 120,
                        bgcolor: "#2E7D32",
                        "&:hover": { bgcolor: "#1B5E20" },
                      }}
                    >
                      Siguiente
                    </Button>
                  </Box>
                </Box>
              )}

              {/* Pestaña 3: Contacto/Encargado */}
              {tabValue === 2 && (
                <Box sx={{ mt: 3 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 3,
                      fontFamily: "Lexend, sans-serif",
                      color: "#333",
                    }}
                  >
                    Contactos/Encargados
                  </Typography>

                  {/* Lista dinámica de contactos */}
                  {formData.contactos.map((contacto, index) => (
                    <Box
                      key={contacto.id}
                      sx={{
                        mb: 4,
                        p: 3,
                        border: "1px solid #e0e0e0",
                        borderRadius: 2,
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          mb: 2,
                        }}
                      >
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <PersonIcon
                            sx={{
                              color: "#2E7D32",
                              fontSize: "1.2rem",
                            }}
                          />
                          <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: 600, color: "#333" }}
                          >
                            Contacto #{index + 1}
                          </Typography>
                        </Box>
                        {formData.contactos.length > 1 && (
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                            onClick={() => {
                              setFormData((prev) => ({
                                ...prev,
                                contactos: prev.contactos.filter(
                                  (c) => c.id !== contacto.id
                                ),
                              }));
                            }}
                          >
                            Eliminar
                          </Button>
                        )}
                      </Box>

                      <Grid container spacing={3}>
                        {/* Nombre del contacto */}
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <Typography variant="body2" sx={labelStyles}>
                            Nombre del Encargado *
                          </Typography>
                          <TextField
                            placeholder="Ej: María González"
                            variant="outlined"
                            name={`contacto_${contacto.id}_nombre`}
                            type="text"
                            required
                            fullWidth
                            error={Boolean(
                              error[`contacto_${contacto.id}_nombre`]
                            )}
                            helperText={error[`contacto_${contacto.id}_nombre`]}
                            onChange={(e) => {
                              const newContactos = [...formData.contactos];
                              const contactoIndex = newContactos.findIndex(
                                (c) => c.id === contacto.id
                              );
                              newContactos[contactoIndex].nombre =
                                e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                contactos: newContactos,
                              }));
                            }}
                            value={contacto.nombre}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  {contacto.nombre &&
                                    (error[`contacto_${contacto.id}_nombre`] ? (
                                      <ErrorIcon color="error" />
                                    ) : (
                                      <CheckCircleIcon color="success" />
                                    ))}
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Grid>

                        {/* Cargo del contacto */}
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <Typography variant="body2" sx={labelStyles}>
                            Cargo (Opcional)
                          </Typography>
                          <TextField
                            placeholder="Ej: Administrador, Encargado"
                            variant="outlined"
                            name={`contacto_${contacto.id}_cargo`}
                            type="text"
                            fullWidth
                            onChange={(e) => {
                              const newContactos = [...formData.contactos];
                              const contactoIndex = newContactos.findIndex(
                                (c) => c.id === contacto.id
                              );
                              newContactos[contactoIndex].cargo =
                                e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                contactos: newContactos,
                              }));
                            }}
                            value={contacto.cargo}
                          />
                        </Grid>

                        {/* Teléfono del contacto */}
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <Typography variant="body2" sx={labelStyles}>
                            Teléfono de Contacto
                          </Typography>
                          <TextField
                            placeholder="Ej: 0000-000000"
                            variant="outlined"
                            name={`contacto_${contacto.id}_telefono`}
                            type="text"
                            fullWidth
                            error={Boolean(
                              error[`contacto_${contacto.id}_telefono`]
                            )}
                            helperText={
                              error[`contacto_${contacto.id}_telefono`]
                            }
                            onChange={(e) => {
                              const newContactos = [...formData.contactos];
                              const contactoIndex = newContactos.findIndex(
                                (c) => c.id === contacto.id
                              );
                              newContactos[contactoIndex].telefono =
                                e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                contactos: newContactos,
                              }));
                            }}
                            value={contacto.telefono}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  {contacto.telefono &&
                                    (error[
                                      `contacto_${contacto.id}_telefono`
                                    ] ? (
                                      <ErrorIcon color="error" />
                                    ) : (
                                      <CheckCircleIcon color="success" />
                                    ))}
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Grid>

                        {/* Email del contacto */}
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <Typography variant="body2" sx={labelStyles}>
                            Email de Contacto
                          </Typography>
                          <TextField
                            placeholder="Ej: <EMAIL>"
                            variant="outlined"
                            name={`contacto_${contacto.id}_email`}
                            type="email"
                            fullWidth
                            error={Boolean(
                              error[`contacto_${contacto.id}_email`]
                            )}
                            helperText={error[`contacto_${contacto.id}_email`]}
                            onChange={(e) => {
                              const newContactos = [...formData.contactos];
                              const contactoIndex = newContactos.findIndex(
                                (c) => c.id === contacto.id
                              );
                              newContactos[contactoIndex].email =
                                e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                contactos: newContactos,
                              }));
                            }}
                            value={contacto.email}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  {contacto.email &&
                                    (error[`contacto_${contacto.id}_email`] ? (
                                      <ErrorIcon color="error" />
                                    ) : (
                                      <CheckCircleIcon color="success" />
                                    ))}
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Grid>
                      </Grid>
                    </Box>
                  ))}

                  {/* Botón para agregar más contactos */}
                  <Button
                    variant="outlined"
                    startIcon={<AddCircleIcon />}
                    onClick={() => {
                      const newId =
                        Math.max(...formData.contactos.map((c) => c.id)) + 1;
                      setFormData((prev) => ({
                        ...prev,
                        contactos: [
                          ...prev.contactos,
                          {
                            id: newId,
                            nombre: "",
                            cargo: "",
                            telefono: "",
                            email: "",
                          },
                        ],
                      }));
                    }}
                    sx={{ mt: 2 }}
                  >
                    Agregar otro contacto
                  </Button>

                  {/* Botones de navegación para pestaña 3 */}
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mt: 3,
                      pt: 2,
                      borderTop: "1px solid #e0e0e0",
                    }}
                  >
                    <Button
                      variant="outlined"
                      onClick={handlePreviousTab}
                      sx={{ minWidth: 120 }}
                    >
                      Anterior
                    </Button>
                    <Button
                      variant="contained"
                      type="submit"
                      disabled={isSubmitting}
                      onClick={handleAddCliente}
                      sx={{
                        minWidth: 120,
                        bgcolor: "#2E7D32",
                        "&:hover": { bgcolor: "#1B5E20" },
                      }}
                    >
                      {isSubmitting
                        ? "Guardando..."
                        : estadoModal === "add"
                        ? "Registrar"
                        : "Actualizar"}
                    </Button>
                  </Box>
                </Box>
              )}
            </DialogContent>
          </Box>
        </Box>
      </Dialog>
    </>
  );
};

export default AgricultorGanadero;
