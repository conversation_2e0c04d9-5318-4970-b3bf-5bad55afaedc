"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [enabledTabs, setEnabledTabs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        true,\n        false,\n        false\n    ]); // Solo la primera pestaña habilitada inicialmente\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Funciones de validación para cada pestaña\n    const validateTab1 = ()=>{\n        const { propietarioNombre, propietarioDocumento, propietarioTelefono, propietarioEmail } = formData;\n        return !!(propietarioNombre.trim() && propietarioDocumento.trim() && propietarioTelefono.trim() && propietarioEmail.trim());\n    };\n    const validateTab2 = ()=>{\n        const { empresaRazonSocial, empresaTipoCliente, empresaDomicilio, empresaLocalidad, empresaProvincia, empresaCondFrenteIva } = formData;\n        return !!(empresaRazonSocial.trim() && empresaTipoCliente.trim() && empresaDomicilio.trim() && empresaLocalidad.trim() && empresaProvincia.trim() && empresaCondFrenteIva.trim());\n    };\n    const validateTab3 = ()=>{\n        return formData.contactos.length > 0 && formData.contactos[0].nombre.trim() !== \"\" && formData.contactos[0].cargo.trim() !== \"\" && formData.contactos[0].telefono.trim() !== \"\" && formData.contactos[0].email.trim() !== \"\";\n    };\n    // Función para habilitar la siguiente pestaña\n    const enableNextTab = (currentTab)=>{\n        const newEnabledTabs = [\n            ...enabledTabs\n        ];\n        if (currentTab + 1 < newEnabledTabs.length) {\n            newEnabledTabs[currentTab + 1] = true;\n            setEnabledTabs(newEnabledTabs);\n        }\n    };\n    // Función para ir a la siguiente pestaña\n    const handleNextTab = ()=>{\n        let canProceed = false;\n        switch(tabValue){\n            case 0:\n                canProceed = validateTab1();\n                break;\n            case 1:\n                canProceed = validateTab2();\n                break;\n            case 2:\n                canProceed = validateTab3();\n                break;\n        }\n        if (canProceed) {\n            enableNextTab(tabValue);\n            setTabValue(tabValue + 1);\n        } else {\n            alert(\"Por favor complete todos los campos requeridos antes de continuar.\");\n        }\n    };\n    // Función para ir a la pestaña anterior\n    const handlePreviousTab = ()=>{\n        if (tabValue > 0) {\n            setTabValue(tabValue - 1);\n        }\n    };\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData((prevData)=>({\n                    ...prevData,\n                    empresaRazonSocial: selectedClient.razonSocial,\n                    empresaTipoCliente: selectedClient.tipoCliente || \"\",\n                    empresaDomicilio: selectedClient.direccion,\n                    empresaLocalidad: localidad,\n                    empresaProvincia: provincia,\n                    empresaCondFrenteIva: selectedClient.condFrenteIva,\n                    // Update contacts array with the selected client's contact information\n                    contactos: [\n                        {\n                            id: 1,\n                            nombre: selectedClient.nombreContacto || \"\",\n                            cargo: selectedClient.cargoContacto || \"\",\n                            telefono: selectedClient.telefono,\n                            email: selectedClient.mail\n                        }\n                    ]\n                }));\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Pestaña 1: Propietario/Persona Física\n        propietarioNombre: \"\",\n        propietarioDocumento: \"\",\n        propietarioTelefono: \"\",\n        propietarioEmail: \"\",\n        usarComoRazonSocial: false,\n        // Pestaña 2: Empresa/Razón Social\n        empresaRazonSocial: \"\",\n        empresaTipoCliente: \"\",\n        empresaDomicilio: \"\",\n        empresaLocalidad: \"\",\n        empresaProvincia: \"\",\n        empresaCondFrenteIva: \"\",\n        // Pestaña 3: Contactos/Encargados (array dinámico)\n        contactos: [\n            {\n                id: 1,\n                nombre: \"\",\n                cargo: \"\",\n                telefono: \"\",\n                email: \"\"\n            }\n        ]\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            // Pestaña 1: Propietario/Persona Física\n            propietarioNombre: \"\",\n            propietarioDocumento: \"\",\n            propietarioTelefono: \"\",\n            propietarioEmail: \"\",\n            usarComoRazonSocial: false,\n            // Pestaña 2: Empresa/Razón Social\n            empresaRazonSocial: \"\",\n            empresaTipoCliente: \"\",\n            empresaDomicilio: \"\",\n            empresaLocalidad: \"\",\n            empresaProvincia: \"\",\n            empresaCondFrenteIva: \"\",\n            // Pestaña 3: Contactos/Encargados\n            contactos: [\n                {\n                    id: 1,\n                    nombre: \"\",\n                    cargo: \"\",\n                    telefono: \"\",\n                    email: \"\"\n                }\n            ]\n        });\n        setError({});\n        setTabValue(0); // Resetear al primer tab\n        setEnabledTabs([\n            true,\n            false,\n            false\n        ]); // Resetear pestañas habilitadas\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Validaciones para campos de texto (nombres, razón social, etc.)\n        if (name === \"propietarioNombre\" || name === \"empresaRazonSocial\" || name === \"empresaLocalidad\" || name.startsWith(\"contacto\") && name.includes(\"nombre\")) {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para domicilio\n        if (name === \"empresaDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para teléfonos\n        if (name === \"propietarioTelefono\" || name.includes(\"telefono\")) {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"propietarioDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>{\n            const newState = {\n                ...prevState,\n                [name]: value\n            };\n            // Si está marcada la opción de usar como razón social, sincronizar datos\n            if (prevState.usarComoRazonSocial) {\n                if (name === \"propietarioNombre\") {\n                    newState.empresaRazonSocial = value;\n                    // También actualizar el primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            nombre: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioTelefono\") {\n                    // Actualizar el teléfono del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            telefono: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioEmail\") {\n                    // Actualizar el email del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            email: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                }\n            }\n            return newState;\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            direccion: formData.empresaDomicilio,\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            lugar: lugar,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            documento: \"\"\n        };\n        // TODO: Add proper form validation if needed\n        // For now, individual field validations are handled in the onChange handlers\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData((prevData)=>({\n                        ...prevData,\n                        empresaRazonSocial: agricultor.razonSocial,\n                        empresaTipoCliente: agricultor.tipoCliente || \"\",\n                        empresaDomicilio: agricultor.direccion,\n                        empresaLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                        empresaProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                        empresaCondFrenteIva: agricultor.condFrenteIva,\n                        contactos: [\n                            {\n                                id: 1,\n                                nombre: agricultor.nombreContacto || \"\",\n                                cargo: agricultor.cargoContacto || \"\",\n                                telefono: agricultor.telefono,\n                                email: agricultor.mail\n                            }\n                        ]\n                    }));\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            direccion: formData.empresaDomicilio,\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            lugar: lugar,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            documento: \"\"\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 944,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 933,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 966,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 955,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 925,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 983,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 982,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 973,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 990,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 972,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1018,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1029,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1017,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1045,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1040,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            borderBottom: 1,\n                                            borderColor: \"divider\",\n                                            mb: 3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            value: tabValue,\n                                            onChange: (event, newValue)=>{\n                                                // Solo permitir cambiar a pestañas habilitadas\n                                                if (enabledTabs[newValue]) {\n                                                    setTabValue(newValue);\n                                                }\n                                            },\n                                            sx: {\n                                                \"& .MuiTab-root\": {\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    fontWeight: 600,\n                                                    textTransform: \"none\",\n                                                    fontSize: \"1rem\"\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDC64 Propietario/Persona F\\xedsica\",\n                                                    sx: {\n                                                        minWidth: 220\n                                                    },\n                                                    disabled: !enabledTabs[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1072,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83C\\uDFE2 Empresa/Raz\\xf3n Social\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1077,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDCDE Contacto/Encargado\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1082,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1055,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1054,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tabValue === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos del Propietario/Persona F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1093,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre Completo *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1106,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan Carlos P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioNombre\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioNombre),\n                                                                helperText: error.propietarioNombre,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioNombre,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioNombre && (error.propietarioNombre ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1125,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1127,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1122,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1109,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"DNI/CUIT *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1137,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 20-12345678-9\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioDocumento\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioDocumento),\n                                                                helperText: error.propietarioDocumento,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioDocumento,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioDocumento && (error.propietarioDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1156,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1158,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1153,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1140,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1136,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioTelefono\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioTelefono),\n                                                                helperText: error.propietarioTelefono,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioTelefono && (error.propietarioTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1186,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1188,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1183,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1171,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1167,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1198,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioEmail\",\n                                                                type: \"email\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioEmail),\n                                                                helperText: error.propietarioEmail,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioEmail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioEmail && (error.propietarioEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1216,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1218,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1213,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1201,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1197,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                mt: 2,\n                                                                p: 2,\n                                                                bgcolor: \"#f5f5f5\",\n                                                                borderRadius: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        cursor: \"pointer\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.usarComoRazonSocial,\n                                                                            onChange: (e)=>{\n                                                                                const checked = e.target.checked;\n                                                                                setFormData((prev)=>{\n                                                                                    var _prev_contactos_;\n                                                                                    return {\n                                                                                        ...prev,\n                                                                                        usarComoRazonSocial: checked,\n                                                                                        // Si se marca, copiar datos del propietario a la empresa\n                                                                                        empresaRazonSocial: checked ? prev.propietarioNombre : \"\",\n                                                                                        // También copiar el primer contacto con los datos del propietario\n                                                                                        contactos: checked ? [\n                                                                                            {\n                                                                                                ...prev.contactos[0],\n                                                                                                nombre: prev.propietarioNombre,\n                                                                                                telefono: prev.propietarioTelefono,\n                                                                                                email: prev.propietarioEmail,\n                                                                                                cargo: ((_prev_contactos_ = prev.contactos[0]) === null || _prev_contactos_ === void 0 ? void 0 : _prev_contactos_.cargo) || \"Propietario\"\n                                                                                            },\n                                                                                            ...prev.contactos.slice(1)\n                                                                                        ] : prev.contactos\n                                                                                    };\n                                                                                });\n                                                                            },\n                                                                            style: {\n                                                                                marginRight: \"8px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1243,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                fontFamily: \"Inter, sans-serif\"\n                                                                            },\n                                                                            children: \"✅ Usar estos datos del propietario para la empresa y contacto principal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1274,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1236,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        color: \"#2E7D32\",\n                                                                        mt: 1,\n                                                                        display: \"block\",\n                                                                        fontStyle: \"italic\"\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCA1 Los datos del propietario se sincronizar\\xe1n autom\\xe1ticamente con la raz\\xf3n social de la empresa y el contacto principal\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1283,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1103,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Cancelar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1311,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1092,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos de la Empresa/Raz\\xf3n Social\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1338,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1351,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Agropecuaria San Juan S.A.\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaRazonSocial\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaRazonSocial),\n                                                                helperText: error.empresaRazonSocial,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaRazonSocial,\n                                                                disabled: formData.usarComoRazonSocial,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaRazonSocial && (error.empresaRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1371,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1373,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1368,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        backgroundColor: formData.usarComoRazonSocial ? \"#f5f5f5\" : \"transparent\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1354,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                sx: {\n                                                                    color: \"#666\",\n                                                                    mt: 1,\n                                                                    display: \"block\"\n                                                                },\n                                                                children: \"\\uD83D\\uDCA1 Autocompletado desde datos del propietario\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1387,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1398,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaTipoCliente\",\n                                                                        value: formData.empresaTipoCliente,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaTipoCliente\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione tipo de cliente\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1421,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            const option = tipoClienteOptions.find((opt)=>opt.value === selected);\n                                                                            return option ? \"\".concat(option.icon, \" \").concat(option.value) : selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1443,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: option.value,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                        sx: {\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\",\n                                                                                            gap: 1\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.icon\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1455,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.value\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1456,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1448,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, option.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1447,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1462,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1401,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1397,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio de la Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1471,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Av. San Mart\\xedn 1234\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaDomicilio\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaDomicilio),\n                                                                helperText: error.empresaDomicilio,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaDomicilio && (error.empresaDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1489,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1491,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1486,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1474,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1501,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaLocalidad\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaLocalidad),\n                                                                helperText: error.empresaLocalidad,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaLocalidad && (error.empresaLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1520,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1522,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1517,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1504,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1500,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1531,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaProvincia\",\n                                                                        value: formData.empresaProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione provincia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1554,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione provincia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1571,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: provincia,\n                                                                                    children: provincia\n                                                                                }, provincia, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1575,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1538,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1581,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1534,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1530,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Condici\\xf3n frente al IVA *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1590,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaCondFrenteIva\",\n                                                                        value: formData.empresaCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione condici\\xf3n IVA\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1613,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1630,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            condFrenteIvaOptions.map((condicion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: condicion,\n                                                                                    children: condicion\n                                                                                }, condicion, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1634,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1597,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1640,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1593,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1589,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1658,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1665,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1649,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1337,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Contactos/Encargados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1683,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            formData.contactos.map((contacto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    sx: {\n                                                        mb: 4,\n                                                        p: 3,\n                                                        border: \"1px solid #e0e0e0\",\n                                                        borderRadius: 2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                justifyContent: \"space-between\",\n                                                                alignItems: \"center\",\n                                                                mb: 2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"subtitle1\",\n                                                                    sx: {\n                                                                        fontWeight: 600,\n                                                                        color: \"#333\"\n                                                                    },\n                                                                    children: [\n                                                                        \"Contacto #\",\n                                                                        index + 1\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1713,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.contactos.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"outlined\",\n                                                                    color: \"error\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>{\n                                                                        setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                contactos: prev.contactos.filter((c)=>c.id !== contacto.id)\n                                                                            }));\n                                                                    },\n                                                                    children: \"Eliminar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1720,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1705,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            container: true,\n                                                            spacing: 3,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre del Encargado *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1741,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Mar\\xeda Gonz\\xe1lez\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_nombre\"),\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_nombre\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_nombre\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].nombre = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.nombre,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.nombre && (error[\"contacto_\".concat(contacto.id, \"_nombre\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1773,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1775,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1770,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1744,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1740,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Cargo (Opcional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1785,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Administrador, Encargado\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_cargo\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].cargo = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.cargo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1788,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1784,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tel\\xe9fono de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1812,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 0000-000000\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_telefono\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_telefono\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_telefono\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].telefono = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.telefono,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.telefono && (error[\"contacto_\".concat(contacto.id, \"_telefono\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1847,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1849,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1842,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1815,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1811,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Email de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1859,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: <EMAIL>\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_email\"),\n                                                                            type: \"email\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_email\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_email\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].email = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.email,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.email && (error[\"contacto_\".concat(contacto.id, \"_email\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1890,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1892,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1887,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1862,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1858,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1738,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, contacto.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1696,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1906,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: ()=>{\n                                                    const newId = Math.max(...formData.contactos.map((c)=>c.id)) + 1;\n                                                    setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactos: [\n                                                                ...prev.contactos,\n                                                                {\n                                                                    id: newId,\n                                                                    nombre: \"\",\n                                                                    cargo: \"\",\n                                                                    telefono: \"\",\n                                                                    email: \"\"\n                                                                }\n                                                            ]\n                                                        }));\n                                                },\n                                                sx: {\n                                                    mt: 2\n                                                },\n                                                children: \"Agregar otro contacto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1904,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1939,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        type: \"submit\",\n                                                        disabled: isSubmitting,\n                                                        onClick: handleAddCliente,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: isSubmitting ? \"Guardando...\" : estadoModal === \"add\" ? \"Registrar\" : \"Actualizar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1946,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1930,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1682,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1052,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1047,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 1015,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 1002,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"BbVUwd51WSh2d/v81WqeKFo1Vys=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});