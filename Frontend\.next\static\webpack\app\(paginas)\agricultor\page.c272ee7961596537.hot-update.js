"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* harmony import */ var _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/Person */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [enabledTabs, setEnabledTabs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        true,\n        false,\n        false\n    ]); // Solo la primera pestaña habilitada inicialmente\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Funciones de validación para cada pestaña\n    const validateTab1 = ()=>{\n        const { propietarioNombre, propietarioDocumento, propietarioTelefono, propietarioEmail } = formData;\n        return !!(propietarioNombre.trim() && propietarioDocumento.trim() && propietarioTelefono.trim() && propietarioEmail.trim());\n    };\n    const validateTab2 = ()=>{\n        const { empresaRazonSocial, empresaTipoCliente, empresaDomicilio, empresaLocalidad, empresaProvincia, empresaCondFrenteIva } = formData;\n        return !!(empresaRazonSocial.trim() && empresaTipoCliente.trim() && empresaDomicilio.trim() && empresaLocalidad.trim() && empresaProvincia.trim() && empresaCondFrenteIva.trim());\n    };\n    const validateTab3 = ()=>{\n        return formData.contactos.length > 0 && formData.contactos[0].nombre.trim() !== \"\" && formData.contactos[0].cargo.trim() !== \"\" && formData.contactos[0].telefono.trim() !== \"\" && formData.contactos[0].email.trim() !== \"\";\n    };\n    // Función para habilitar la siguiente pestaña\n    const enableNextTab = (currentTab)=>{\n        const newEnabledTabs = [\n            ...enabledTabs\n        ];\n        if (currentTab + 1 < newEnabledTabs.length) {\n            newEnabledTabs[currentTab + 1] = true;\n            setEnabledTabs(newEnabledTabs);\n        }\n    };\n    // Función para ir a la siguiente pestaña\n    const handleNextTab = ()=>{\n        let canProceed = false;\n        switch(tabValue){\n            case 0:\n                canProceed = validateTab1();\n                break;\n            case 1:\n                canProceed = validateTab2();\n                break;\n            case 2:\n                canProceed = validateTab3();\n                break;\n        }\n        if (canProceed) {\n            enableNextTab(tabValue);\n            setTabValue(tabValue + 1);\n        } else {\n            alert(\"Por favor complete todos los campos requeridos antes de continuar.\");\n        }\n    };\n    // Función para ir a la pestaña anterior\n    const handlePreviousTab = ()=>{\n        if (tabValue > 0) {\n            setTabValue(tabValue - 1);\n        }\n    };\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData((prevData)=>({\n                    ...prevData,\n                    empresaRazonSocial: selectedClient.razonSocial,\n                    empresaTipoCliente: selectedClient.tipoCliente || \"\",\n                    empresaDomicilio: selectedClient.direccion,\n                    empresaLocalidad: localidad,\n                    empresaProvincia: provincia,\n                    empresaCondFrenteIva: selectedClient.condFrenteIva,\n                    // Update contacts array with the selected client's contact information\n                    contactos: [\n                        {\n                            id: 1,\n                            nombre: selectedClient.nombreContacto || \"\",\n                            cargo: selectedClient.cargoContacto || \"\",\n                            telefono: selectedClient.telefono,\n                            email: selectedClient.mail\n                        }\n                    ]\n                }));\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Pestaña 1: Propietario/Persona Física\n        propietarioNombre: \"\",\n        propietarioDocumento: \"\",\n        propietarioTelefono: \"\",\n        propietarioEmail: \"\",\n        usarComoRazonSocial: false,\n        // Pestaña 2: Empresa/Razón Social\n        empresaRazonSocial: \"\",\n        empresaTipoCliente: \"\",\n        empresaDomicilio: \"\",\n        empresaLocalidad: \"\",\n        empresaProvincia: \"\",\n        empresaCondFrenteIva: \"\",\n        // Pestaña 3: Contactos/Encargados (array dinámico)\n        contactos: [\n            {\n                id: 1,\n                nombre: \"\",\n                cargo: \"\",\n                telefono: \"\",\n                email: \"\"\n            }\n        ]\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            // Pestaña 1: Propietario/Persona Física\n            propietarioNombre: \"\",\n            propietarioDocumento: \"\",\n            propietarioTelefono: \"\",\n            propietarioEmail: \"\",\n            usarComoRazonSocial: false,\n            // Pestaña 2: Empresa/Razón Social\n            empresaRazonSocial: \"\",\n            empresaTipoCliente: \"\",\n            empresaDomicilio: \"\",\n            empresaLocalidad: \"\",\n            empresaProvincia: \"\",\n            empresaCondFrenteIva: \"\",\n            // Pestaña 3: Contactos/Encargados\n            contactos: [\n                {\n                    id: 1,\n                    nombre: \"\",\n                    cargo: \"\",\n                    telefono: \"\",\n                    email: \"\"\n                }\n            ]\n        });\n        setError({});\n        setTabValue(0); // Resetear al primer tab\n        setEnabledTabs([\n            true,\n            false,\n            false\n        ]); // Resetear pestañas habilitadas\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Validaciones para campos de texto (nombres, razón social, etc.)\n        if (name === \"propietarioNombre\" || name === \"empresaRazonSocial\" || name === \"empresaLocalidad\" || name.startsWith(\"contacto\") && name.includes(\"nombre\")) {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para domicilio\n        if (name === \"empresaDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para teléfonos\n        if (name === \"propietarioTelefono\" || name.includes(\"telefono\")) {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"propietarioDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>{\n            const newState = {\n                ...prevState,\n                [name]: value\n            };\n            // Si está marcada la opción de usar como razón social, sincronizar datos\n            if (prevState.usarComoRazonSocial) {\n                if (name === \"propietarioNombre\") {\n                    newState.empresaRazonSocial = value;\n                    // También actualizar el primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            nombre: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioTelefono\") {\n                    // Actualizar el teléfono del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            telefono: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioEmail\") {\n                    // Actualizar el email del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            email: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                }\n            }\n            return newState;\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 566,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            // Datos del Propietario/Persona Física (Pestaña 1)\n            propietarioNombre: formData.propietarioNombre,\n            propietarioDocumento: formData.propietarioDocumento,\n            propietarioTelefono: formData.propietarioTelefono,\n            propietarioEmail: formData.propietarioEmail,\n            // Datos de la Empresa/Razón Social (Pestaña 2)\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            direccion: formData.empresaDomicilio,\n            empresaLocalidad: formData.empresaLocalidad,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            // Datos del Contacto/Encargado Principal (Pestaña 3)\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            // Campos calculados/derivados\n            lugar: lugar,\n            documento: formData.propietarioDocumento\n        };\n        // TODO: Add proper form validation if needed\n        // For now, individual field validations are handled in the onChange handlers\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData((prevData)=>({\n                        ...prevData,\n                        empresaRazonSocial: agricultor.razonSocial,\n                        empresaTipoCliente: agricultor.tipoCliente || \"\",\n                        empresaDomicilio: agricultor.direccion,\n                        empresaLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                        empresaProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                        empresaCondFrenteIva: agricultor.condFrenteIva,\n                        contactos: [\n                            {\n                                id: 1,\n                                nombre: agricultor.nombreContacto || \"\",\n                                cargo: agricultor.cargoContacto || \"\",\n                                telefono: agricultor.telefono,\n                                email: agricultor.mail\n                            }\n                        ]\n                    }));\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            // Datos del Propietario/Persona Física (Pestaña 1)\n            propietarioNombre: formData.propietarioNombre,\n            propietarioDocumento: formData.propietarioDocumento,\n            propietarioTelefono: formData.propietarioTelefono,\n            propietarioEmail: formData.propietarioEmail,\n            // Datos de la Empresa/Razón Social (Pestaña 2)\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            direccion: formData.empresaDomicilio,\n            empresaLocalidad: formData.empresaLocalidad,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            // Datos del Contacto/Encargado Principal (Pestaña 3)\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            // Campos calculados/derivados\n            lugar: lugar,\n            documento: formData.propietarioDocumento\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 973,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 983,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 972,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1005,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 994,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 964,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1022,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 1012,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 1029,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 1011,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1057,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1056,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1084,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1079,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            borderBottom: 1,\n                                            borderColor: \"divider\",\n                                            mb: 3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            value: tabValue,\n                                            onChange: (event, newValue)=>{\n                                                // Solo permitir cambiar a pestañas habilitadas\n                                                if (enabledTabs[newValue]) {\n                                                    setTabValue(newValue);\n                                                }\n                                            },\n                                            sx: {\n                                                \"& .MuiTab-root\": {\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    fontWeight: 600,\n                                                    textTransform: \"none\",\n                                                    fontSize: \"1rem\"\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDC64 Propietario/Persona F\\xedsica\",\n                                                    sx: {\n                                                        minWidth: 220\n                                                    },\n                                                    disabled: !enabledTabs[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1111,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83C\\uDFE2 Empresa/Raz\\xf3n Social\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1116,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDCDE Contacto/Encargado\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1094,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1093,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tabValue === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos del Propietario/Persona F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1132,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre Completo *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1145,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan Carlos P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioNombre\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioNombre),\n                                                                helperText: error.propietarioNombre,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioNombre,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioNombre && (error.propietarioNombre ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1164,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1166,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1161,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1148,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1144,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"DNI/CUIT *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1176,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 20-12345678-9\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioDocumento\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioDocumento),\n                                                                helperText: error.propietarioDocumento,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioDocumento,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioDocumento && (error.propietarioDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1195,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1197,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1192,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1179,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1175,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1207,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioTelefono\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioTelefono),\n                                                                helperText: error.propietarioTelefono,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioTelefono && (error.propietarioTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1225,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1227,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1222,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1210,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1206,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1237,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioEmail\",\n                                                                type: \"email\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioEmail),\n                                                                helperText: error.propietarioEmail,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioEmail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioEmail && (error.propietarioEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1255,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1257,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1252,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1240,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                mt: 2,\n                                                                p: 2,\n                                                                bgcolor: \"#f5f5f5\",\n                                                                borderRadius: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        cursor: \"pointer\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.usarComoRazonSocial,\n                                                                            onChange: (e)=>{\n                                                                                const checked = e.target.checked;\n                                                                                setFormData((prev)=>{\n                                                                                    var _prev_contactos_;\n                                                                                    return {\n                                                                                        ...prev,\n                                                                                        usarComoRazonSocial: checked,\n                                                                                        // Si se marca, copiar datos del propietario a la empresa\n                                                                                        empresaRazonSocial: checked ? prev.propietarioNombre : \"\",\n                                                                                        // También copiar el primer contacto con los datos del propietario\n                                                                                        contactos: checked ? [\n                                                                                            {\n                                                                                                ...prev.contactos[0],\n                                                                                                nombre: prev.propietarioNombre,\n                                                                                                telefono: prev.propietarioTelefono,\n                                                                                                email: prev.propietarioEmail,\n                                                                                                cargo: ((_prev_contactos_ = prev.contactos[0]) === null || _prev_contactos_ === void 0 ? void 0 : _prev_contactos_.cargo) || \"Propietario\"\n                                                                                            },\n                                                                                            ...prev.contactos.slice(1)\n                                                                                        ] : prev.contactos\n                                                                                    };\n                                                                                });\n                                                                            },\n                                                                            style: {\n                                                                                marginRight: \"8px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1282,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                fontFamily: \"Inter, sans-serif\"\n                                                                            },\n                                                                            children: \"✅ Usar estos datos del propietario para la empresa y contacto principal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1313,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1275,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        color: \"#2E7D32\",\n                                                                        mt: 1,\n                                                                        display: \"block\",\n                                                                        fontStyle: \"italic\"\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCA1 Los datos del propietario se sincronizar\\xe1n autom\\xe1ticamente con la raz\\xf3n social de la empresa y el contacto principal\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1322,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1267,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1266,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1142,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Cancelar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1341,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1131,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos de la Empresa/Raz\\xf3n Social\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1377,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1390,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Agropecuaria San Juan S.A.\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaRazonSocial\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaRazonSocial),\n                                                                helperText: error.empresaRazonSocial,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaRazonSocial,\n                                                                disabled: formData.usarComoRazonSocial,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaRazonSocial && (error.empresaRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1410,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1412,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1407,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        backgroundColor: formData.usarComoRazonSocial ? \"#f5f5f5\" : \"transparent\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1393,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                sx: {\n                                                                    color: \"#666\",\n                                                                    mt: 1,\n                                                                    display: \"block\"\n                                                                },\n                                                                children: \"\\uD83D\\uDCA1 Autocompletado desde datos del propietario\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1426,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1437,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaTipoCliente\",\n                                                                        value: formData.empresaTipoCliente,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaTipoCliente\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione tipo de cliente\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1460,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            const option = tipoClienteOptions.find((opt)=>opt.value === selected);\n                                                                            return option ? \"\".concat(option.icon, \" \").concat(option.value) : selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1482,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: option.value,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                        sx: {\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\",\n                                                                                            gap: 1\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.icon\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1494,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.value\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1495,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1487,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, option.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1486,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1444,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1501,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1440,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1436,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio de la Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1510,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Av. San Mart\\xedn 1234\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaDomicilio\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaDomicilio),\n                                                                helperText: error.empresaDomicilio,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaDomicilio && (error.empresaDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1528,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1530,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1525,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1513,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1509,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1540,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaLocalidad\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaLocalidad),\n                                                                helperText: error.empresaLocalidad,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaLocalidad && (error.empresaLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1559,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1561,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1556,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1543,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1539,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1570,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaProvincia\",\n                                                                        value: formData.empresaProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione provincia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1593,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione provincia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1610,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: provincia,\n                                                                                    children: provincia\n                                                                                }, provincia, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1614,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1577,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1620,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1573,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1569,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Condici\\xf3n frente al IVA *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1629,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaCondFrenteIva\",\n                                                                        value: formData.empresaCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione condici\\xf3n IVA\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1652,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1669,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            condFrenteIvaOptions.map((condicion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: condicion,\n                                                                                    children: condicion\n                                                                                }, condicion, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1673,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1636,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1679,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1632,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1628,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1387,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1697,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1704,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1688,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1376,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Contactos/Encargados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1722,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            formData.contactos.map((contacto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    sx: {\n                                                        mb: 4,\n                                                        p: 3,\n                                                        border: \"1px solid #e0e0e0\",\n                                                        borderRadius: 2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                justifyContent: \"space-between\",\n                                                                alignItems: \"center\",\n                                                                mb: 2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    sx: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        gap: 1\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#2E7D32\",\n                                                                                fontSize: \"1.2rem\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1755,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"subtitle1\",\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#333\"\n                                                                            },\n                                                                            children: [\n                                                                                \"Contacto #\",\n                                                                                index + 1\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1761,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1752,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.contactos.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"outlined\",\n                                                                    color: \"error\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>{\n                                                                        setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                contactos: prev.contactos.filter((c)=>c.id !== contacto.id)\n                                                                            }));\n                                                                    },\n                                                                    children: \"Eliminar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1769,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1744,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            container: true,\n                                                            spacing: 3,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre del Encargado *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1790,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Mar\\xeda Gonz\\xe1lez\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_nombre\"),\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_nombre\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_nombre\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].nombre = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.nombre,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.nombre && (error[\"contacto_\".concat(contacto.id, \"_nombre\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1822,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1824,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1819,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1793,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1789,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Cargo (Opcional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1834,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Administrador, Encargado\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_cargo\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].cargo = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.cargo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1837,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1833,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tel\\xe9fono de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1861,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 0000-000000\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_telefono\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_telefono\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_telefono\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].telefono = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.telefono,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.telefono && (error[\"contacto_\".concat(contacto.id, \"_telefono\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1896,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1898,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1891,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1864,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1860,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Email de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1908,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: <EMAIL>\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_email\"),\n                                                                            type: \"email\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_email\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_email\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].email = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.email,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.email && (error[\"contacto_\".concat(contacto.id, \"_email\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1939,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1941,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1936,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1911,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1907,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1787,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, contacto.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1735,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1955,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: ()=>{\n                                                    const newId = Math.max(...formData.contactos.map((c)=>c.id)) + 1;\n                                                    setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactos: [\n                                                                ...prev.contactos,\n                                                                {\n                                                                    id: newId,\n                                                                    nombre: \"\",\n                                                                    cargo: \"\",\n                                                                    telefono: \"\",\n                                                                    email: \"\"\n                                                                }\n                                                            ]\n                                                        }));\n                                                },\n                                                sx: {\n                                                    mt: 2\n                                                },\n                                                children: \"Agregar otro contacto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1953,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1988,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        type: \"submit\",\n                                                        disabled: isSubmitting,\n                                                        onClick: handleAddCliente,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: isSubmitting ? \"Guardando...\" : estadoModal === \"add\" ? \"Registrar\" : \"Actualizar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1995,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1979,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1721,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1091,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1086,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 1054,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 1041,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"BbVUwd51WSh2d/v81WqeKFo1Vys=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});