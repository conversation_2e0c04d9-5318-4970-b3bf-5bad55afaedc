"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [enabledTabs, setEnabledTabs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        true,\n        false,\n        false\n    ]); // Solo la primera pestaña habilitada inicialmente\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Funciones de validación para cada pestaña\n    const validateTab1 = ()=>{\n        const { propietarioNombre, propietarioDocumento, propietarioTelefono, propietarioEmail } = formData;\n        return !!(propietarioNombre.trim() && propietarioDocumento.trim() && propietarioTelefono.trim() && propietarioEmail.trim());\n    };\n    const validateTab2 = ()=>{\n        const { empresaRazonSocial, empresaTipoCliente, empresaDomicilio, empresaLocalidad, empresaProvincia, empresaCondFrenteIva } = formData;\n        return !!(empresaRazonSocial.trim() && empresaTipoCliente.trim() && empresaDomicilio.trim() && empresaLocalidad.trim() && empresaProvincia.trim() && empresaCondFrenteIva.trim());\n    };\n    const validateTab3 = ()=>{\n        return formData.contactos.length > 0 && formData.contactos[0].nombre.trim() !== \"\" && formData.contactos[0].cargo.trim() !== \"\" && formData.contactos[0].telefono.trim() !== \"\" && formData.contactos[0].email.trim() !== \"\";\n    };\n    // Función para habilitar la siguiente pestaña\n    const enableNextTab = (currentTab)=>{\n        const newEnabledTabs = [\n            ...enabledTabs\n        ];\n        if (currentTab + 1 < newEnabledTabs.length) {\n            newEnabledTabs[currentTab + 1] = true;\n            setEnabledTabs(newEnabledTabs);\n        }\n    };\n    // Función para ir a la siguiente pestaña\n    const handleNextTab = ()=>{\n        let canProceed = false;\n        switch(tabValue){\n            case 0:\n                canProceed = validateTab1();\n                break;\n            case 1:\n                canProceed = validateTab2();\n                break;\n            case 2:\n                canProceed = validateTab3();\n                break;\n        }\n        if (canProceed) {\n            enableNextTab(tabValue);\n            setTabValue(tabValue + 1);\n        } else {\n            alert(\"Por favor complete todos los campos requeridos antes de continuar.\");\n        }\n    };\n    // Función para ir a la pestaña anterior\n    const handlePreviousTab = ()=>{\n        if (tabValue > 0) {\n            setTabValue(tabValue - 1);\n        }\n    };\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData((prevData)=>({\n                    ...prevData,\n                    empresaRazonSocial: selectedClient.razonSocial,\n                    empresaTipoCliente: selectedClient.tipoCliente || \"\",\n                    empresaDomicilio: selectedClient.direccion,\n                    empresaLocalidad: localidad,\n                    empresaProvincia: provincia,\n                    empresaCondFrenteIva: selectedClient.condFrenteIva,\n                    // Update contacts array with the selected client's contact information\n                    contactos: [\n                        {\n                            id: 1,\n                            nombre: selectedClient.nombreContacto || \"\",\n                            cargo: selectedClient.cargoContacto || \"\",\n                            telefono: selectedClient.telefono,\n                            email: selectedClient.mail\n                        }\n                    ]\n                }));\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Pestaña 1: Propietario/Persona Física\n        propietarioNombre: \"\",\n        propietarioDocumento: \"\",\n        propietarioTelefono: \"\",\n        propietarioEmail: \"\",\n        usarComoRazonSocial: false,\n        // Pestaña 2: Empresa/Razón Social\n        empresaRazonSocial: \"\",\n        empresaTipoCliente: \"\",\n        empresaDomicilio: \"\",\n        empresaLocalidad: \"\",\n        empresaProvincia: \"\",\n        empresaCondFrenteIva: \"\",\n        // Pestaña 3: Contactos/Encargados (array dinámico)\n        contactos: [\n            {\n                id: 1,\n                nombre: \"\",\n                cargo: \"\",\n                telefono: \"\",\n                email: \"\"\n            }\n        ]\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            // Pestaña 1: Propietario/Persona Física\n            propietarioNombre: \"\",\n            propietarioDocumento: \"\",\n            propietarioTelefono: \"\",\n            propietarioEmail: \"\",\n            usarComoRazonSocial: false,\n            // Pestaña 2: Empresa/Razón Social\n            empresaRazonSocial: \"\",\n            empresaTipoCliente: \"\",\n            empresaDomicilio: \"\",\n            empresaLocalidad: \"\",\n            empresaProvincia: \"\",\n            empresaCondFrenteIva: \"\",\n            // Pestaña 3: Contactos/Encargados\n            contactos: [\n                {\n                    id: 1,\n                    nombre: \"\",\n                    cargo: \"\",\n                    telefono: \"\",\n                    email: \"\"\n                }\n            ]\n        });\n        setError({});\n        setTabValue(0); // Resetear al primer tab\n        setEnabledTabs([\n            true,\n            false,\n            false\n        ]); // Resetear pestañas habilitadas\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Validaciones para campos de texto (nombres, razón social, etc.)\n        if (name === \"propietarioNombre\" || name === \"empresaRazonSocial\" || name === \"empresaLocalidad\" || name.startsWith(\"contacto\") && name.includes(\"nombre\")) {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para domicilio\n        if (name === \"empresaDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para teléfonos\n        if (name === \"propietarioTelefono\" || name.includes(\"telefono\")) {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"propietarioDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>({\n                ...prevState,\n                [name]: value\n            }));\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 515,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            direccion: formData.empresaDomicilio,\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            lugar: lugar,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            documento: \"\"\n        };\n        // TODO: Add proper form validation if needed\n        // For now, individual field validations are handled in the onChange handlers\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData((prevData)=>({\n                        ...prevData,\n                        empresaRazonSocial: agricultor.razonSocial,\n                        empresaTipoCliente: agricultor.tipoCliente || \"\",\n                        empresaDomicilio: agricultor.direccion,\n                        empresaLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                        empresaProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                        empresaCondFrenteIva: agricultor.condFrenteIva,\n                        contactos: [\n                            {\n                                id: 1,\n                                nombre: agricultor.nombreContacto || \"\",\n                                cargo: agricultor.cargoContacto || \"\",\n                                telefono: agricultor.telefono,\n                                email: agricultor.mail\n                            }\n                        ]\n                    }));\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            direccion: formData.empresaDomicilio,\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            lugar: lugar,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            documento: \"\"\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 897,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 907,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 896,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 929,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 918,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 888,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 944,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 936,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 953,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 935,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1008,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1003,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            borderBottom: 1,\n                                            borderColor: \"divider\",\n                                            mb: 3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            value: tabValue,\n                                            onChange: (event, newValue)=>{\n                                                // Solo permitir cambiar a pestañas habilitadas\n                                                if (enabledTabs[newValue]) {\n                                                    setTabValue(newValue);\n                                                }\n                                            },\n                                            sx: {\n                                                \"& .MuiTab-root\": {\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    fontWeight: 600,\n                                                    textTransform: \"none\",\n                                                    fontSize: \"1rem\"\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDC64 Propietario/Persona F\\xedsica\",\n                                                    sx: {\n                                                        minWidth: 220\n                                                    },\n                                                    disabled: !enabledTabs[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1035,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83C\\uDFE2 Empresa/Raz\\xf3n Social\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1040,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDCDE Contacto/Encargado\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1017,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tabValue === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos del Propietario/Persona F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre Completo *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1069,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan Carlos P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioNombre\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioNombre),\n                                                                helperText: error.propietarioNombre,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioNombre,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioNombre && (error.propietarioNombre ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1088,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1090,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1085,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1072,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1068,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"DNI/CUIT *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 20-12345678-9\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioDocumento\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioDocumento),\n                                                                helperText: error.propietarioDocumento,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioDocumento,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioDocumento && (error.propietarioDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1119,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1121,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1116,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1103,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioTelefono\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioTelefono),\n                                                                helperText: error.propietarioTelefono,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioTelefono && (error.propietarioTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1149,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1151,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1146,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1134,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1130,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1161,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioEmail\",\n                                                                type: \"email\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioEmail),\n                                                                helperText: error.propietarioEmail,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioEmail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioEmail && (error.propietarioEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1179,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1181,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1176,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1164,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1160,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                mt: 2,\n                                                                p: 2,\n                                                                bgcolor: \"#f5f5f5\",\n                                                                borderRadius: 1\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    cursor: \"pointer\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: formData.usarComoRazonSocial,\n                                                                        onChange: (e)=>{\n                                                                            const checked = e.target.checked;\n                                                                            setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    usarComoRazonSocial: checked,\n                                                                                    empresaRazonSocial: checked ? prev.propietarioNombre : prev.empresaRazonSocial\n                                                                                }));\n                                                                        },\n                                                                        style: {\n                                                                            marginRight: \"8px\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1206,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        sx: {\n                                                                            fontFamily: \"Inter, sans-serif\"\n                                                                        },\n                                                                        children: \"✅ Usar estos datos tambi\\xe9n como raz\\xf3n social de la empresa\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1221,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1199,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1191,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"flex-end\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    variant: \"contained\",\n                                                    onClick: handleNextTab,\n                                                    sx: {\n                                                        minWidth: 120,\n                                                        bgcolor: \"#2E7D32\",\n                                                        \"&:hover\": {\n                                                            bgcolor: \"#1B5E20\"\n                                                        }\n                                                    },\n                                                    children: \"Siguiente\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1243,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1234,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos de la Empresa/Raz\\xf3n Social\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1261,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1274,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Agropecuaria San Juan S.A.\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaRazonSocial\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaRazonSocial),\n                                                                helperText: error.empresaRazonSocial,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaRazonSocial,\n                                                                disabled: formData.usarComoRazonSocial,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaRazonSocial && (error.empresaRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1294,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1296,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1291,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        backgroundColor: formData.usarComoRazonSocial ? \"#f5f5f5\" : \"transparent\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1277,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                sx: {\n                                                                    color: \"#666\",\n                                                                    mt: 1,\n                                                                    display: \"block\"\n                                                                },\n                                                                children: \"\\uD83D\\uDCA1 Autocompletado desde datos del propietario\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1310,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1321,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaTipoCliente\",\n                                                                        value: formData.empresaTipoCliente,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaTipoCliente\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione tipo de cliente\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1344,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            const option = tipoClienteOptions.find((opt)=>opt.value === selected);\n                                                                            return option ? \"\".concat(option.icon, \" \").concat(option.value) : selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1366,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: option.value,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                        sx: {\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\",\n                                                                                            gap: 1\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.icon\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1378,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.value\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1379,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1371,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, option.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1370,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1328,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1385,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1324,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio de la Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1394,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Av. San Mart\\xedn 1234\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaDomicilio\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaDomicilio),\n                                                                helperText: error.empresaDomicilio,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaDomicilio && (error.empresaDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1412,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1414,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1409,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1397,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1393,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1424,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaLocalidad\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaLocalidad),\n                                                                helperText: error.empresaLocalidad,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaLocalidad && (error.empresaLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1443,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1445,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1440,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1427,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1454,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaProvincia\",\n                                                                        value: formData.empresaProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione provincia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1477,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione provincia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1494,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: provincia,\n                                                                                    children: provincia\n                                                                                }, provincia, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1498,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1461,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1504,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1457,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1453,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Condici\\xf3n frente al IVA *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1513,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaCondFrenteIva\",\n                                                                        value: formData.empresaCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione condici\\xf3n IVA\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1536,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1553,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            condFrenteIvaOptions.map((condicion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: condicion,\n                                                                                    children: condicion\n                                                                                }, condicion, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1557,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1520,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1563,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1516,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1512,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1260,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Contactos/Encargados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1576,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            formData.contactos.map((contacto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    sx: {\n                                                        mb: 4,\n                                                        p: 3,\n                                                        border: \"1px solid #e0e0e0\",\n                                                        borderRadius: 2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                justifyContent: \"space-between\",\n                                                                alignItems: \"center\",\n                                                                mb: 2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"subtitle1\",\n                                                                    sx: {\n                                                                        fontWeight: 600,\n                                                                        color: \"#333\"\n                                                                    },\n                                                                    children: [\n                                                                        \"Contacto #\",\n                                                                        index + 1\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1606,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.contactos.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"outlined\",\n                                                                    color: \"error\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>{\n                                                                        setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                contactos: prev.contactos.filter((c)=>c.id !== contacto.id)\n                                                                            }));\n                                                                    },\n                                                                    children: \"Eliminar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1613,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1598,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            container: true,\n                                                            spacing: 3,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre del Encargado *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1634,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Mar\\xeda Gonz\\xe1lez\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_nombre\"),\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_nombre\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_nombre\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].nombre = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.nombre,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.nombre && (error[\"contacto_\".concat(contacto.id, \"_nombre\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1666,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1668,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1663,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1637,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1633,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Cargo (Opcional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1678,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Administrador, Encargado\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_cargo\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].cargo = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.cargo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1681,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1677,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tel\\xe9fono de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1705,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 0000-000000\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_telefono\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_telefono\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_telefono\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].telefono = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.telefono,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.telefono && (error[\"contacto_\".concat(contacto.id, \"_telefono\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1740,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1742,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1735,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1708,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1704,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Email de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1752,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: <EMAIL>\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_email\"),\n                                                                            type: \"email\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_email\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_email\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].email = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.email,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.email && (error[\"contacto_\".concat(contacto.id, \"_email\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1783,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1785,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1780,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1755,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1751,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1631,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, contacto.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1589,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1799,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: ()=>{\n                                                    const newId = Math.max(...formData.contactos.map((c)=>c.id)) + 1;\n                                                    setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactos: [\n                                                                ...prev.contactos,\n                                                                {\n                                                                    id: newId,\n                                                                    nombre: \"\",\n                                                                    cargo: \"\",\n                                                                    telefono: \"\",\n                                                                    email: \"\"\n                                                                }\n                                                            ]\n                                                        }));\n                                                },\n                                                sx: {\n                                                    mt: 2\n                                                },\n                                                children: \"Agregar otro contacto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1797,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1575,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            mt: 4,\n                                            pt: 3,\n                                            borderTop: \"1px solid #e0e0e0\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                sx: {\n                                                    minWidth: 120\n                                                },\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1834,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"contained\",\n                                                type: \"submit\",\n                                                disabled: isSubmitting,\n                                                sx: {\n                                                    minWidth: 120,\n                                                    bgcolor: \"#2E7D32\",\n                                                    \"&:hover\": {\n                                                        bgcolor: \"#1B5E20\"\n                                                    }\n                                                },\n                                                children: isSubmitting ? \"Guardando...\" : estadoModal === \"add\" ? \"Registrar\" : \"Actualizar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1843,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1825,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1015,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1010,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 978,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 965,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"BbVUwd51WSh2d/v81WqeKFo1Vys=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});