globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(paginas)/dashboard/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/ThemeProviderWrapper.tsx":{"*":{"id":"(ssr)/./src/app/components/ThemeProviderWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(ssr)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/container/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/container/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(paginas)/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/(paginas)/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/menu/MenuPrincipal.tsx":{"*":{"id":"(ssr)/./src/app/components/menu/MenuPrincipal.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":{"*":{"id":"(ssr)/./src/app/(paginas)/agricultor/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Lexend\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"lexend\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Lexend\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"lexend\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\components\\ThemeProviderWrapper.tsx":{"id":"(app-pages-browser)/./src/app/components/ThemeProviderWrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\auth\\container\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/container/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\(paginas)\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/(paginas)/dashboard/page.tsx","name":"*","chunks":["app/(paginas)/dashboard/page","static/chunks/app/(paginas)/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\components\\menu\\MenuPrincipal.tsx":{"id":"(app-pages-browser)/./src/app/components/menu/MenuPrincipal.tsx","name":"*","chunks":["app/(paginas)/layout","static/chunks/app/(paginas)/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\(paginas)\\agricultor\\page.tsx":{"id":"(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\":[],"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\(paginas)\\layout":[],"C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\(paginas)\\dashboard\\page":[]}}