"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [enabledTabs, setEnabledTabs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        true,\n        false,\n        false\n    ]); // Solo la primera pestaña habilitada inicialmente\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData((prevData)=>({\n                    ...prevData,\n                    empresaRazonSocial: selectedClient.razonSocial,\n                    empresaTipoCliente: selectedClient.tipoCliente || \"\",\n                    empresaDomicilio: selectedClient.direccion,\n                    empresaLocalidad: localidad,\n                    empresaProvincia: provincia,\n                    empresaCondFrenteIva: selectedClient.condFrenteIva,\n                    // Update contacts array with the selected client's contact information\n                    contactos: [\n                        {\n                            id: 1,\n                            nombre: selectedClient.nombreContacto || \"\",\n                            cargo: selectedClient.cargoContacto || \"\",\n                            telefono: selectedClient.telefono,\n                            email: selectedClient.mail\n                        }\n                    ]\n                }));\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Pestaña 1: Propietario/Persona Física\n        propietarioNombre: \"\",\n        propietarioDocumento: \"\",\n        propietarioTelefono: \"\",\n        propietarioEmail: \"\",\n        usarComoRazonSocial: false,\n        // Pestaña 2: Empresa/Razón Social\n        empresaRazonSocial: \"\",\n        empresaTipoCliente: \"\",\n        empresaDomicilio: \"\",\n        empresaLocalidad: \"\",\n        empresaProvincia: \"\",\n        empresaCondFrenteIva: \"\",\n        // Pestaña 3: Contactos/Encargados (array dinámico)\n        contactos: [\n            {\n                id: 1,\n                nombre: \"\",\n                cargo: \"\",\n                telefono: \"\",\n                email: \"\"\n            }\n        ]\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            // Pestaña 1: Propietario/Persona Física\n            propietarioNombre: \"\",\n            propietarioDocumento: \"\",\n            propietarioTelefono: \"\",\n            propietarioEmail: \"\",\n            usarComoRazonSocial: false,\n            // Pestaña 2: Empresa/Razón Social\n            empresaRazonSocial: \"\",\n            empresaTipoCliente: \"\",\n            empresaDomicilio: \"\",\n            empresaLocalidad: \"\",\n            empresaProvincia: \"\",\n            empresaCondFrenteIva: \"\",\n            // Pestaña 3: Contactos/Encargados\n            contactos: [\n                {\n                    id: 1,\n                    nombre: \"\",\n                    cargo: \"\",\n                    telefono: \"\",\n                    email: \"\"\n                }\n            ]\n        });\n        setError({});\n        setTabValue(0); // Resetear al primer tab\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Validaciones para campos de texto (nombres, razón social, etc.)\n        if (name === \"propietarioNombre\" || name === \"empresaRazonSocial\" || name === \"empresaLocalidad\" || name.startsWith(\"contacto\") && name.includes(\"nombre\")) {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para domicilio\n        if (name === \"empresaDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para teléfonos\n        if (name === \"propietarioTelefono\" || name.includes(\"telefono\")) {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"propietarioDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>({\n                ...prevState,\n                [name]: value\n            }));\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            direccion: formData.empresaDomicilio,\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            lugar: lugar,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            documento: \"\"\n        };\n        // TODO: Add proper form validation if needed\n        // For now, individual field validations are handled in the onChange handlers\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData((prevData)=>({\n                        ...prevData,\n                        empresaRazonSocial: agricultor.razonSocial,\n                        empresaTipoCliente: agricultor.tipoCliente || \"\",\n                        empresaDomicilio: agricultor.direccion,\n                        empresaLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                        empresaProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                        empresaCondFrenteIva: agricultor.condFrenteIva,\n                        contactos: [\n                            {\n                                id: 1,\n                                nombre: agricultor.nombreContacto || \"\",\n                                cargo: agricultor.cargoContacto || \"\",\n                                telefono: agricultor.telefono,\n                                email: agricultor.mail\n                            }\n                        ]\n                    }));\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            direccion: formData.empresaDomicilio,\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            lugar: lugar,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            documento: \"\"\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 819,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 808,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 830,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 800,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 856,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 848,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 865,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 847,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 892,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 920,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 915,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            borderBottom: 1,\n                                            borderColor: \"divider\",\n                                            mb: 3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            value: tabValue,\n                                            onChange: (event, newValue)=>setTabValue(newValue),\n                                            sx: {\n                                                \"& .MuiTab-root\": {\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    fontWeight: 600,\n                                                    textTransform: \"none\",\n                                                    fontSize: \"1rem\"\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDC64 Propietario/Persona F\\xedsica\",\n                                                    sx: {\n                                                        minWidth: 220\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83C\\uDFE2 Empresa/Raz\\xf3n Social\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDCDE Contacto/Encargado\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 947,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tabValue === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos del Propietario/Persona F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre Completo *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan Carlos P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioNombre\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioNombre),\n                                                                helperText: error.propietarioNombre,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioNombre,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioNombre && (error.propietarioNombre ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 986,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 988,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 983,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 970,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 966,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"DNI/CUIT *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 20-12345678-9\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioDocumento\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioDocumento),\n                                                                helperText: error.propietarioDocumento,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioDocumento,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioDocumento && (error.propietarioDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1017,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1019,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1014,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1001,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1029,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioTelefono\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioTelefono),\n                                                                helperText: error.propietarioTelefono,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioTelefono && (error.propietarioTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1047,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1049,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1044,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1059,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioEmail\",\n                                                                type: \"email\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioEmail),\n                                                                helperText: error.propietarioEmail,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioEmail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioEmail && (error.propietarioEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1077,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1079,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1074,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                mt: 2,\n                                                                p: 2,\n                                                                bgcolor: \"#f5f5f5\",\n                                                                borderRadius: 1\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    cursor: \"pointer\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: formData.usarComoRazonSocial,\n                                                                        onChange: (e)=>{\n                                                                            const checked = e.target.checked;\n                                                                            setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    usarComoRazonSocial: checked,\n                                                                                    empresaRazonSocial: checked ? prev.propietarioNombre : prev.empresaRazonSocial\n                                                                                }));\n                                                                        },\n                                                                        style: {\n                                                                            marginRight: \"8px\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1104,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        sx: {\n                                                                            fontFamily: \"Inter, sans-serif\"\n                                                                        },\n                                                                        children: \"✅ Usar estos datos tambi\\xe9n como raz\\xf3n social de la empresa\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1119,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1097,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1089,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos de la Empresa/Raz\\xf3n Social\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1136,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Agropecuaria San Juan S.A.\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaRazonSocial\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaRazonSocial),\n                                                                helperText: error.empresaRazonSocial,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaRazonSocial,\n                                                                disabled: formData.usarComoRazonSocial,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaRazonSocial && (error.empresaRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1169,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1171,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1166,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        backgroundColor: formData.usarComoRazonSocial ? \"#f5f5f5\" : \"transparent\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1152,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                sx: {\n                                                                    color: \"#666\",\n                                                                    mt: 1,\n                                                                    display: \"block\"\n                                                                },\n                                                                children: \"\\uD83D\\uDCA1 Autocompletado desde datos del propietario\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1185,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1196,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaTipoCliente\",\n                                                                        value: formData.empresaTipoCliente,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaTipoCliente\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione tipo de cliente\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1219,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            const option = tipoClienteOptions.find((opt)=>opt.value === selected);\n                                                                            return option ? \"\".concat(option.icon, \" \").concat(option.value) : selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1241,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: option.value,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                        sx: {\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\",\n                                                                                            gap: 1\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.icon\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1253,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.value\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1254,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1246,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, option.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1245,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1203,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1260,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1199,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1195,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio de la Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1269,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Av. San Mart\\xedn 1234\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaDomicilio\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaDomicilio),\n                                                                helperText: error.empresaDomicilio,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaDomicilio && (error.empresaDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1287,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1289,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1284,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1272,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1268,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1299,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaLocalidad\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaLocalidad),\n                                                                helperText: error.empresaLocalidad,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaLocalidad && (error.empresaLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1318,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1320,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1315,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1302,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1298,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1329,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaProvincia\",\n                                                                        value: formData.empresaProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione provincia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1352,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione provincia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1369,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: provincia,\n                                                                                    children: provincia\n                                                                                }, provincia, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1373,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1336,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1379,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1332,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1328,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Condici\\xf3n frente al IVA *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1388,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaCondFrenteIva\",\n                                                                        value: formData.empresaCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione condici\\xf3n IVA\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1411,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1428,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            condFrenteIvaOptions.map((condicion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: condicion,\n                                                                                    children: condicion\n                                                                                }, condicion, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1432,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1395,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1438,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1391,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1387,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Contactos/Encargados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1451,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            formData.contactos.map((contacto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    sx: {\n                                                        mb: 4,\n                                                        p: 3,\n                                                        border: \"1px solid #e0e0e0\",\n                                                        borderRadius: 2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                justifyContent: \"space-between\",\n                                                                alignItems: \"center\",\n                                                                mb: 2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"subtitle1\",\n                                                                    sx: {\n                                                                        fontWeight: 600,\n                                                                        color: \"#333\"\n                                                                    },\n                                                                    children: [\n                                                                        \"Contacto #\",\n                                                                        index + 1\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1481,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.contactos.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"outlined\",\n                                                                    color: \"error\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>{\n                                                                        setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                contactos: prev.contactos.filter((c)=>c.id !== contacto.id)\n                                                                            }));\n                                                                    },\n                                                                    children: \"Eliminar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1488,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1473,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            container: true,\n                                                            spacing: 3,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre del Encargado *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1509,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Mar\\xeda Gonz\\xe1lez\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_nombre\"),\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_nombre\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_nombre\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].nombre = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.nombre,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.nombre && (error[\"contacto_\".concat(contacto.id, \"_nombre\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1541,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1543,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1538,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1512,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1508,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Cargo (Opcional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1553,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Administrador, Encargado\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_cargo\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].cargo = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.cargo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1556,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1552,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tel\\xe9fono de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1580,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 0000-000000\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_telefono\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_telefono\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_telefono\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].telefono = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.telefono,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.telefono && (error[\"contacto_\".concat(contacto.id, \"_telefono\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1615,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1617,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1610,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1583,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1579,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Email de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1627,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: <EMAIL>\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_email\"),\n                                                                            type: \"email\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_email\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_email\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].email = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.email,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.email && (error[\"contacto_\".concat(contacto.id, \"_email\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1658,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1660,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1655,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1630,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1626,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1506,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, contacto.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1464,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1674,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: ()=>{\n                                                    const newId = Math.max(...formData.contactos.map((c)=>c.id)) + 1;\n                                                    setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactos: [\n                                                                ...prev.contactos,\n                                                                {\n                                                                    id: newId,\n                                                                    nombre: \"\",\n                                                                    cargo: \"\",\n                                                                    telefono: \"\",\n                                                                    email: \"\"\n                                                                }\n                                                            ]\n                                                        }));\n                                                },\n                                                sx: {\n                                                    mt: 2\n                                                },\n                                                children: \"Agregar otro contacto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1672,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1450,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            mt: 4,\n                                            pt: 3,\n                                            borderTop: \"1px solid #e0e0e0\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                sx: {\n                                                    minWidth: 120\n                                                },\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1709,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"contained\",\n                                                type: \"submit\",\n                                                disabled: isSubmitting,\n                                                sx: {\n                                                    minWidth: 120,\n                                                    bgcolor: \"#2E7D32\",\n                                                    \"&:hover\": {\n                                                        bgcolor: \"#1B5E20\"\n                                                    }\n                                                },\n                                                children: isSubmitting ? \"Guardando...\" : estadoModal === \"add\" ? \"Registrar\" : \"Actualizar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1718,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1700,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 922,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 890,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 877,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"BbVUwd51WSh2d/v81WqeKFo1Vys=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});