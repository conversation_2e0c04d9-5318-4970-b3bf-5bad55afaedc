"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData({\n                personRazonSocial: selectedClient.razonSocial,\n                personTipoCliente: selectedClient.tipoCliente || \"\",\n                personNombreContacto: selectedClient.nombreContacto || \"\",\n                personCargoContacto: selectedClient.cargoContacto || \"\",\n                personDomicilio: selectedClient.direccion,\n                personTelefono: selectedClient.telefono,\n                personMail: selectedClient.mail,\n                personLocalidad: localidad,\n                personProvincia: provincia,\n                personCondFrenteIva: selectedClient.condFrenteIva,\n                personDocumento: selectedClient.documento\n            });\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        //personId: \"\",\n        personRazonSocial: \"\",\n        personTipoCliente: \"\",\n        personNombreContacto: \"\",\n        personCargoContacto: \"\",\n        personDomicilio: \"\",\n        personTelefono: \"\",\n        personMail: \"\",\n        personLocalidad: \"\",\n        personProvincia: \"\",\n        personCondFrenteIva: \"\",\n        personDocumento: \"\"\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            personRazonSocial: \"\",\n            personTipoCliente: \"\",\n            personNombreContacto: \"\",\n            personCargoContacto: \"\",\n            personDomicilio: \"\",\n            personTelefono: \"\",\n            personMail: \"\",\n            personLocalidad: \"\",\n            personProvincia: \"\",\n            personCondFrenteIva: \"\",\n            personDocumento: \"\"\n        });\n        setError({});\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        if (name === \"personRazonSocial\" || name === \"personLocalidad\" || name === \"personNombreContacto\" || name === \"personCargoContacto\") {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"personDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        personDomicilio: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        personDomicilio: \"\"\n                    }));\n            }\n        }\n        if (name === \"personTelefono\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    personTelefono: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        personMail: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        personMail: \"\"\n                    }));\n            }\n        }\n        if (name === \"personDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    personDocumento: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>({\n                ...prevState,\n                [name]: value\n            }));\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.personLocalidad, \" - \").concat(formData.personProvincia);\n        const newPerson = {\n            razonSocial: formData.personRazonSocial,\n            tipoCliente: formData.personTipoCliente,\n            nombreContacto: formData.personNombreContacto,\n            cargoContacto: formData.personCargoContacto,\n            direccion: formData.personDomicilio,\n            telefono: formData.personTelefono,\n            mail: formData.personMail,\n            lugar: lugar,\n            provincia: formData.personProvincia,\n            condFrenteIva: formData.personCondFrenteIva,\n            documento: formData.personDocumento\n        };\n        // Llamar a la función de validación\n        const errors = formData;\n        console.log(errors);\n        if (errors) {\n            setError(errors);\n            return;\n        }\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData({\n                    personRazonSocial: agricultor.razonSocial,\n                    personTipoCliente: agricultor.tipoCliente || \"\",\n                    personNombreContacto: agricultor.nombreContacto || \"\",\n                    personCargoContacto: agricultor.cargoContacto || \"\",\n                    personDomicilio: agricultor.direccion,\n                    personTelefono: agricultor.telefono,\n                    personMail: agricultor.mail,\n                    personLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                    personProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                    personCondFrenteIva: agricultor.condFrenteIva,\n                    personDocumento: agricultor.documento\n                });\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.personLocalidad, \" - \").concat(formData.personProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            razonSocial: formData.personRazonSocial,\n            tipoCliente: formData.personTipoCliente,\n            nombreContacto: formData.personNombreContacto,\n            cargoContacto: formData.personCargoContacto,\n            direccion: formData.personDomicilio,\n            telefono: formData.personTelefono,\n            mail: formData.personMail,\n            lugar: lugar,\n            provincia: formData.personProvincia,\n            condFrenteIva: formData.personCondFrenteIva,\n            documento: formData.personDocumento\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 766,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 776,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 765,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 757,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 805,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 822,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 804,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    container: true,\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: {\n                                                xs: 12\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            variant: \"h6\",\n                                                            sx: {\n                                                                fontFamily: \"Lexend, sans-serif\",\n                                                                fontWeight: \"600\",\n                                                                color: \"#333\",\n                                                                mb: 3,\n                                                                mt: 1,\n                                                                fontSize: \"1.2rem\"\n                                                            },\n                                                            children: \"Informaci\\xf3n de la Empresa/Entidad\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Estancia La Esperanza S.A.\",\n                                                                variant: \"outlined\",\n                                                                id: \"razonSocial\",\n                                                                name: \"personRazonSocial\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personRazonSocial),\n                                                                helperText: error.personRazonSocial,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personRazonSocial,\n                                                                disabled: estadoModal === \"update\",\n                                                                fullWidth: true,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personRazonSocial && (error.personRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 938,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 940,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 935,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    style: {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    },\n                                                                    \"& .MuiFormHelperText-root\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 913,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.personTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        id: \"tipoCliente\",\n                                                                        name: \"personTipoCliente\",\n                                                                        value: formData.personTipoCliente,\n                                                                        onChange: (e)=>handleInputChange(e),\n                                                                        displayEmpty: true,\n                                                                        MenuProps: {\n                                                                            PaperProps: {\n                                                                                sx: {\n                                                                                    maxHeight: 400,\n                                                                                    width: \"auto\",\n                                                                                    minWidth: \"300px\",\n                                                                                    \"& .MuiMenuItem-root\": {\n                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                        fontSize: \"0.875rem\",\n                                                                                        fontWeight: 600,\n                                                                                        lineHeight: 1.5,\n                                                                                        padding: \"12px 16px\",\n                                                                                        whiteSpace: \"normal\",\n                                                                                        wordWrap: \"break-word\",\n                                                                                        minHeight: \"auto\",\n                                                                                        color: \"#333\",\n                                                                                        borderBottom: \"1px solid #f0f0f0\",\n                                                                                        \"&:last-child\": {\n                                                                                            borderBottom: \"none\"\n                                                                                        }\n                                                                                    }\n                                                                                }\n                                                                            }\n                                                                        },\n                                                                        sx: {\n                                                                            fontFamily: \"Lexend, sans-serif\",\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                fontFamily: \"Lexend, sans-serif\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                fontWeight: 600,\n                                                                                lineHeight: 1.4,\n                                                                                padding: \"16.5px 14px\",\n                                                                                color: \"#333\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            },\n                                                                            \"& .MuiOutlinedInput-notchedOutline\": {\n                                                                                borderColor: \"rgba(0, 0, 0, 0.23)\"\n                                                                            },\n                                                                            \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                                                                                borderColor: \"rgba(0, 0, 0, 0.87)\"\n                                                                            },\n                                                                            \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                                                                                borderColor: \"#1976d2\",\n                                                                                borderWidth: \"2px\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                sx: {\n                                                                                    fontFamily: \"Lexend, sans-serif\",\n                                                                                    fontStyle: \"italic\",\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"0.875rem\",\n                                                                                    fontWeight: 600,\n                                                                                    backgroundColor: \"#fafafa\"\n                                                                                },\n                                                                                children: \"Seleccione un tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1024,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((tipo, index)=>{\n                                                                                const isFirstInCategory = index === 0 || tipoClienteOptions[index - 1].category !== tipo.category;\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    value: tipo.value,\n                                                                                    sx: {\n                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                        fontSize: \"0.875rem\",\n                                                                                        fontWeight: 600,\n                                                                                        lineHeight: 1.5,\n                                                                                        padding: \"12px 16px\",\n                                                                                        whiteSpace: \"normal\",\n                                                                                        wordWrap: \"break-word\",\n                                                                                        minHeight: \"auto\",\n                                                                                        display: \"flex\",\n                                                                                        alignItems: \"center\",\n                                                                                        gap: \"8px\",\n                                                                                        color: \"#333\",\n                                                                                        borderTop: isFirstInCategory && index > 0 ? \"1px solid #e0e0e0\" : \"none\",\n                                                                                        marginTop: isFirstInCategory && index > 0 ? \"4px\" : \"0\",\n                                                                                        paddingTop: isFirstInCategory && index > 0 ? \"16px\" : \"12px\",\n                                                                                        \"&:hover\": {\n                                                                                            backgroundColor: \"#f8f9fa\",\n                                                                                            borderLeft: \"3px solid #2196f3\"\n                                                                                        },\n                                                                                        \"&.Mui-selected\": {\n                                                                                            backgroundColor: \"#e3f2fd\",\n                                                                                            borderLeft: \"3px solid #1976d2\",\n                                                                                            fontWeight: 600,\n                                                                                            \"&:hover\": {\n                                                                                                backgroundColor: \"#bbdefb\"\n                                                                                            }\n                                                                                        }\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            style: {\n                                                                                                fontSize: \"1rem\",\n                                                                                                marginRight: \"8px\"\n                                                                                            },\n                                                                                            children: tipo.icon\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                            lineNumber: 1087,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                            sx: {\n                                                                                                display: \"flex\",\n                                                                                                flexDirection: \"column\",\n                                                                                                flex: 1\n                                                                                            },\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    style: {\n                                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                                        fontWeight: 600,\n                                                                                                        color: \"#333\"\n                                                                                                    },\n                                                                                                    children: tipo.value\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                    lineNumber: 1102,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    style: {\n                                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                                        fontSize: \"0.75rem\",\n                                                                                                        color: \"#666\",\n                                                                                                        fontStyle: \"italic\",\n                                                                                                        fontWeight: 500\n                                                                                                    },\n                                                                                                    children: tipo.category\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                    lineNumber: 1111,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                            lineNumber: 1095,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, \"\".concat(tipo.value, \"-\").concat(index), true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1045,\n                                                                                    columnNumber: 31\n                                                                                }, undefined);\n                                                                            })\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 964,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.personTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        sx: {\n                                                                            fontFamily: \"Inter, sans-serif\"\n                                                                        },\n                                                                        children: error.personTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 960,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            variant: \"h6\",\n                                                            sx: {\n                                                                fontFamily: \"Lexend, sans-serif\",\n                                                                fontWeight: \"600\",\n                                                                color: \"#333\",\n                                                                mb: 3,\n                                                                mt: 3,\n                                                                fontSize: \"1.2rem\"\n                                                            },\n                                                            children: \"Informaci\\xf3n del Contacto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre del Contacto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1156,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                id: \"nombreContacto\",\n                                                                name: \"personNombreContacto\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personNombreContacto),\n                                                                helperText: error.personNombreContacto,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personNombreContacto,\n                                                                fullWidth: true,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personNombreContacto && (error.personNombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1183,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1185,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1180,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    style: {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    },\n                                                                    \"& .MuiFormHelperText-root\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1159,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1155,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Cargo (Opcional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1202,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Encargado, Propietario, Administrador\",\n                                                                variant: \"outlined\",\n                                                                id: \"cargoContacto\",\n                                                                name: \"personCargoContacto\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personCargoContacto),\n                                                                helperText: error.personCargoContacto,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personCargoContacto,\n                                                                fullWidth: true,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personCargoContacto && (error.personCargoContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1229,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1231,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1226,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    style: {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    },\n                                                                    \"& .MuiFormHelperText-root\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1205,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1201,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1250,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Cordoba 123\",\n                                                                variant: \"outlined\",\n                                                                id: \"domicilio\",\n                                                                name: \"personDomicilio\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personDomicilio),\n                                                                helperText: error.personDomicilio,\n                                                                fullWidth: true,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personDomicilio && (error.personDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1277,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1279,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1274,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    style: {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    },\n                                                                    \"& .MuiFormHelperText-root\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1253,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1299,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                id: \"telefono\",\n                                                                name: \"personTelefono\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personTelefono),\n                                                                helperText: error.personTelefono,\n                                                                fullWidth: true,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personTelefono && (error.personTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1326,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1328,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1302,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1297,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1337,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                id: \"email\",\n                                                                name: \"personMail\",\n                                                                type: \"email\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.personMail),\n                                                                helperText: error.personMail,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personMail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personMail && (error.personMail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1365,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1367,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1362,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1340,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1335,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1378,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                id: \"localidad\",\n                                                                name: \"personLocalidad\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personLocalidad),\n                                                                helperText: error.personLocalidad,\n                                                                fullWidth: true,\n                                                                required: true,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personLocalidad && (error.personLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1406,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1408,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1403,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1381,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1376,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1417,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.personProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        name: \"personProvincia\",\n                                                                        labelId: \"demo-simple-select-label\",\n                                                                        fullWidth: true,\n                                                                        value: formData.personProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"personProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleProvinciaChange(syntheticEvent);\n                                                                        },\n                                                                        required: true,\n                                                                        inputRef: selectProvinciaRef,\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return \"Seleccione una provincia\";\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        endAdornment: formData.personProvincia && !error.personProvincia ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            position: \"end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                color: \"success\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1451,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1450,\n                                                                            columnNumber: 31\n                                                                        }, void 0) : null,\n                                                                        MenuProps: {\n                                                                            PaperProps: {\n                                                                                style: {\n                                                                                    maxHeight: 200\n                                                                                }\n                                                                            }\n                                                                        },\n                                                                        sx: {\n                                                                            minWidth: \"200px\"\n                                                                        },\n                                                                        children: provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                value: provincia,\n                                                                                children: provincia\n                                                                            }, provincia, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1465,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1424,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.personProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        children: error.personProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1471,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1420,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1415,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Cond. Frente al IVA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1481,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.personCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        id: \"condIva\",\n                                                                        name: \"personCondFrenteIva\",\n                                                                        value: formData.personCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"personCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleCondIvaChange(syntheticEvent);\n                                                                        },\n                                                                        inputRef: selectCondIvaRef,\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return \"Seleccione una opci\\xf3n\";\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        endAdornment: formData.personCondFrenteIva && !error.personCondFrenteIva ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            position: \"end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                color: \"success\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1513,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1512,\n                                                                            columnNumber: 31\n                                                                        }, void 0) : null,\n                                                                        children: condFrenteIvaOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                value: option,\n                                                                                children: option\n                                                                            }, option, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1519,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1488,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.personCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        children: error.personCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1525,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1484,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1479,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Documento\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1534,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                id: \"documento\",\n                                                                placeholder: \"Ej: 00-00000000-0\",\n                                                                name: \"personDocumento\",\n                                                                value: formData.personDocumento,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                error: Boolean(error.personDocumento),\n                                                                helperText: error.personDocumento,\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                inputRef: documentoRef,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personDocumento && (error.personDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1561,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1563,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1558,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1537,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: {\n                                                xs: 12,\n                                                sm: 6\n                                            },\n                                            style: {\n                                                margin: \"8px 0\",\n                                                padding: \"8px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                container: true,\n                                                spacing: 1,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: {\n                                                        xs: 12,\n                                                        sm: 6\n                                                    },\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"flex-end\",\n                                                        gap: \"8px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            type: \"button\",\n                                                            variant: \"outlined\",\n                                                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                            children: \"Cancelar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1585,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            type: \"submit\",\n                                                            variant: \"contained\",\n                                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1597,\n                                                                columnNumber: 36\n                                                            }, void 0),\n                                                            sx: {\n                                                                bgcolor: \"#2E7D32\",\n                                                                color: \"#ffffff\",\n                                                                \"&:hover\": {\n                                                                    bgcolor: \"#1B5E20\"\n                                                                },\n                                                                textTransform: \"none\",\n                                                                \"& .MuiSvgIcon-root\": {\n                                                                    color: \"#ffffff\"\n                                                                }\n                                                            },\n                                                            children: estadoModal === \"add\" ? \"Registrar\" : \"Guardar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1594,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1577,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1576,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1572,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 879,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 847,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 834,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"7xHfrc7yY0vfO8Ist2RMzzr3NYU=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});