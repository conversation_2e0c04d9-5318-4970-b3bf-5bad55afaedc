"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* harmony import */ var _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/Person */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [enabledTabs, setEnabledTabs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        true,\n        false,\n        false\n    ]); // Solo la primera pestaña habilitada inicialmente\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Funciones de validación para cada pestaña\n    const validateTab1 = ()=>{\n        const { propietarioNombre, propietarioDocumento, propietarioTelefono, propietarioEmail } = formData;\n        return !!(propietarioNombre.trim() && propietarioDocumento.trim() && propietarioTelefono.trim() && propietarioEmail.trim());\n    };\n    const validateTab2 = ()=>{\n        const { empresaRazonSocial, empresaTipoCliente, empresaDomicilio, empresaLocalidad, empresaProvincia, empresaCondFrenteIva } = formData;\n        return !!(empresaRazonSocial.trim() && empresaTipoCliente.trim() && empresaDomicilio.trim() && empresaLocalidad.trim() && empresaProvincia.trim() && empresaCondFrenteIva.trim());\n    };\n    const validateTab3 = ()=>{\n        return formData.contactos.length > 0 && formData.contactos[0].nombre.trim() !== \"\" && formData.contactos[0].cargo.trim() !== \"\" && formData.contactos[0].telefono.trim() !== \"\" && formData.contactos[0].email.trim() !== \"\";\n    };\n    // Función para habilitar la siguiente pestaña\n    const enableNextTab = (currentTab)=>{\n        const newEnabledTabs = [\n            ...enabledTabs\n        ];\n        if (currentTab + 1 < newEnabledTabs.length) {\n            newEnabledTabs[currentTab + 1] = true;\n            setEnabledTabs(newEnabledTabs);\n        }\n    };\n    // Función para ir a la siguiente pestaña\n    const handleNextTab = ()=>{\n        let canProceed = false;\n        switch(tabValue){\n            case 0:\n                canProceed = validateTab1();\n                break;\n            case 1:\n                canProceed = validateTab2();\n                break;\n            case 2:\n                canProceed = validateTab3();\n                break;\n        }\n        if (canProceed) {\n            enableNextTab(tabValue);\n            setTabValue(tabValue + 1);\n        } else {\n            alert(\"Por favor complete todos los campos requeridos antes de continuar.\");\n        }\n    };\n    // Función para ir a la pestaña anterior\n    const handlePreviousTab = ()=>{\n        if (tabValue > 0) {\n            setTabValue(tabValue - 1);\n        }\n    };\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData((prevData)=>({\n                    ...prevData,\n                    empresaRazonSocial: selectedClient.razonSocial,\n                    empresaTipoCliente: selectedClient.tipoCliente || \"\",\n                    empresaDomicilio: selectedClient.direccion,\n                    empresaLocalidad: localidad,\n                    empresaProvincia: provincia,\n                    empresaCondFrenteIva: selectedClient.condFrenteIva,\n                    // Update contacts array with the selected client's contact information\n                    contactos: [\n                        {\n                            id: 1,\n                            nombre: selectedClient.nombreContacto || \"\",\n                            cargo: selectedClient.cargoContacto || \"\",\n                            telefono: selectedClient.telefono,\n                            email: selectedClient.mail\n                        }\n                    ]\n                }));\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Pestaña 1: Propietario/Persona Física\n        propietarioNombre: \"\",\n        propietarioDocumento: \"\",\n        propietarioTelefono: \"\",\n        propietarioEmail: \"\",\n        usarComoRazonSocial: false,\n        // Pestaña 2: Empresa/Razón Social\n        empresaRazonSocial: \"\",\n        empresaTipoCliente: \"\",\n        empresaDomicilio: \"\",\n        empresaLocalidad: \"\",\n        empresaProvincia: \"\",\n        empresaCondFrenteIva: \"\",\n        // Pestaña 3: Contactos/Encargados (array dinámico)\n        contactos: [\n            {\n                id: 1,\n                nombre: \"\",\n                cargo: \"\",\n                telefono: \"\",\n                email: \"\"\n            }\n        ]\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            // Pestaña 1: Propietario/Persona Física\n            propietarioNombre: \"\",\n            propietarioDocumento: \"\",\n            propietarioTelefono: \"\",\n            propietarioEmail: \"\",\n            usarComoRazonSocial: false,\n            // Pestaña 2: Empresa/Razón Social\n            empresaRazonSocial: \"\",\n            empresaTipoCliente: \"\",\n            empresaDomicilio: \"\",\n            empresaLocalidad: \"\",\n            empresaProvincia: \"\",\n            empresaCondFrenteIva: \"\",\n            // Pestaña 3: Contactos/Encargados\n            contactos: [\n                {\n                    id: 1,\n                    nombre: \"\",\n                    cargo: \"\",\n                    telefono: \"\",\n                    email: \"\"\n                }\n            ]\n        });\n        setError({});\n        setTabValue(0); // Resetear al primer tab\n        setEnabledTabs([\n            true,\n            false,\n            false\n        ]); // Resetear pestañas habilitadas\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Validaciones para campos de texto (nombres, razón social, etc.)\n        if (name === \"propietarioNombre\" || name === \"empresaRazonSocial\" || name === \"empresaLocalidad\" || name.startsWith(\"contacto\") && name.includes(\"nombre\")) {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para domicilio\n        if (name === \"empresaDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para teléfonos\n        if (name === \"propietarioTelefono\" || name.includes(\"telefono\")) {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"propietarioDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>{\n            const newState = {\n                ...prevState,\n                [name]: value\n            };\n            // Si está marcada la opción de usar como razón social, sincronizar datos\n            if (prevState.usarComoRazonSocial) {\n                if (name === \"propietarioNombre\") {\n                    newState.empresaRazonSocial = value;\n                    // También actualizar el primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            nombre: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioTelefono\") {\n                    // Actualizar el teléfono del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            telefono: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioEmail\") {\n                    // Actualizar el email del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            email: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                }\n            }\n            return newState;\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 566,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            // Datos del Propietario/Persona Física (Pestaña 1)\n            propietarioNombre: formData.propietarioNombre,\n            propietarioDocumento: formData.propietarioDocumento,\n            propietarioTelefono: formData.propietarioTelefono,\n            propietarioEmail: formData.propietarioEmail,\n            // Datos de la Empresa/Razón Social (Pestaña 2)\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            direccion: formData.empresaDomicilio,\n            empresaLocalidad: formData.empresaLocalidad,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            // Datos del Contacto/Encargado Principal (Pestaña 3)\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            // Campos calculados/derivados\n            lugar: lugar,\n            documento: formData.propietarioDocumento\n        };\n        // TODO: Add proper form validation if needed\n        // For now, individual field validations are handled in the onChange handlers\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData((prevData)=>{\n                    var _agricultor_lugar_split_, _agricultor_lugar_split_1;\n                    return {\n                        ...prevData,\n                        // Datos del Propietario/Persona Física (Pestaña 1)\n                        propietarioNombre: agricultor.propietarioNombre || \"\",\n                        propietarioDocumento: agricultor.propietarioDocumento || \"\",\n                        propietarioTelefono: agricultor.propietarioTelefono || \"\",\n                        propietarioEmail: agricultor.propietarioEmail || \"\",\n                        // Datos de la Empresa/Razón Social (Pestaña 2)\n                        empresaRazonSocial: agricultor.razonSocial,\n                        empresaTipoCliente: agricultor.tipoCliente || \"\",\n                        empresaDomicilio: agricultor.direccion,\n                        empresaLocalidad: agricultor.empresaLocalidad || ((_agricultor_lugar_split_ = agricultor.lugar.split(\" - \")[0]) === null || _agricultor_lugar_split_ === void 0 ? void 0 : _agricultor_lugar_split_.trim()) || \"\",\n                        empresaProvincia: agricultor.provincia || ((_agricultor_lugar_split_1 = agricultor.lugar.split(\" - \")[1]) === null || _agricultor_lugar_split_1 === void 0 ? void 0 : _agricultor_lugar_split_1.trim()) || \"\",\n                        empresaCondFrenteIva: agricultor.condFrenteIva,\n                        // Datos del Contacto/Encargado Principal (Pestaña 3)\n                        contactos: [\n                            {\n                                id: 1,\n                                nombre: agricultor.nombreContacto || \"\",\n                                cargo: agricultor.cargoContacto || \"\",\n                                telefono: agricultor.telefono,\n                                email: agricultor.mail\n                            }\n                        ]\n                    };\n                });\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            // Datos del Propietario/Persona Física (Pestaña 1)\n            propietarioNombre: formData.propietarioNombre,\n            propietarioDocumento: formData.propietarioDocumento,\n            propietarioTelefono: formData.propietarioTelefono,\n            propietarioEmail: formData.propietarioEmail,\n            // Datos de la Empresa/Razón Social (Pestaña 2)\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            direccion: formData.empresaDomicilio,\n            empresaLocalidad: formData.empresaLocalidad,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            // Datos del Contacto/Encargado Principal (Pestaña 3)\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            // Campos calculados/derivados\n            lugar: lugar,\n            documento: formData.propietarioDocumento\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 987,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 1009,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 979,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1037,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1035,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 1027,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 1044,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 1026,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1072,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1083,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1071,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1099,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1094,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            borderBottom: 1,\n                                            borderColor: \"divider\",\n                                            mb: 3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            value: tabValue,\n                                            onChange: (event, newValue)=>{\n                                                // Solo permitir cambiar a pestañas habilitadas\n                                                if (enabledTabs[newValue]) {\n                                                    setTabValue(newValue);\n                                                }\n                                            },\n                                            sx: {\n                                                \"& .MuiTab-root\": {\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    fontWeight: 600,\n                                                    textTransform: \"none\",\n                                                    fontSize: \"1rem\"\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDC64 Propietario/Persona F\\xedsica\",\n                                                    sx: {\n                                                        minWidth: 220\n                                                    },\n                                                    disabled: !enabledTabs[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1126,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83C\\uDFE2 Empresa/Raz\\xf3n Social\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDCDE Contacto/Encargado\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1136,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1109,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1108,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tabValue === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos del Propietario/Persona F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1147,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre Completo *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1160,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan Carlos P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioNombre\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioNombre),\n                                                                helperText: error.propietarioNombre,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioNombre,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioNombre && (error.propietarioNombre ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1179,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1181,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1176,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1159,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"DNI/CUIT *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1191,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 20-12345678-9\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioDocumento\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioDocumento),\n                                                                helperText: error.propietarioDocumento,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioDocumento,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioDocumento && (error.propietarioDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1210,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1212,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1207,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1194,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1222,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioTelefono\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioTelefono),\n                                                                helperText: error.propietarioTelefono,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioTelefono && (error.propietarioTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1240,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1242,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1237,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1225,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1252,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioEmail\",\n                                                                type: \"email\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioEmail),\n                                                                helperText: error.propietarioEmail,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioEmail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioEmail && (error.propietarioEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1270,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1272,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1267,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1255,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1251,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                mt: 2,\n                                                                p: 2,\n                                                                bgcolor: \"#f5f5f5\",\n                                                                borderRadius: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        cursor: \"pointer\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.usarComoRazonSocial,\n                                                                            onChange: (e)=>{\n                                                                                const checked = e.target.checked;\n                                                                                setFormData((prev)=>{\n                                                                                    var _prev_contactos_;\n                                                                                    return {\n                                                                                        ...prev,\n                                                                                        usarComoRazonSocial: checked,\n                                                                                        // Si se marca, copiar datos del propietario a la empresa\n                                                                                        empresaRazonSocial: checked ? prev.propietarioNombre : \"\",\n                                                                                        // También copiar el primer contacto con los datos del propietario\n                                                                                        contactos: checked ? [\n                                                                                            {\n                                                                                                ...prev.contactos[0],\n                                                                                                nombre: prev.propietarioNombre,\n                                                                                                telefono: prev.propietarioTelefono,\n                                                                                                email: prev.propietarioEmail,\n                                                                                                cargo: ((_prev_contactos_ = prev.contactos[0]) === null || _prev_contactos_ === void 0 ? void 0 : _prev_contactos_.cargo) || \"Propietario\"\n                                                                                            },\n                                                                                            ...prev.contactos.slice(1)\n                                                                                        ] : prev.contactos\n                                                                                    };\n                                                                                });\n                                                                            },\n                                                                            style: {\n                                                                                marginRight: \"8px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1297,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                fontFamily: \"Inter, sans-serif\"\n                                                                            },\n                                                                            children: \"✅ Usar estos datos del propietario para la empresa y contacto principal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1328,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1290,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        color: \"#2E7D32\",\n                                                                        mt: 1,\n                                                                        display: \"block\",\n                                                                        fontStyle: \"italic\"\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCA1 Los datos del propietario se sincronizar\\xe1n autom\\xe1ticamente con la raz\\xf3n social de la empresa y el contacto principal\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1337,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1282,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1157,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Cancelar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1374,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1356,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos de la Empresa/Raz\\xf3n Social\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Agropecuaria San Juan S.A.\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaRazonSocial\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaRazonSocial),\n                                                                helperText: error.empresaRazonSocial,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaRazonSocial,\n                                                                disabled: formData.usarComoRazonSocial,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaRazonSocial && (error.empresaRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1425,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1427,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1422,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        backgroundColor: formData.usarComoRazonSocial ? \"#f5f5f5\" : \"transparent\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1408,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                sx: {\n                                                                    color: \"#666\",\n                                                                    mt: 1,\n                                                                    display: \"block\"\n                                                                },\n                                                                children: \"\\uD83D\\uDCA1 Autocompletado desde datos del propietario\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1441,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1404,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1452,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaTipoCliente\",\n                                                                        value: formData.empresaTipoCliente,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaTipoCliente\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione tipo de cliente\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1475,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            const option = tipoClienteOptions.find((opt)=>opt.value === selected);\n                                                                            return option ? \"\".concat(option.icon, \" \").concat(option.value) : selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1497,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: option.value,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                        sx: {\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\",\n                                                                                            gap: 1\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.icon\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1509,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.value\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1510,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1502,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, option.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1501,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1459,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1516,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1455,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1451,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio de la Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1525,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Av. San Mart\\xedn 1234\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaDomicilio\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaDomicilio),\n                                                                helperText: error.empresaDomicilio,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaDomicilio && (error.empresaDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1543,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1545,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1540,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1528,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1524,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1555,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaLocalidad\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaLocalidad),\n                                                                helperText: error.empresaLocalidad,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaLocalidad && (error.empresaLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1574,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1576,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1571,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1558,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1554,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1585,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaProvincia\",\n                                                                        value: formData.empresaProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione provincia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1608,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione provincia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1625,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: provincia,\n                                                                                    children: provincia\n                                                                                }, provincia, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1629,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1592,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1635,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1588,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1584,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Condici\\xf3n frente al IVA *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1644,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaCondFrenteIva\",\n                                                                        value: formData.empresaCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione condici\\xf3n IVA\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1667,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1684,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            condFrenteIvaOptions.map((condicion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: condicion,\n                                                                                    children: condicion\n                                                                                }, condicion, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1688,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1651,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1694,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1647,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1643,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1712,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1719,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1703,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1391,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Contactos/Encargados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1737,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            formData.contactos.map((contacto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    sx: {\n                                                        mb: 4,\n                                                        p: 3,\n                                                        border: \"1px solid #e0e0e0\",\n                                                        borderRadius: 2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                justifyContent: \"space-between\",\n                                                                alignItems: \"center\",\n                                                                mb: 2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    sx: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        gap: 1\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#2E7D32\",\n                                                                                fontSize: \"1.2rem\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1770,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"subtitle1\",\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#333\"\n                                                                            },\n                                                                            children: [\n                                                                                \"Contacto #\",\n                                                                                index + 1\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1776,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1767,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.contactos.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"outlined\",\n                                                                    color: \"error\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>{\n                                                                        setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                contactos: prev.contactos.filter((c)=>c.id !== contacto.id)\n                                                                            }));\n                                                                    },\n                                                                    children: \"Eliminar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1784,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1759,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            container: true,\n                                                            spacing: 3,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre del Encargado *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1805,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Mar\\xeda Gonz\\xe1lez\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_nombre\"),\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_nombre\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_nombre\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].nombre = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.nombre,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.nombre && (error[\"contacto_\".concat(contacto.id, \"_nombre\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1837,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1839,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1834,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1808,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1804,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Cargo (Opcional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1849,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Administrador, Encargado\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_cargo\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].cargo = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.cargo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1852,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1848,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tel\\xe9fono de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1876,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 0000-000000\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_telefono\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_telefono\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_telefono\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].telefono = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.telefono,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.telefono && (error[\"contacto_\".concat(contacto.id, \"_telefono\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1911,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1913,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1906,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1879,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1875,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Email de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1923,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: <EMAIL>\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_email\"),\n                                                                            type: \"email\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_email\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_email\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].email = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.email,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.email && (error[\"contacto_\".concat(contacto.id, \"_email\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1954,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1956,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1951,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1926,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1922,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1802,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, contacto.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1750,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1970,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: ()=>{\n                                                    const newId = Math.max(...formData.contactos.map((c)=>c.id)) + 1;\n                                                    setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactos: [\n                                                                ...prev.contactos,\n                                                                {\n                                                                    id: newId,\n                                                                    nombre: \"\",\n                                                                    cargo: \"\",\n                                                                    telefono: \"\",\n                                                                    email: \"\"\n                                                                }\n                                                            ]\n                                                        }));\n                                                },\n                                                sx: {\n                                                    mt: 2\n                                                },\n                                                children: \"Agregar otro contacto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1968,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 2003,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        type: \"submit\",\n                                                        disabled: isSubmitting,\n                                                        onClick: handleAddCliente,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: isSubmitting ? \"Guardando...\" : estadoModal === \"add\" ? \"Registrar\" : \"Actualizar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 2010,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1994,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1736,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1106,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1101,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 1069,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 1056,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"BbVUwd51WSh2d/v81WqeKFo1Vys=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});