"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData({\n                personRazonSocial: selectedClient.razonSocial,\n                personTipoCliente: selectedClient.tipoCliente || \"\",\n                personNombreContacto: selectedClient.nombreContacto || \"\",\n                personCargoContacto: selectedClient.cargoContacto || \"\",\n                personDomicilio: selectedClient.direccion,\n                personTelefono: selectedClient.telefono,\n                personMail: selectedClient.mail,\n                personLocalidad: localidad,\n                personProvincia: provincia,\n                personCondFrenteIva: selectedClient.condFrenteIva,\n                personDocumento: selectedClient.documento\n            });\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        //personId: \"\",\n        personRazonSocial: \"\",\n        personTipoCliente: \"\",\n        personNombreContacto: \"\",\n        personCargoContacto: \"\",\n        personDomicilio: \"\",\n        personTelefono: \"\",\n        personMail: \"\",\n        personLocalidad: \"\",\n        personProvincia: \"\",\n        personCondFrenteIva: \"\",\n        personDocumento: \"\"\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            personRazonSocial: \"\",\n            personTipoCliente: \"\",\n            personNombreContacto: \"\",\n            personCargoContacto: \"\",\n            personDomicilio: \"\",\n            personTelefono: \"\",\n            personMail: \"\",\n            personLocalidad: \"\",\n            personProvincia: \"\",\n            personCondFrenteIva: \"\",\n            personDocumento: \"\"\n        });\n        setError({});\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        if (name === \"personRazonSocial\" || name === \"personLocalidad\" || name === \"personNombreContacto\" || name === \"personCargoContacto\") {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"personDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        personDomicilio: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        personDomicilio: \"\"\n                    }));\n            }\n        }\n        if (name === \"personTelefono\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    personTelefono: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        personMail: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        personMail: \"\"\n                    }));\n            }\n        }\n        if (name === \"personDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    personDocumento: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>({\n                ...prevState,\n                [name]: value\n            }));\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.personLocalidad, \" - \").concat(formData.personProvincia);\n        const newPerson = {\n            razonSocial: formData.personRazonSocial,\n            tipoCliente: formData.personTipoCliente,\n            nombreContacto: formData.personNombreContacto,\n            cargoContacto: formData.personCargoContacto,\n            direccion: formData.personDomicilio,\n            telefono: formData.personTelefono,\n            mail: formData.personMail,\n            lugar: lugar,\n            provincia: formData.personProvincia,\n            condFrenteIva: formData.personCondFrenteIva,\n            documento: formData.personDocumento\n        };\n        // Llamar a la función de validación\n        const errors = formData;\n        console.log(errors);\n        if (errors) {\n            setError(errors);\n            return;\n        }\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData({\n                    personRazonSocial: agricultor.razonSocial,\n                    personTipoCliente: agricultor.tipoCliente || \"\",\n                    personNombreContacto: agricultor.nombreContacto || \"\",\n                    personCargoContacto: agricultor.cargoContacto || \"\",\n                    personDomicilio: agricultor.direccion,\n                    personTelefono: agricultor.telefono,\n                    personMail: agricultor.mail,\n                    personLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                    personProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                    personCondFrenteIva: agricultor.condFrenteIva,\n                    personDocumento: agricultor.documento\n                });\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.personLocalidad, \" - \").concat(formData.personProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            razonSocial: formData.personRazonSocial,\n            tipoCliente: formData.personTipoCliente,\n            nombreContacto: formData.personNombreContacto,\n            cargoContacto: formData.personCargoContacto,\n            direccion: formData.personDomicilio,\n            telefono: formData.personTelefono,\n            mail: formData.personMail,\n            lugar: lugar,\n            provincia: formData.personProvincia,\n            condFrenteIva: formData.personCondFrenteIva,\n            documento: formData.personDocumento\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 762,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 784,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 754,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 802,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 819,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 801,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 847,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 869,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    container: true,\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: {\n                                                xs: 12\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            variant: \"h6\",\n                                                            sx: {\n                                                                fontFamily: \"Lexend, sans-serif\",\n                                                                fontWeight: \"600\",\n                                                                color: \"#333\",\n                                                                mb: 3,\n                                                                mt: 1,\n                                                                fontSize: \"1.2rem\"\n                                                            },\n                                                            children: \"Informaci\\xf3n de la Empresa/Entidad\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Estancia La Esperanza S.A.\",\n                                                                variant: \"outlined\",\n                                                                id: \"razonSocial\",\n                                                                name: \"personRazonSocial\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personRazonSocial),\n                                                                helperText: error.personRazonSocial,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personRazonSocial,\n                                                                disabled: estadoModal === \"update\",\n                                                                fullWidth: true,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personRazonSocial && (error.personRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 935,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 937,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 932,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    style: {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    },\n                                                                    \"& .MuiFormHelperText-root\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.personTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        id: \"tipoCliente\",\n                                                                        name: \"personTipoCliente\",\n                                                                        value: formData.personTipoCliente,\n                                                                        onChange: (e)=>handleInputChange(e),\n                                                                        displayEmpty: true,\n                                                                        MenuProps: {\n                                                                            PaperProps: {\n                                                                                sx: {\n                                                                                    maxHeight: 400,\n                                                                                    width: \"auto\",\n                                                                                    minWidth: \"300px\",\n                                                                                    \"& .MuiMenuItem-root\": {\n                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                        fontSize: \"0.875rem\",\n                                                                                        fontWeight: 600,\n                                                                                        lineHeight: 1.5,\n                                                                                        padding: \"12px 16px\",\n                                                                                        whiteSpace: \"normal\",\n                                                                                        wordWrap: \"break-word\",\n                                                                                        minHeight: \"auto\",\n                                                                                        color: \"#333\",\n                                                                                        borderBottom: \"1px solid #f0f0f0\",\n                                                                                        \"&:last-child\": {\n                                                                                            borderBottom: \"none\"\n                                                                                        }\n                                                                                    }\n                                                                                }\n                                                                            }\n                                                                        },\n                                                                        sx: {\n                                                                            fontFamily: \"Lexend, sans-serif\",\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                fontFamily: \"Lexend, sans-serif\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                fontWeight: 600,\n                                                                                lineHeight: 1.4,\n                                                                                padding: \"16.5px 14px\",\n                                                                                color: \"#333\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            },\n                                                                            \"& .MuiOutlinedInput-notchedOutline\": {\n                                                                                borderColor: \"rgba(0, 0, 0, 0.23)\"\n                                                                            },\n                                                                            \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                                                                                borderColor: \"rgba(0, 0, 0, 0.87)\"\n                                                                            },\n                                                                            \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                                                                                borderColor: \"#1976d2\",\n                                                                                borderWidth: \"2px\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                sx: {\n                                                                                    fontFamily: \"Lexend, sans-serif\",\n                                                                                    fontStyle: \"italic\",\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"0.875rem\",\n                                                                                    fontWeight: 600,\n                                                                                    backgroundColor: \"#fafafa\"\n                                                                                },\n                                                                                children: \"Seleccione un tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1021,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((tipo, index)=>{\n                                                                                const isFirstInCategory = index === 0 || tipoClienteOptions[index - 1].category !== tipo.category;\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    value: tipo.value,\n                                                                                    sx: {\n                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                        fontSize: \"0.875rem\",\n                                                                                        fontWeight: 600,\n                                                                                        lineHeight: 1.5,\n                                                                                        padding: \"12px 16px\",\n                                                                                        whiteSpace: \"normal\",\n                                                                                        wordWrap: \"break-word\",\n                                                                                        minHeight: \"auto\",\n                                                                                        display: \"flex\",\n                                                                                        alignItems: \"center\",\n                                                                                        gap: \"8px\",\n                                                                                        color: \"#333\",\n                                                                                        borderTop: isFirstInCategory && index > 0 ? \"1px solid #e0e0e0\" : \"none\",\n                                                                                        marginTop: isFirstInCategory && index > 0 ? \"4px\" : \"0\",\n                                                                                        paddingTop: isFirstInCategory && index > 0 ? \"16px\" : \"12px\",\n                                                                                        \"&:hover\": {\n                                                                                            backgroundColor: \"#f8f9fa\",\n                                                                                            borderLeft: \"3px solid #2196f3\"\n                                                                                        },\n                                                                                        \"&.Mui-selected\": {\n                                                                                            backgroundColor: \"#e3f2fd\",\n                                                                                            borderLeft: \"3px solid #1976d2\",\n                                                                                            fontWeight: 600,\n                                                                                            \"&:hover\": {\n                                                                                                backgroundColor: \"#bbdefb\"\n                                                                                            }\n                                                                                        }\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            style: {\n                                                                                                fontSize: \"1rem\",\n                                                                                                marginRight: \"8px\"\n                                                                                            },\n                                                                                            children: tipo.icon\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                            lineNumber: 1084,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                            sx: {\n                                                                                                display: \"flex\",\n                                                                                                flexDirection: \"column\",\n                                                                                                flex: 1\n                                                                                            },\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    style: {\n                                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                                        fontWeight: 600,\n                                                                                                        color: \"#333\"\n                                                                                                    },\n                                                                                                    children: tipo.value\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                    lineNumber: 1099,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    style: {\n                                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                                        fontSize: \"0.75rem\",\n                                                                                                        color: \"#666\",\n                                                                                                        fontStyle: \"italic\",\n                                                                                                        fontWeight: 500\n                                                                                                    },\n                                                                                                    children: tipo.category\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                    lineNumber: 1108,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                            lineNumber: 1092,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, \"\".concat(tipo.value, \"-\").concat(index), true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1042,\n                                                                                    columnNumber: 31\n                                                                                }, undefined);\n                                                                            })\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 961,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.personTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        sx: {\n                                                                            fontFamily: \"Inter, sans-serif\"\n                                                                        },\n                                                                        children: error.personTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1125,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            variant: \"h6\",\n                                                            sx: {\n                                                                fontFamily: \"Lexend, sans-serif\",\n                                                                fontWeight: \"600\",\n                                                                color: \"#333\",\n                                                                mb: 3,\n                                                                mt: 3,\n                                                                fontSize: \"1.2rem\"\n                                                            },\n                                                            children: \"Informaci\\xf3n del Contacto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1136,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre del Contacto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                id: \"nombreContacto\",\n                                                                name: \"personNombreContacto\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personNombreContacto),\n                                                                helperText: error.personNombreContacto,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personNombreContacto,\n                                                                fullWidth: true,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personNombreContacto && (error.personNombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1180,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1182,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1177,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    style: {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    },\n                                                                    \"& .MuiFormHelperText-root\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1156,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Cargo (Opcional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1199,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Encargado, Propietario, Administrador\",\n                                                                variant: \"outlined\",\n                                                                id: \"cargoContacto\",\n                                                                name: \"personCargoContacto\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personCargoContacto),\n                                                                helperText: error.personCargoContacto,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personCargoContacto,\n                                                                fullWidth: true,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personCargoContacto && (error.personCargoContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1226,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1228,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1223,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    style: {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    },\n                                                                    \"& .MuiFormHelperText-root\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1202,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1247,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Cordoba 123\",\n                                                                variant: \"outlined\",\n                                                                id: \"domicilio\",\n                                                                name: \"personDomicilio\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personDomicilio),\n                                                                helperText: error.personDomicilio,\n                                                                fullWidth: true,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personDomicilio && (error.personDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1274,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1276,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1271,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    style: {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    },\n                                                                    \"& .MuiFormHelperText-root\": {\n                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1250,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1246,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1296,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                id: \"telefono\",\n                                                                name: \"personTelefono\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personTelefono),\n                                                                helperText: error.personTelefono,\n                                                                fullWidth: true,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personTelefono && (error.personTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1323,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1325,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1299,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1294,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1334,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                id: \"email\",\n                                                                name: \"personMail\",\n                                                                type: \"email\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.personMail),\n                                                                helperText: error.personMail,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personMail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personMail && (error.personMail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1362,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1364,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1337,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1332,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1375,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                id: \"localidad\",\n                                                                name: \"personLocalidad\",\n                                                                type: \"text\",\n                                                                error: Boolean(error.personLocalidad),\n                                                                helperText: error.personLocalidad,\n                                                                fullWidth: true,\n                                                                required: true,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                value: formData.personLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personLocalidad && (error.personLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1403,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1405,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1400,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1378,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1373,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.personProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        name: \"personProvincia\",\n                                                                        labelId: \"demo-simple-select-label\",\n                                                                        fullWidth: true,\n                                                                        value: formData.personProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"personProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleProvinciaChange(syntheticEvent);\n                                                                        },\n                                                                        required: true,\n                                                                        inputRef: selectProvinciaRef,\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return \"Seleccione una provincia\";\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        endAdornment: formData.personProvincia && !error.personProvincia ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            position: \"end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                color: \"success\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1448,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1447,\n                                                                            columnNumber: 31\n                                                                        }, void 0) : null,\n                                                                        MenuProps: {\n                                                                            PaperProps: {\n                                                                                style: {\n                                                                                    maxHeight: 200\n                                                                                }\n                                                                            }\n                                                                        },\n                                                                        sx: {\n                                                                            minWidth: \"200px\"\n                                                                        },\n                                                                        children: provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                value: provincia,\n                                                                                children: provincia\n                                                                            }, provincia, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1462,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1421,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.personProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        children: error.personProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1468,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1417,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1412,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Cond. Frente al IVA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1478,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.personCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        id: \"condIva\",\n                                                                        name: \"personCondFrenteIva\",\n                                                                        value: formData.personCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"personCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleCondIvaChange(syntheticEvent);\n                                                                        },\n                                                                        inputRef: selectCondIvaRef,\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return \"Seleccione una opci\\xf3n\";\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        endAdornment: formData.personCondFrenteIva && !error.personCondFrenteIva ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            position: \"end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                color: \"success\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1510,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1509,\n                                                                            columnNumber: 31\n                                                                        }, void 0) : null,\n                                                                        children: condFrenteIvaOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                value: option,\n                                                                                children: option\n                                                                            }, option, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1516,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1485,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.personCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        children: error.personCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1522,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1481,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1476,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Documento\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1531,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                id: \"documento\",\n                                                                placeholder: \"Ej: 00-00000000-0\",\n                                                                name: \"personDocumento\",\n                                                                value: formData.personDocumento,\n                                                                onChange: (e)=>handleInputChange(e),\n                                                                error: Boolean(error.personDocumento),\n                                                                helperText: error.personDocumento,\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                inputRef: documentoRef,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.personDocumento && (error.personDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1558,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1560,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1555,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1534,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1528,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: {\n                                                xs: 12,\n                                                sm: 6\n                                            },\n                                            style: {\n                                                margin: \"8px 0\",\n                                                padding: \"8px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                container: true,\n                                                spacing: 1,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: {\n                                                        xs: 12,\n                                                        sm: 6\n                                                    },\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"flex-end\",\n                                                        gap: \"8px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            type: \"button\",\n                                                            variant: \"outlined\",\n                                                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                            children: \"Cancelar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1582,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            type: \"submit\",\n                                                            variant: \"contained\",\n                                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1594,\n                                                                columnNumber: 36\n                                                            }, void 0),\n                                                            sx: {\n                                                                bgcolor: \"#2E7D32\",\n                                                                color: \"#ffffff\",\n                                                                \"&:hover\": {\n                                                                    bgcolor: \"#1B5E20\"\n                                                                },\n                                                                textTransform: \"none\",\n                                                                \"& .MuiSvgIcon-root\": {\n                                                                    color: \"#ffffff\"\n                                                                }\n                                                            },\n                                                            children: estadoModal === \"add\" ? \"Registrar\" : \"Guardar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1591,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1574,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1573,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1569,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 881,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 876,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 844,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 831,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"Cj5bzcQJMCaS3rqf4z1Gu7p1JtU=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKHBhZ2luYXMpL2FncmljdWx0b3IvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFpQ01BO0FBaENxRDtBQUNHO0FBQ0g7QUFPcEM7QUFDZ0I7QUFDTztBQUNGO0FBQ007QUFDTjtBQUNHO0FBRUo7QUFFRjtBQUNGO0FBQ0c7QUFDQztBQUNjO0FBQ1A7QUFDaEI7QUFDNEI7QUFDWjtBQUNTO0FBRUQ7QUF3QjFELE1BQU0rQixxQkFBd0Q7O0lBQzVELE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNnQyxhQUFhQyxlQUFlLEdBQUdqQywrQ0FBUUEsQ0FBZ0I7SUFDOUQsTUFBTSxDQUFDa0MsTUFBTUMsUUFBUSxHQUFHbkMsK0NBQVFBLENBQUM7SUFDakMsTUFBTSxDQUFDb0MsTUFBTUMsUUFBUSxHQUFHckMsK0NBQVFBLENBQVcsRUFBRTtJQUM3QyxNQUFNLENBQUNzQyxZQUFZQyxjQUFjLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN3QyxpQkFBaUJDLG1CQUFtQixHQUFHekMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDMEMsY0FBY0MsZ0JBQWdCLEdBQUczQywrQ0FBUUEsQ0FBVyxFQUFFO0lBQzdELE1BQU1JLFdBQVdDLHNEQUFXQTtJQUM1QixNQUFNLENBQUN1QyxVQUFVQyxZQUFZLEdBQUc3QywrQ0FBUUEsQ0FBUztJQUNqRCxNQUFNLENBQUM4QyxhQUFhQyxlQUFlLEdBQUcvQywrQ0FBUUEsQ0FBbUI7SUFDakUsTUFBTSxDQUFDZ0Qsa0JBQWtCQyxvQkFBb0IsR0FBR2pELCtDQUFRQSxDQUFnQjtJQUN4RSxNQUFNa0QscUJBQXFCaEQsNkNBQU1BLENBQXVDO0lBQ3hFLE1BQU1pRCxtQkFBbUJqRCw2Q0FBTUEsQ0FBdUM7SUFDdEUsTUFBTWtELGVBQWVsRCw2Q0FBTUEsQ0FBdUM7SUFDbEUsTUFBTSxDQUFDbUQsT0FBT0MsU0FBUyxHQUFHdEQsK0NBQVFBLENBQTRCLENBQUM7SUFFL0QsTUFBTXVELHFCQUFxQixDQUFDQztRQUMxQlAsb0JBQW9CTztRQUNwQixNQUFNQyxpQkFBaUIsS0FBbUJDLElBQUksQ0FDNUMsQ0FBQ0MsTUFBUUEsSUFBSUMsRUFBRSxLQUFLSjtRQUV0QixJQUFJQyxnQkFBZ0I7WUFDbEIscURBQXFEO1lBQ3JELE1BQU0sQ0FBQ0ksV0FBV0MsVUFBVSxHQUFHTCxlQUFlTSxLQUFLLENBQUNDLEtBQUssQ0FBQztZQUUxREMsWUFBWTtnQkFDVkMsbUJBQW1CVCxlQUFlVSxXQUFXO2dCQUM3Q0MsbUJBQW1CWCxlQUFlWSxXQUFXLElBQUk7Z0JBQ2pEQyxzQkFBc0JiLGVBQWVjLGNBQWMsSUFBSTtnQkFDdkRDLHFCQUFxQmYsZUFBZWdCLGFBQWEsSUFBSTtnQkFDckRDLGlCQUFpQmpCLGVBQWVrQixTQUFTO2dCQUN6Q0MsZ0JBQWdCbkIsZUFBZW9CLFFBQVE7Z0JBQ3ZDQyxZQUFZckIsZUFBZXNCLElBQUk7Z0JBQy9CQyxpQkFBaUJuQjtnQkFDakJvQixpQkFBaUJuQjtnQkFDakJvQixxQkFBcUJ6QixlQUFlMEIsYUFBYTtnQkFDakRDLGlCQUFpQjNCLGVBQWU0QixTQUFTO1lBQzNDO1FBQ0Y7SUFDRjtJQUVBLE1BQU0sQ0FBQ0MsVUFBVXJCLFlBQVksR0FBR2pFLCtDQUFRQSxDQUFDO1FBQ3ZDLGVBQWU7UUFDZmtFLG1CQUFtQjtRQUNuQkUsbUJBQW1CO1FBQ25CRSxzQkFBc0I7UUFDdEJFLHFCQUFxQjtRQUNyQkUsaUJBQWlCO1FBQ2pCRSxnQkFBZ0I7UUFDaEJFLFlBQVk7UUFDWkUsaUJBQWlCO1FBQ2pCQyxpQkFBaUI7UUFDakJDLHFCQUFxQjtRQUNyQkUsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTUcsYUFBYTtRQUNqQjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFRCxNQUFNQyxxQkFBcUI7UUFDekIsY0FBYztRQUNkO1lBQUVDLE9BQU87WUFBd0JDLFVBQVU7WUFBZUMsTUFBTTtRQUFRO1FBQ3hFO1lBQUVGLE9BQU87WUFBdUJDLFVBQVU7WUFBZUMsTUFBTTtRQUFjO1FBQzdFO1lBQUVGLE9BQU87WUFBWUMsVUFBVTtZQUFlQyxNQUFNO1FBQU07UUFFMUQsNEJBQTRCO1FBQzVCO1lBQ0VGLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxNQUFNO1FBQ1I7UUFDQTtZQUFFRixPQUFPO1lBQWVDLFVBQVU7WUFBWUMsTUFBTTtRQUFNO1FBQzFEO1lBQ0VGLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxNQUFNO1FBQ1I7UUFFQSwyQkFBMkI7UUFDM0I7WUFDRUYsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLE1BQU07UUFDUjtRQUNBO1lBQ0VGLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxNQUFNO1FBQ1I7UUFFQSx5QkFBeUI7UUFDekI7WUFDRUYsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLE1BQU07UUFDUjtRQUNBO1lBQ0VGLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxNQUFNO1FBQ1I7UUFDQTtZQUFFRixPQUFPO1lBQWlDQyxVQUFVO1lBQVNDLE1BQU07UUFBSTtRQUN2RTtZQUFFRixPQUFPO1lBQW1CQyxVQUFVO1lBQVNDLE1BQU07UUFBSTtLQUMxRDtJQUVELE1BQU1DLHVCQUF1QjtRQUMzQjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRUQsTUFBTUMsZ0JBQWdCO1FBQ3BCOUMsZUFBZTtRQUNmK0M7UUFDQTNELFFBQVE7SUFDVjtJQUVBLE1BQU0yRCxZQUFZO1FBQ2hCN0IsWUFBWTtZQUNWQyxtQkFBbUI7WUFDbkJFLG1CQUFtQjtZQUNuQkUsc0JBQXNCO1lBQ3RCRSxxQkFBcUI7WUFDckJFLGlCQUFpQjtZQUNqQkUsZ0JBQWdCO1lBQ2hCRSxZQUFZO1lBQ1pFLGlCQUFpQjtZQUNqQkMsaUJBQWlCO1lBQ2pCQyxxQkFBcUI7WUFDckJFLGlCQUFpQjtRQUNuQjtRQUNBOUIsU0FBUyxDQUFDO0lBQ1o7SUFFQSxNQUFNeUMsbUJBQW1CLENBQ3ZCQyxRQUNBQztRQUVBLElBQUlBLFVBQVVBLFdBQVcsaUJBQWlCO1FBQzFDOUQsUUFBUTtJQUNWO0lBRUEsTUFBTStELG9CQUFvQixDQUN4QkM7UUFFQSxNQUFNLEVBQUVDLElBQUksRUFBRVgsS0FBSyxFQUFFLEdBQUdVLEVBQUVFLE1BQU07UUFFaEMsSUFDRUQsU0FBUyx1QkFDVEEsU0FBUyxxQkFDVEEsU0FBUywwQkFDVEEsU0FBUyx1QkFDVDtZQUNBLElBQUksQ0FBQyxtQkFBbUJFLElBQUksQ0FBQ2IsUUFBUTtnQkFDbkNuQyxTQUFTLENBQUNpRCxZQUFlO3dCQUN2QixHQUFHQSxTQUFTO3dCQUNaLENBQUNILEtBQUssRUFBRTtvQkFDVjtnQkFDQTtZQUNGLE9BQU87Z0JBQ0w5QyxTQUFTLENBQUNpRCxZQUFlO3dCQUN2QixHQUFHQSxTQUFTO3dCQUNaLENBQUNILEtBQUssRUFBRTtvQkFDVjtZQUNGO1FBQ0Y7UUFFQSxJQUFJQSxTQUFTLG1CQUFtQjtZQUM5QixJQUFJLENBQUMsdUJBQXVCRSxJQUFJLENBQUNiLFFBQVE7Z0JBQ3ZDbkMsU0FBUyxDQUFDaUQsWUFBZTt3QkFDdkIsR0FBR0EsU0FBUzt3QkFDWjdCLGlCQUNFO29CQUNKO2dCQUNBO1lBQ0YsT0FBTztnQkFDTHBCLFNBQVMsQ0FBQ2lELFlBQWU7d0JBQ3ZCLEdBQUdBLFNBQVM7d0JBQ1o3QixpQkFBaUI7b0JBQ25CO1lBQ0Y7UUFDRjtRQUVBLElBQUkwQixTQUFTLGtCQUFrQjtZQUM3QixxQ0FBcUM7WUFDckMsTUFBTUksVUFBVWYsTUFBTWdCLE9BQU8sQ0FBQyxPQUFPO1lBRXJDLHVCQUF1QjtZQUN2QixJQUFJRCxRQUFRRSxNQUFNLEdBQUcsSUFBSTtZQUV6QixJQUFJQztZQUNKLElBQUlILFFBQVFFLE1BQU0sSUFBSSxHQUFHO2dCQUN2QkMsWUFBWUg7WUFDZCxPQUFPO2dCQUNMRyxZQUFZLEdBQTBCSCxPQUF2QkEsUUFBUUksS0FBSyxDQUFDLEdBQUcsSUFBRyxLQUFvQixPQUFqQkosUUFBUUksS0FBSyxDQUFDO1lBQ3REO1lBRUEsOEJBQThCO1lBQzlCLE1BQU1DLGdCQUFnQixnQkFBZ0JQLElBQUksQ0FBQ0s7WUFFM0NyRCxTQUFTLENBQUNpRCxZQUFlO29CQUN2QixHQUFHQSxTQUFTO29CQUNaM0IsZ0JBQ0UrQixVQUFVRCxNQUFNLEtBQUssTUFBTSxDQUFDRyxnQkFDeEIsOENBQ0E7Z0JBQ1I7WUFFQTVDLFlBQVksQ0FBQzZDLFlBQWU7b0JBQzFCLEdBQUdBLFNBQVM7b0JBQ1osQ0FBQ1YsS0FBSyxFQUFFTztnQkFDVjtZQUNBO1FBQ0Y7UUFFQSxJQUFJUCxTQUFTLGNBQWM7WUFDekIsdUNBQXVDO1lBQ3ZDLE1BQU1XLGFBQWE7WUFFbkIsZ0RBQWdEO1lBQ2hELElBQUl0QixTQUFTLENBQUNzQixXQUFXVCxJQUFJLENBQUNiLFFBQVE7Z0JBQ3BDbkMsU0FBUyxDQUFDaUQsWUFBZTt3QkFDdkIsR0FBR0EsU0FBUzt3QkFDWnpCLFlBQVk7b0JBQ2Q7WUFDRixPQUFPO2dCQUNMeEIsU0FBUyxDQUFDaUQsWUFBZTt3QkFDdkIsR0FBR0EsU0FBUzt3QkFDWnpCLFlBQVk7b0JBQ2Q7WUFDRjtRQUNGO1FBRUEsSUFBSXNCLFNBQVMsbUJBQW1CO1lBQzlCLHFDQUFxQztZQUNyQyxNQUFNSSxVQUFVZixNQUFNZ0IsT0FBTyxDQUFDLE9BQU87WUFFckMsZ0NBQWdDO1lBQ2hDLElBQUlELFFBQVFFLE1BQU0sR0FBRyxJQUFJO1lBRXpCLElBQUlDO1lBQ0osSUFBSUgsUUFBUUUsTUFBTSxJQUFJLEdBQUc7Z0JBQ3ZCQyxZQUFZSDtZQUNkLE9BQU8sSUFBSUEsUUFBUUUsTUFBTSxJQUFJLElBQUk7Z0JBQy9CQyxZQUFZLEdBQTBCSCxPQUF2QkEsUUFBUUksS0FBSyxDQUFDLEdBQUcsSUFBRyxLQUFvQixPQUFqQkosUUFBUUksS0FBSyxDQUFDO1lBQ3RELE9BQU87Z0JBQ0xELFlBQVksR0FBMEJILE9BQXZCQSxRQUFRSSxLQUFLLENBQUMsR0FBRyxJQUFHLEtBRzlCSixPQUhpQ0EsUUFBUUksS0FBSyxDQUNqRCxHQUNBLEtBQ0EsS0FBeUIsT0FBdEJKLFFBQVFJLEtBQUssQ0FBQyxJQUFJO1lBQ3pCO1lBRUEsOEJBQThCO1lBQzlCLE1BQU1DLGdCQUFnQixzQkFBc0JQLElBQUksQ0FBQ0s7WUFFakRyRCxTQUFTLENBQUNpRCxZQUFlO29CQUN2QixHQUFHQSxTQUFTO29CQUNabkIsaUJBQ0V1QixVQUFVRCxNQUFNLEtBQUssTUFBTSxDQUFDRyxnQkFDeEIsZ0RBQ0E7Z0JBQ1I7WUFFQTVDLFlBQVksQ0FBQzZDLFlBQWU7b0JBQzFCLEdBQUdBLFNBQVM7b0JBQ1osQ0FBQ1YsS0FBSyxFQUFFTztnQkFDVjtZQUNBO1FBQ0Y7UUFFQTFDLFlBQVksQ0FBQzZDLFlBQWU7Z0JBQzFCLEdBQUdBLFNBQVM7Z0JBQ1osQ0FBQ1YsS0FBSyxFQUFFWDtZQUNWO0lBQ0Y7SUFFQSxNQUFNdUIsZUFBZSxDQUFDYjtRQUNwQkEsRUFBRWMsY0FBYztJQUNsQjtJQUVBLE1BQU1DLG9CQUFvQjtRQUN4QnpFLG1CQUFtQixDQUFDRDtJQUN0QjtJQUVBLE1BQU0yRSxVQUFVO1FBQ2Q7WUFDRUMsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1lBQ2pCQyxZQUFZLENBQUNDLHVCQUNYLDhEQUFDbEcsbURBQUdBO29CQUFDbUcsSUFBSTt3QkFBRUMsSUFBSTtvQkFBRTs7c0NBQ2YsOERBQUN2RyxzRkFBVUE7NEJBQ1R3RyxTQUFROzRCQUNSRixJQUFJO2dDQUNGRyxZQUFZO2dDQUNaQyxPQUFPO2dDQUNQQyxVQUFVO2dDQUNWQyxZQUFZOzRCQUNkO3NDQUVDUCxPQUFPOUQsR0FBRyxDQUFDUSxXQUFXOzs7Ozs7d0JBRXhCc0QsT0FBTzlELEdBQUcsQ0FBQ1UsV0FBVyxrQkFDckIsOERBQUNqRCxzRkFBVUE7NEJBQ1R3RyxTQUFROzRCQUNSRixJQUFJO2dDQUNGSSxPQUFPO2dDQUNQQyxVQUFVO2dDQUNWRSxXQUFXOzRCQUNiO3NDQUVDUixPQUFPOUQsR0FBRyxDQUFDVSxXQUFXOzs7Ozs7Ozs7Ozs7UUFLakM7UUFDQTtZQUNFK0MsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1lBQ2pCQyxZQUFZLENBQUNDLHVCQUNYLDhEQUFDbEcsbURBQUdBO29CQUFDbUcsSUFBSTt3QkFBRUMsSUFBSTtvQkFBRTs4QkFDZEYsT0FBTzlELEdBQUcsQ0FBQ1ksY0FBYyxpQkFDeEI7OzBDQUNFLDhEQUFDbkQsc0ZBQVVBO2dDQUNUd0csU0FBUTtnQ0FDUkYsSUFBSTtvQ0FDRkcsWUFBWTtvQ0FDWkMsT0FBTztvQ0FDUEMsVUFBVTtvQ0FDVkMsWUFBWTtnQ0FDZDswQ0FFQ1AsT0FBTzlELEdBQUcsQ0FBQ1ksY0FBYzs7Ozs7OzRCQUUzQmtELE9BQU85RCxHQUFHLENBQUNjLGFBQWEsa0JBQ3ZCLDhEQUFDckQsc0ZBQVVBO2dDQUNUd0csU0FBUTtnQ0FDUkYsSUFBSTtvQ0FDRkksT0FBTztvQ0FDUEMsVUFBVTtvQ0FDVkUsV0FBVztnQ0FDYjswQ0FFQ1IsT0FBTzlELEdBQUcsQ0FBQ2MsYUFBYTs7Ozs7OztxREFLL0IsOERBQUNyRCxzRkFBVUE7d0JBQ1R3RyxTQUFRO3dCQUNSRixJQUFJOzRCQUNGSSxPQUFPOzRCQUNQRyxXQUFXOzRCQUNYRixVQUFVO3dCQUNaO2tDQUNEOzs7Ozs7Ozs7OztRQU1UO1FBQ0E7WUFDRVgsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO0tBQ0Q7SUFFRCw2QkFBNkIsR0FDN0IsTUFBTVcsbUJBQW1CO1FBQ3ZCQyxRQUFRQyxHQUFHLENBQUM7UUFDWnJHLGdCQUFnQjtRQUNoQixNQUFNZ0MsUUFBUSxHQUFpQ3VCLE9BQTlCQSxTQUFTTixlQUFlLEVBQUMsT0FBOEIsT0FBekJNLFNBQVNMLGVBQWU7UUFDdkUsTUFBTW9ELFlBQVk7WUFDaEJsRSxhQUFhbUIsU0FBU3BCLGlCQUFpQjtZQUN2Q0csYUFBYWlCLFNBQVNsQixpQkFBaUI7WUFDdkNHLGdCQUFnQmUsU0FBU2hCLG9CQUFvQjtZQUM3Q0csZUFBZWEsU0FBU2QsbUJBQW1CO1lBQzNDRyxXQUFXVyxTQUFTWixlQUFlO1lBQ25DRyxVQUFVUyxTQUFTVixjQUFjO1lBQ2pDRyxNQUFNTyxTQUFTUixVQUFVO1lBQ3pCZixPQUFPQTtZQUNQRCxXQUFXd0IsU0FBU0wsZUFBZTtZQUNuQ0UsZUFBZUcsU0FBU0osbUJBQW1CO1lBQzNDRyxXQUFXQyxTQUFTRixlQUFlO1FBQ3JDO1FBRUEsb0NBQW9DO1FBQ3BDLE1BQU1rRCxTQUFTaEQ7UUFDZjZDLFFBQVFDLEdBQUcsQ0FBQ0U7UUFDWixJQUFJQSxRQUFRO1lBQ1ZoRixTQUFTZ0Y7WUFDVDtRQUNGO1FBRUEsNkNBQTZDO1FBQzdDSCxRQUFRQyxHQUFHLENBQUMsb0JBQWlCQyxVQUFVbEUsV0FBVztRQUNsRGdFLFFBQVFDLEdBQUcsQ0FBQyxpQkFBY0MsVUFBVTFELFNBQVM7UUFDN0N3RCxRQUFRQyxHQUFHLENBQUMsZ0JBQWFDLFVBQVV4RCxRQUFRO1FBQzNDc0QsUUFBUUMsR0FBRyxDQUFDLFNBQVNDLFVBQVV0RCxJQUFJO1FBQ25Db0QsUUFBUUMsR0FBRyxDQUFDLFVBQVVyRTtRQUN0Qm9FLFFBQVFDLEdBQUcsQ0FBQyw0QkFBeUJDLFVBQVVsRCxhQUFhO1FBQzVEZ0QsUUFBUUMsR0FBRyxDQUFDLGNBQWNDLFVBQVVoRCxTQUFTO1FBRTdDLElBQUk7WUFDRixNQUFNa0QsTUFBTSxNQUFNQyxNQUFNLHdDQUF3QztnQkFDOURDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDUjtZQUN2QjtZQUVBLHdDQUF3QztZQUN4QyxJQUFJLENBQUNFLElBQUlPLEVBQUUsRUFBRTtnQkFDWCxNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQWpEO1lBQ0EsTUFBTWtELGVBQWUsTUFBTUM7WUFDM0I1RyxRQUFRMkc7WUFDUjdHLFFBQVE7UUFDVixFQUFFLE9BQU9rQixPQUFPO1lBQ2Q4RSxRQUFROUUsS0FBSyxDQUFDLDBCQUEwQkE7UUFDMUM7SUFDRjtJQUVBLDJDQUEyQyxHQUMzQyxNQUFNNEYsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRixNQUFNVixNQUFNLE1BQU1DLE1BQU07WUFDeEIsSUFBSSxDQUFDRCxJQUFJTyxFQUFFLEVBQUU7Z0JBQ1gsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBQ0EsTUFBTUMsZUFBZSxNQUFNVCxJQUFJVyxJQUFJO1lBQ25DLE9BQU9GO1FBQ1QsRUFBRSxPQUFPM0YsT0FBTztZQUNkOEUsUUFBUTlFLEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDLG9EQUFvRDtZQUNwRCxPQUFPLEVBQUU7UUFDWDtJQUNGO0lBRUFwRCxnREFBU0EsQ0FBQztRQUNSLE1BQU1rSixVQUFVO1lBQ2QsTUFBTUgsZUFBZSxNQUFNQztZQUMzQjVHLFFBQVEyRztRQUNWO1FBRUFHO0lBQ0YsR0FBRyxFQUFFO0lBRUwsNEJBQTRCLEdBQzVCLE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQixNQUFNQyxjQUFjRCxNQUFNaEQsTUFBTSxDQUFDWixLQUFLO1FBQ3RDOEQsY0FBY0Q7UUFFZCxNQUFNRSxlQUFlcEgsS0FBS3FILE1BQU0sQ0FBQyxDQUFDOUY7WUFDaEMsT0FDRUEsSUFBSVEsV0FBVyxDQUFDdUYsV0FBVyxHQUFHQyxRQUFRLENBQUNMLFlBQVlJLFdBQVcsT0FDOUQvRixJQUFJZ0IsU0FBUyxDQUFDK0UsV0FBVyxHQUFHQyxRQUFRLENBQUNMLFlBQVlJLFdBQVcsT0FDNUQvRixJQUFJa0IsUUFBUSxDQUFDNkUsV0FBVyxHQUFHQyxRQUFRLENBQUNMLFlBQVlJLFdBQVcsT0FDM0QvRixJQUFJb0IsSUFBSSxDQUFDMkUsV0FBVyxHQUFHQyxRQUFRLENBQUNMLFlBQVlJLFdBQVcsT0FDdkQvRixJQUFJSSxLQUFLLENBQUMyRixXQUFXLEdBQUdDLFFBQVEsQ0FBQ0wsWUFBWUksV0FBVyxPQUN4RC9GLElBQUl3QixhQUFhLENBQUN1RSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0wsWUFBWUksV0FBVyxPQUNoRS9GLElBQUkwQixTQUFTLENBQUNxRSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0wsWUFBWUksV0FBVztRQUVoRTtRQUNBL0csZ0JBQWdCNkc7SUFDbEI7SUFFQSw4QkFBOEIsR0FDOUIsTUFBTUksc0JBQXNCLE9BQU9oRztRQUNqQ3VFLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUJ4RTtRQUVuQyxJQUFJO1lBQ0YsTUFBTTJFLE1BQU0sTUFBTUMsTUFBTSx3Q0FBMkMsT0FBSDVFLEtBQU07Z0JBQ3BFNkUsUUFBUTtZQUNWO1lBRUEsSUFBSUYsSUFBSU8sRUFBRSxFQUFFO2dCQUNWWCxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osbUVBQW1FO2dCQUNuRSxNQUFNWSxlQUFlLE1BQU1DO2dCQUMzQjVHLFFBQVEyRztZQUNWLE9BQU87Z0JBQ0xiLFFBQVE5RSxLQUFLLENBQUMsaUNBQWlDTztZQUNqRDtRQUNGLEVBQUUsT0FBT1AsT0FBTztZQUNkOEUsUUFBUTlFLEtBQUssQ0FBQyw0Q0FBeUNBO1FBQ3pEO0lBQ0Y7SUFFQSw4QkFBOEIsR0FDOUIsTUFBTXdHLGFBQWEsT0FBT2pHO1FBQ3hCLElBQUk7WUFDRixNQUFNMkUsTUFBTSxNQUFNQyxNQUFNLHdDQUEyQyxPQUFINUUsS0FBTTtnQkFDcEU2RSxRQUFRO1lBQ1Y7WUFFQSxJQUFJRixJQUFJTyxFQUFFLEVBQUU7Z0JBQ1ZYLFFBQVFDLEdBQUcsQ0FBQztnQkFFWixNQUFNMEIsYUFBYSxNQUFNdkIsSUFBSVcsSUFBSTtnQkFDakNqRixZQUFZO29CQUNWQyxtQkFBbUI0RixXQUFXM0YsV0FBVztvQkFDekNDLG1CQUFtQjBGLFdBQVd6RixXQUFXLElBQUk7b0JBQzdDQyxzQkFBc0J3RixXQUFXdkYsY0FBYyxJQUFJO29CQUNuREMscUJBQXFCc0YsV0FBV3JGLGFBQWEsSUFBSTtvQkFDakRDLGlCQUFpQm9GLFdBQVduRixTQUFTO29CQUNyQ0MsZ0JBQWdCa0YsV0FBV2pGLFFBQVE7b0JBQ25DQyxZQUFZZ0YsV0FBVy9FLElBQUk7b0JBQzNCQyxpQkFBaUI4RSxXQUFXL0YsS0FBSyxDQUFDQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQytGLElBQUk7b0JBQ3REOUUsaUJBQWlCNkUsV0FBVy9GLEtBQUssQ0FBQ0MsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMrRixJQUFJO29CQUN0RDdFLHFCQUFxQjRFLFdBQVczRSxhQUFhO29CQUM3Q0MsaUJBQWlCMEUsV0FBV3pFLFNBQVM7Z0JBQ3ZDO1lBQ0YsT0FBTztnQkFDTDhDLFFBQVE5RSxLQUFLLENBQUMsa0NBQWtDTztZQUNsRDtRQUNGLEVBQUUsT0FBT1AsT0FBTztZQUNkOEUsUUFBUTlFLEtBQUssQ0FBQyw0Q0FBeUNBO1FBQ3pEO1FBRUFOLGVBQWU7UUFDZlosUUFBUTtJQUNWO0lBRUEsNENBQTRDLEdBQzVDLE1BQU02SCxzQkFBc0I7UUFDMUIsSUFBSSxDQUFDaEksYUFBYTtRQUNsQixNQUFNK0IsUUFBUSxHQUFpQ3VCLE9BQTlCQSxTQUFTTixlQUFlLEVBQUMsT0FBOEIsT0FBekJNLFNBQVNMLGVBQWU7UUFDdkUsTUFBTW9ELFlBQW9CO1lBQ3hCekUsSUFBSTVCLFlBQVk0QixFQUFFO1lBQ2xCTyxhQUFhbUIsU0FBU3BCLGlCQUFpQjtZQUN2Q0csYUFBYWlCLFNBQVNsQixpQkFBaUI7WUFDdkNHLGdCQUFnQmUsU0FBU2hCLG9CQUFvQjtZQUM3Q0csZUFBZWEsU0FBU2QsbUJBQW1CO1lBQzNDRyxXQUFXVyxTQUFTWixlQUFlO1lBQ25DRyxVQUFVUyxTQUFTVixjQUFjO1lBQ2pDRyxNQUFNTyxTQUFTUixVQUFVO1lBQ3pCZixPQUFPQTtZQUNQRCxXQUFXd0IsU0FBU0wsZUFBZTtZQUNuQ0UsZUFBZUcsU0FBU0osbUJBQW1CO1lBQzNDRyxXQUFXQyxTQUFTRixlQUFlO1FBQ3JDO1FBRUEsSUFBSTtZQUNGLE1BQU1tRCxNQUFNLE1BQU1DLE1BQU8sd0NBQXVDO2dCQUM5REMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNSO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDRSxJQUFJTyxFQUFFLEVBQUU7Z0JBQ1gsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsaUNBQWlDO1lBQ2pDLE1BQU1rQixjQUFjN0gsS0FBSzhILEdBQUcsQ0FBQyxDQUFDdkc7Z0JBQzVCLElBQUlBLElBQUlDLEVBQUUsS0FBS3lFLFVBQVV6RSxFQUFFLEVBQUU7b0JBQzNCLE9BQU95RTtnQkFDVDtnQkFDQSxPQUFPMUU7WUFDVDtZQUVBdEIsUUFBUTRIO1lBQ1JuRTtZQUNBM0QsUUFBUTtRQUNWLEVBQUUsT0FBT2tCLE9BQU87WUFDZDhFLFFBQVE5RSxLQUFLLENBQUMsMEJBQTBCQTtRQUMxQztJQUNGO0lBRUEsTUFBTThHLHlCQUF5QixDQUM3QmQ7UUFFQSxJQUFJQSxNQUFNZSxHQUFHLEtBQUssV0FBV2xILG1CQUFtQm1ILE9BQU8sRUFBRTtZQUN2RG5ILG1CQUFtQm1ILE9BQU8sQ0FBQ0MsS0FBSztRQUNsQztJQUNGO0lBRUEsTUFBTUMsd0JBQXdCLENBQzVCbEI7UUFFQW5ELGtCQUFrQm1EO1FBQ2xCbUIsV0FBVztZQUNULElBQUlySCxpQkFBaUJrSCxPQUFPLEVBQUU7Z0JBQzVCbEgsaUJBQWlCa0gsT0FBTyxDQUFDQyxLQUFLO1lBQ2hDO1FBQ0YsR0FBRztJQUNMO0lBRUEsTUFBTUcsc0JBQXNCLENBQzFCcEI7UUFFQW5ELGtCQUFrQm1EO1FBQ2xCbUIsV0FBVztZQUNULElBQUlwSCxhQUFhaUgsT0FBTyxFQUFFO2dCQUN4QmpILGFBQWFpSCxPQUFPLENBQUNDLEtBQUs7WUFDNUI7UUFDRixHQUFHO0lBQ0w7SUFFQSxNQUFNSSx5QkFBeUIsQ0FBQzlHO1FBQzlCLE1BQU0rRyxxQkFBcUJ2SSxLQUFLc0IsSUFBSSxDQUFDLENBQUNDLE1BQVFBLElBQUlDLEVBQUUsS0FBS0E7UUFFekQsSUFBSStHLG9CQUFvQjtZQUN0QixNQUFNYixhQUFhO2dCQUNqQmxHLElBQUkrRyxtQkFBbUIvRyxFQUFFO2dCQUN6Qk8sYUFBYXdHLG1CQUFtQnhHLFdBQVc7WUFDN0M7WUFFQSxxQ0FBcUM7WUFDckN5RyxhQUFhQyxPQUFPLENBQUMsc0JBQXNCakMsS0FBS0MsU0FBUyxDQUFDaUI7WUFFMUQscURBQXFEO1lBQ3JEZ0IsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekI7SUFDRjtJQUVBLE1BQU0sQ0FBQ0MsWUFBWTFCLGNBQWMsR0FBR3ZKLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2tMLE1BQU1DLFFBQVEsR0FBR25MLCtDQUFRQSxDQUFDO0lBQ2pDLE1BQU1vTCxxQkFBcUIsQ0FBQy9CO1FBQzFCRSxjQUFjRixNQUFNaEQsTUFBTSxDQUFDWixLQUFLO1FBQ2hDMEYsUUFBUTtJQUNWO0lBRUEsTUFBTUUsY0FBYztRQUNsQnhELFlBQVk7UUFDWkMsT0FBTztRQUNQd0QsY0FBYztRQUNkQyxTQUFTO1FBQ1RDLFlBQVk7SUFDZDtJQUVBLHFCQUNFOzswQkFDRSw4REFBQ2pLLG1EQUFHQTtnQkFDRm1HLElBQUk7b0JBQ0Y2RCxTQUFTO29CQUNURSxnQkFBZ0I7b0JBQ2hCQyxJQUFJO29CQUNKQyxJQUFJO2dCQUNOOztrQ0FFQSw4REFBQ3BLLG1EQUFHQTs7MENBQ0YsOERBQUNILHNGQUFVQTtnQ0FDVHdHLFNBQVE7Z0NBQ1JnRSxXQUFVO2dDQUNWbEUsSUFBSTtvQ0FDRkcsWUFBWTtvQ0FDWjJELFlBQVk7Z0NBQ2Q7MENBQ0Q7Ozs7OzswQ0FHRCw4REFBQ3BLLHNGQUFVQTtnQ0FDVHdHLFNBQVE7Z0NBQ1JGLElBQUk7b0NBQ0ZJLE9BQU87b0NBQ1A2RCxJQUFJO29DQUNKSCxZQUFZLEdBQTBCLE9BQXZCMUwsNEtBQVcsQ0FBQzBMLFVBQVU7Z0NBQ3ZDOzBDQUNEOzs7Ozs7Ozs7Ozs7a0NBSUgsOERBQUNsTCw0R0FBTUE7d0JBQ0xzSCxTQUFRO3dCQUNSa0UsU0FBU2pHO3dCQUNUNkIsSUFBSTs0QkFDRnFFLFNBQVM7NEJBQ1RqRSxPQUFPOzRCQUNQLFdBQVc7Z0NBQUVpRSxTQUFTOzRCQUFVOzRCQUNoQ0MsUUFBUTs0QkFDUkMsV0FBVzs0QkFDWFQsWUFBWSxHQUEwQixPQUF2QjFMLDRLQUFXLENBQUMwTCxVQUFVO3dCQUN2Qzt3QkFDQVUseUJBQVcsOERBQUMvTCx1RUFBZUE7Ozs7O2tDQUM1Qjs7Ozs7Ozs7Ozs7OzBCQUtILDhEQUFDTSw0R0FBS0E7Z0JBQUMwTCxXQUFXO2dCQUFHekUsSUFBSTtvQkFBRTBFLEdBQUc7b0JBQUdWLElBQUk7b0JBQUdXLGNBQWM7Z0JBQUU7O2tDQUN0RCw4REFBQ2xMLHNGQUFTQTt3QkFDUm1MLFNBQVM7d0JBQ1QxRSxTQUFRO3dCQUNSMkUsYUFBWTt3QkFDWjlHLE9BQU93Rjt3QkFDUHVCLFVBQVVwRDt3QkFDVnFELFlBQVk7NEJBQ1ZDLDhCQUNFLDhEQUFDbE0sNkdBQWNBO2dDQUFDbU0sVUFBUzswQ0FDdkIsNEVBQUMzTCx1RkFBVUE7b0NBQUM4SyxTQUFTNUU7OENBQ25CLDRFQUFDdkYseUZBQVVBOzs7Ozs7Ozs7Ozs7Ozs7d0JBSW5CO3dCQUNBK0YsSUFBSTs0QkFBRWdFLElBQUk7d0JBQUU7Ozs7OztrQ0FFZCw4REFBQ3JLLG1FQUFTQTt3QkFDUjhGLFNBQVNBO3dCQUNUL0UsTUFBTU0sYUFBYWdFLE1BQU0sR0FBRyxJQUFJaEUsZUFBZU47d0JBQy9Dd0ssUUFBUTt3QkFDUkMsc0JBQXNCakQ7d0JBQ3RCa0Qsc0JBQXNCakQ7d0JBQ3RCNUgsZ0JBQWdCLENBQUMwQixNQUFRMUIsZUFBZTBCO3dCQUN4QzNCLGFBQWFBO3dCQUNiK0ssY0FBY3JDOzs7Ozs7Ozs7Ozs7MEJBSWxCLDhEQUFDaEssbUZBQU1BO2dCQUNMd0IsTUFBTUE7Z0JBQ044SyxTQUFTakg7Z0JBQ1RrSCxVQUFTO2dCQUNUWCxTQUFTO2dCQUNUNUUsSUFBSTtvQkFDRixzQkFBc0I7d0JBQ3BCSixPQUFPO3dCQUNQMkYsVUFBVTt3QkFDVkMsV0FBVztvQkFDYjtnQkFDRjswQkFFQSw0RUFBQzNMLG1EQUFHQTtvQkFBQ21HLElBQUk7d0JBQUUwRSxHQUFHO29CQUFFOztzQ0FFZCw4REFBQzdLLG1EQUFHQTs0QkFBQ21HLElBQUk7Z0NBQUVnRSxJQUFJOzRCQUFFOzs4Q0FDZiw4REFBQzlLLHdGQUFXQTtvQ0FDVjhHLElBQUk7d0NBQ0YwRSxHQUFHO3dDQUNIWixZQUFZO3dDQUNaekQsVUFBVTt3Q0FDVkYsWUFBWTt3Q0FDWkMsT0FBTztvQ0FDVDs4Q0FDRDs7Ozs7OzhDQUdELDhEQUFDakgsOEZBQWlCQTtvQ0FDaEI2RyxJQUFJO3dDQUNGMEUsR0FBRzt3Q0FDSFQsSUFBSTt3Q0FDSkgsWUFBWTt3Q0FDWjFELE9BQU87b0NBQ1Q7OENBQ0Q7Ozs7Ozs7Ozs7OztzQ0FJSCw4REFBQzlHLHVGQUFVQTs0QkFDVG1NLGNBQVc7NEJBQ1hyQixTQUFTLENBQUN6QyxRQUFVdEQsaUJBQWlCc0QsT0FBTzs0QkFDNUMzQixJQUFJO2dDQUFFaUYsVUFBVTtnQ0FBWVMsT0FBTztnQ0FBR0MsS0FBSzs0QkFBRTtzQ0FFN0MsNEVBQUMvTCxrRUFBU0E7Ozs7Ozs7Ozs7c0NBRVosOERBQUNDLG1EQUFHQTs0QkFDRnFLLFdBQVU7NEJBQ1YwQixVQUFVdEc7NEJBQ1Z1RyxXQUFXek4sZ0xBQWU7c0NBRTFCLDRFQUFDYSwwRkFBYUE7Z0NBQUMrRyxJQUFJO29DQUFFMEUsR0FBRztnQ0FBRTswQ0FHeEIsNEVBQUM3TCw2R0FBSUE7b0NBQUNpTixTQUFTO29DQUFDQyxTQUFTOztzREFFdkIsOERBQUNsTiw2R0FBSUE7NENBQUNtTixNQUFNO2dEQUFFQyxJQUFJOzRDQUFHO3NEQUNuQiw0RUFBQ3BOLDZHQUFJQTtnREFBQ2lOLFNBQVM7Z0RBQUNDLFNBQVM7O2tFQUV2Qiw4REFBQ2xOLDZHQUFJQTt3REFBQ21OLE1BQU07NERBQUVDLElBQUk7d0RBQUc7a0VBQ25CLDRFQUFDdk0sc0ZBQVVBOzREQUNUd0csU0FBUTs0REFDUkYsSUFBSTtnRUFDRjhELFlBQVk7Z0VBQ1ozRCxZQUFZO2dFQUNaQyxPQUFPO2dFQUNQNEQsSUFBSTtnRUFDSkMsSUFBSTtnRUFDSjVELFVBQVU7NERBQ1o7c0VBQ0Q7Ozs7Ozs7Ozs7O2tFQU1ILDhEQUFDeEgsNkdBQUlBO3dEQUFDbU4sTUFBTTs0REFBRUMsSUFBSTs0REFBSUMsSUFBSTt3REFBRTs7MEVBQzFCLDhEQUFDeE0sc0ZBQVVBO2dFQUFDd0csU0FBUTtnRUFBUUYsSUFBSTJEOzBFQUFhOzs7Ozs7MEVBRzdDLDhEQUFDbEssc0ZBQVNBO2dFQUNSb0wsYUFBWTtnRUFDWjNFLFNBQVE7Z0VBQ1JoRSxJQUFHO2dFQUNId0MsTUFBSztnRUFDTHlILE1BQUs7Z0VBQ0x4SyxPQUFPeUssUUFBUXpLLE1BQU1hLGlCQUFpQjtnRUFDdEM2SixZQUFZMUssTUFBTWEsaUJBQWlCO2dFQUNuQ3NJLFVBQVUsQ0FDUnJHLElBSUFELGtCQUNFQztnRUFHSlYsT0FBT0gsU0FBU3BCLGlCQUFpQjtnRUFDakM4SixVQUFVbEwsZ0JBQWdCO2dFQUMxQndKLFNBQVM7Z0VBQ1RHLFlBQVk7b0VBQ1Z3Qiw0QkFDRSw4REFBQ3pOLDZHQUFjQTt3RUFBQ21NLFVBQVM7a0ZBQ3RCckgsU0FBU3BCLGlCQUFpQixJQUN4QmIsQ0FBQUEsTUFBTWEsaUJBQWlCLGlCQUN0Qiw4REFBQ3pDLGtFQUFTQTs0RUFBQ3FHLE9BQU07Ozs7O21HQUVqQiw4REFBQ3RHLHdFQUFlQTs0RUFBQ3NHLE9BQU07Ozs7O2tGQUN6Qjs7Ozs7O29FQUdOK0QsT0FBTzt3RUFBRUwsWUFBWTtvRUFBb0I7Z0VBQzNDO2dFQUNBOUQsSUFBSTtvRUFDRix5QkFBeUI7d0VBQ3ZCOEQsWUFBWTtvRUFDZDtvRUFDQSw2QkFBNkI7d0VBQzNCQSxZQUFZO29FQUNkO2dFQUNGOzs7Ozs7Ozs7Ozs7a0VBR0osOERBQUNqTCw2R0FBSUE7d0RBQUNtTixNQUFNOzREQUFFQyxJQUFJOzREQUFJQyxJQUFJO3dEQUFFOzswRUFDMUIsOERBQUN4TSxzRkFBVUE7Z0VBQUN3RyxTQUFRO2dFQUFRRixJQUFJMkQ7MEVBQWE7Ozs7OzswRUFHN0MsOERBQUN2Syx3RkFBV0E7Z0VBQ1Z3TCxTQUFTO2dFQUNUakosT0FBT3lLLFFBQVF6SyxNQUFNZSxpQkFBaUI7O2tGQUV0Qyw4REFBQ2xELG1GQUFNQTt3RUFDTDBDLElBQUc7d0VBQ0h3QyxNQUFLO3dFQUNMWCxPQUFPSCxTQUFTbEIsaUJBQWlCO3dFQUNqQ29JLFVBQVUsQ0FBQ3JHLElBQ1RELGtCQUNFQzt3RUFHSitILFlBQVk7d0VBQ1pDLFdBQVc7NEVBQ1RDLFlBQVk7Z0ZBQ1YxRyxJQUFJO29GQUNGMkcsV0FBVztvRkFDWC9HLE9BQU87b0ZBQ1BnSCxVQUFVO29GQUNWLHVCQUF1Qjt3RkFDckI5QyxZQUFZO3dGQUNaekQsVUFBVTt3RkFDVkYsWUFBWTt3RkFDWkcsWUFBWTt3RkFDWnVHLFNBQVM7d0ZBQ1RDLFlBQVk7d0ZBQ1pDLFVBQVU7d0ZBQ1Z2QixXQUFXO3dGQUNYcEYsT0FBTzt3RkFDUDRHLGNBQWM7d0ZBQ2QsZ0JBQWdCOzRGQUNkQSxjQUFjO3dGQUNoQjtvRkFDRjtnRkFDRjs0RUFDRjt3RUFDRjt3RUFDQWhILElBQUk7NEVBQ0Y4RCxZQUFZOzRFQUNaUSxRQUFROzRFQUNSLHVCQUF1QjtnRkFDckJSLFlBQVk7Z0ZBQ1p6RCxVQUFVO2dGQUNWRixZQUFZO2dGQUNaRyxZQUFZO2dGQUNadUcsU0FBUztnRkFDVHpHLE9BQU87Z0ZBQ1BrRSxRQUFRO2dGQUNSVCxTQUFTO2dGQUNUb0QsWUFBWTs0RUFDZDs0RUFDQSxzQ0FBc0M7Z0ZBQ3BDQyxhQUFhOzRFQUNmOzRFQUNBLDRDQUE0QztnRkFDMUNBLGFBQWE7NEVBQ2Y7NEVBQ0Esa0RBQWtEO2dGQUNoREEsYUFBYTtnRkFDYkMsYUFBYTs0RUFDZjt3RUFDRjs7MEZBRUEsOERBQUM1TixxRkFBUUE7Z0ZBQ1B3RSxPQUFNO2dGQUNOdUksUUFBUTtnRkFDUnRHLElBQUk7b0ZBQ0Y4RCxZQUFZO29GQUNadkQsV0FBVztvRkFDWEgsT0FBTztvRkFDUEMsVUFBVTtvRkFDVkYsWUFBWTtvRkFDWmlILGlCQUFpQjtnRkFDbkI7MEZBQ0Q7Ozs7Ozs0RUFHQXRKLG1CQUFtQjBFLEdBQUcsQ0FBQyxDQUFDNkUsTUFBTUM7Z0ZBQzdCLE1BQU1DLG9CQUNKRCxVQUFVLEtBQ1Z4SixrQkFBa0IsQ0FBQ3dKLFFBQVEsRUFBRSxDQUFDdEosUUFBUSxLQUNwQ3FKLEtBQUtySixRQUFRO2dGQUVqQixxQkFDRSw4REFBQ3pFLHFGQUFRQTtvRkFFUHdFLE9BQU9zSixLQUFLdEosS0FBSztvRkFDakJpQyxJQUFJO3dGQUNGOEQsWUFBWTt3RkFDWnpELFVBQVU7d0ZBQ1ZGLFlBQVk7d0ZBQ1pHLFlBQVk7d0ZBQ1p1RyxTQUFTO3dGQUNUQyxZQUFZO3dGQUNaQyxVQUFVO3dGQUNWdkIsV0FBVzt3RkFDWDNCLFNBQVM7d0ZBQ1RvRCxZQUFZO3dGQUNaTyxLQUFLO3dGQUNMcEgsT0FBTzt3RkFDUHFILFdBQ0VGLHFCQUFxQkQsUUFBUSxJQUN6QixzQkFDQTt3RkFDTkksV0FDRUgscUJBQXFCRCxRQUFRLElBQ3pCLFFBQ0E7d0ZBQ05LLFlBQ0VKLHFCQUFxQkQsUUFBUSxJQUN6QixTQUNBO3dGQUNOLFdBQVc7NEZBQ1RGLGlCQUFpQjs0RkFDakJRLFlBQVk7d0ZBQ2Q7d0ZBQ0Esa0JBQWtCOzRGQUNoQlIsaUJBQWlCOzRGQUNqQlEsWUFBWTs0RkFDWnpILFlBQVk7NEZBQ1osV0FBVztnR0FDVGlILGlCQUFpQjs0RkFDbkI7d0ZBQ0Y7b0ZBQ0Y7O3NHQUVBLDhEQUFDUzs0RkFDQzFELE9BQU87Z0dBQ0w5RCxVQUFVO2dHQUNWeUgsYUFBYTs0RkFDZjtzR0FFQ1QsS0FBS3BKLElBQUk7Ozs7OztzR0FFWiw4REFBQ3BFLG1EQUFHQTs0RkFDRm1HLElBQUk7Z0dBQ0Y2RCxTQUFTO2dHQUNUa0UsZUFBZTtnR0FDZkMsTUFBTTs0RkFDUjs7OEdBRUEsOERBQUNIO29HQUNDMUQsT0FBTzt3R0FDTEwsWUFBWTt3R0FDWjNELFlBQVk7d0dBQ1pDLE9BQU87b0dBQ1Q7OEdBRUNpSCxLQUFLdEosS0FBSzs7Ozs7OzhHQUViLDhEQUFDOEo7b0dBQ0MxRCxPQUFPO3dHQUNMTCxZQUFZO3dHQUNaekQsVUFBVTt3R0FDVkQsT0FBTzt3R0FDUEcsV0FBVzt3R0FDWEosWUFBWTtvR0FDZDs4R0FFQ2tILEtBQUtySixRQUFROzs7Ozs7Ozs7Ozs7O21GQTFFYixHQUFpQnNKLE9BQWRELEtBQUt0SixLQUFLLEVBQUMsS0FBUyxPQUFOdUo7Ozs7OzRFQStFNUI7Ozs7Ozs7b0VBRUQzTCxNQUFNZSxpQkFBaUIsa0JBQ3RCLDhEQUFDckQsMkZBQWNBO3dFQUNiMkcsSUFBSTs0RUFBRThELFlBQVk7d0VBQW9CO2tGQUVyQ25JLE1BQU1lLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU9oQyw4REFBQzdELDZHQUFJQTt3REFBQ21OLE1BQU07NERBQUVDLElBQUk7d0RBQUc7a0VBQ25CLDRFQUFDdk0sc0ZBQVVBOzREQUNUd0csU0FBUTs0REFDUkYsSUFBSTtnRUFDRjhELFlBQVk7Z0VBQ1ozRCxZQUFZO2dFQUNaQyxPQUFPO2dFQUNQNEQsSUFBSTtnRUFDSkMsSUFBSTtnRUFDSjVELFVBQVU7NERBQ1o7c0VBQ0Q7Ozs7Ozs7Ozs7O2tFQU1ILDhEQUFDeEgsNkdBQUlBO3dEQUFDbU4sTUFBTTs0REFBRUMsSUFBSTs0REFBSUMsSUFBSTt3REFBRTs7MEVBQzFCLDhEQUFDeE0sc0ZBQVVBO2dFQUFDd0csU0FBUTtnRUFBUUYsSUFBSTJEOzBFQUFhOzs7Ozs7MEVBRzdDLDhEQUFDbEssc0ZBQVNBO2dFQUNSb0wsYUFBWTtnRUFDWjNFLFNBQVE7Z0VBQ1JoRSxJQUFHO2dFQUNId0MsTUFBSztnRUFDTHlILE1BQUs7Z0VBQ0x4SyxPQUFPeUssUUFBUXpLLE1BQU1pQixvQkFBb0I7Z0VBQ3pDeUosWUFBWTFLLE1BQU1pQixvQkFBb0I7Z0VBQ3RDa0ksVUFBVSxDQUNSckcsSUFJQUQsa0JBQ0VDO2dFQUdKVixPQUFPSCxTQUFTaEIsb0JBQW9CO2dFQUNwQ2dJLFNBQVM7Z0VBQ1RHLFlBQVk7b0VBQ1Z3Qiw0QkFDRSw4REFBQ3pOLDZHQUFjQTt3RUFBQ21NLFVBQVM7a0ZBQ3RCckgsU0FBU2hCLG9CQUFvQixJQUMzQmpCLENBQUFBLE1BQU1pQixvQkFBb0IsaUJBQ3pCLDhEQUFDN0Msa0VBQVNBOzRFQUFDcUcsT0FBTTs7Ozs7bUdBRWpCLDhEQUFDdEcsd0VBQWVBOzRFQUFDc0csT0FBTTs7Ozs7a0ZBQ3pCOzs7Ozs7b0VBR04rRCxPQUFPO3dFQUFFTCxZQUFZO29FQUFvQjtnRUFDM0M7Z0VBQ0E5RCxJQUFJO29FQUNGLHlCQUF5Qjt3RUFDdkI4RCxZQUFZO29FQUNkO29FQUNBLDZCQUE2Qjt3RUFDM0JBLFlBQVk7b0VBQ2Q7Z0VBQ0Y7Ozs7Ozs7Ozs7OztrRUFHSiw4REFBQ2pMLDZHQUFJQTt3REFBQ21OLE1BQU07NERBQUVDLElBQUk7NERBQUlDLElBQUk7d0RBQUU7OzBFQUMxQiw4REFBQ3hNLHNGQUFVQTtnRUFBQ3dHLFNBQVE7Z0VBQVFGLElBQUkyRDswRUFBYTs7Ozs7OzBFQUc3Qyw4REFBQ2xLLHNGQUFTQTtnRUFDUm9MLGFBQVk7Z0VBQ1ozRSxTQUFRO2dFQUNSaEUsSUFBRztnRUFDSHdDLE1BQUs7Z0VBQ0x5SCxNQUFLO2dFQUNMeEssT0FBT3lLLFFBQVF6SyxNQUFNbUIsbUJBQW1CO2dFQUN4Q3VKLFlBQVkxSyxNQUFNbUIsbUJBQW1CO2dFQUNyQ2dJLFVBQVUsQ0FDUnJHLElBSUFELGtCQUNFQztnRUFHSlYsT0FBT0gsU0FBU2QsbUJBQW1CO2dFQUNuQzhILFNBQVM7Z0VBQ1RHLFlBQVk7b0VBQ1Z3Qiw0QkFDRSw4REFBQ3pOLDZHQUFjQTt3RUFBQ21NLFVBQVM7a0ZBQ3RCckgsU0FBU2QsbUJBQW1CLElBQzFCbkIsQ0FBQUEsTUFBTW1CLG1CQUFtQixpQkFDeEIsOERBQUMvQyxrRUFBU0E7NEVBQUNxRyxPQUFNOzs7OzttR0FFakIsOERBQUN0Ryx3RUFBZUE7NEVBQUNzRyxPQUFNOzs7OztrRkFDekI7Ozs7OztvRUFHTitELE9BQU87d0VBQUVMLFlBQVk7b0VBQW9CO2dFQUMzQztnRUFDQTlELElBQUk7b0VBQ0YseUJBQXlCO3dFQUN2QjhELFlBQVk7b0VBQ2Q7b0VBQ0EsNkJBQTZCO3dFQUMzQkEsWUFBWTtvRUFDZDtnRUFDRjs7Ozs7Ozs7Ozs7O2tFQUtKLDhEQUFDakwsNkdBQUlBO3dEQUFDbU4sTUFBTTs0REFBRUMsSUFBSTt3REFBRzs7MEVBQ25CLDhEQUFDdk0sc0ZBQVVBO2dFQUFDd0csU0FBUTtnRUFBUUYsSUFBSTJEOzBFQUFhOzs7Ozs7MEVBRzdDLDhEQUFDbEssc0ZBQVNBO2dFQUNSb0wsYUFBWTtnRUFDWjNFLFNBQVE7Z0VBQ1JoRSxJQUFHO2dFQUNId0MsTUFBSztnRUFDTHlILE1BQUs7Z0VBQ0x4SyxPQUFPeUssUUFBUXpLLE1BQU1xQixlQUFlO2dFQUNwQ3FKLFlBQVkxSyxNQUFNcUIsZUFBZTtnRUFDakM0SCxTQUFTO2dFQUNURSxVQUFVLENBQ1JyRyxJQUlBRCxrQkFDRUM7Z0VBR0pWLE9BQU9ILFNBQVNaLGVBQWU7Z0VBQy9CK0gsWUFBWTtvRUFDVndCLDRCQUNFLDhEQUFDek4sNkdBQWNBO3dFQUFDbU0sVUFBUztrRkFDdEJySCxTQUFTWixlQUFlLElBQ3RCckIsQ0FBQUEsTUFBTXFCLGVBQWUsaUJBQ3BCLDhEQUFDakQsa0VBQVNBOzRFQUFDcUcsT0FBTTs7Ozs7bUdBRWpCLDhEQUFDdEcsd0VBQWVBOzRFQUFDc0csT0FBTTs7Ozs7a0ZBQ3pCOzs7Ozs7b0VBR04rRCxPQUFPO3dFQUFFTCxZQUFZO29FQUFvQjtnRUFDM0M7Z0VBQ0E5RCxJQUFJO29FQUNGLHlCQUF5Qjt3RUFDdkI4RCxZQUFZO29FQUNkO29FQUNBLDZCQUE2Qjt3RUFDM0JBLFlBQVk7b0VBQ2Q7Z0VBQ0Y7Ozs7Ozs7Ozs7OztrRUFLSiw4REFBQ2pMLDZHQUFJQTt3REFBQ21OLE1BQU07NERBQUVDLElBQUk7NERBQUlDLElBQUk7d0RBQUU7OzBFQUUxQiw4REFBQ3hNLHNGQUFVQTtnRUFBQ3dHLFNBQVE7Z0VBQVFGLElBQUkyRDswRUFBYTs7Ozs7OzBFQUc3Qyw4REFBQ2xLLHNGQUFTQTtnRUFDUm9MLGFBQVk7Z0VBQ1ozRSxTQUFRO2dFQUNSaEUsSUFBRztnRUFDSHdDLE1BQUs7Z0VBQ0x5SCxNQUFLO2dFQUNMeEssT0FBT3lLLFFBQVF6SyxNQUFNdUIsY0FBYztnRUFDbkNtSixZQUFZMUssTUFBTXVCLGNBQWM7Z0VBQ2hDMEgsU0FBUztnRUFDVEUsVUFBVSxDQUNSckcsSUFJQUQsa0JBQ0VDO2dFQUdKVixPQUFPSCxTQUFTVixjQUFjO2dFQUM5QjZILFlBQVk7b0VBQ1Z3Qiw0QkFDRSw4REFBQ3pOLDZHQUFjQTt3RUFBQ21NLFVBQVM7a0ZBQ3RCckgsU0FBU1YsY0FBYyxJQUNyQnZCLENBQUFBLE1BQU11QixjQUFjLGlCQUNuQiw4REFBQ25ELGtFQUFTQTs0RUFBQ3FHLE9BQU07Ozs7O21HQUVqQiw4REFBQ3RHLHdFQUFlQTs0RUFBQ3NHLE9BQU07Ozs7O2tGQUN6Qjs7Ozs7O2dFQUdSOzs7Ozs7Ozs7Ozs7a0VBR0osOERBQUN2SCw2R0FBSUE7d0RBQUNtTixNQUFNOzREQUFFQyxJQUFJOzREQUFJQyxJQUFJO3dEQUFFOzswRUFFMUIsOERBQUN4TSxzRkFBVUE7Z0VBQUN3RyxTQUFRO2dFQUFRRixJQUFJMkQ7MEVBQWE7Ozs7OzswRUFHN0MsOERBQUNsSyxzRkFBU0E7Z0VBQ1JvTCxhQUFZO2dFQUNaM0UsU0FBUTtnRUFDUmhFLElBQUc7Z0VBQ0h3QyxNQUFLO2dFQUNMeUgsTUFBSztnRUFDTDhCLFFBQVE7Z0VBQ1JyRCxTQUFTO2dFQUNUakosT0FBT3lLLFFBQVF6SyxNQUFNeUIsVUFBVTtnRUFDL0JpSixZQUFZMUssTUFBTXlCLFVBQVU7Z0VBQzVCMEgsVUFBVSxDQUNSckcsSUFJQUQsa0JBQ0VDO2dFQUdKVixPQUFPSCxTQUFTUixVQUFVO2dFQUMxQjJILFlBQVk7b0VBQ1Z3Qiw0QkFDRSw4REFBQ3pOLDZHQUFjQTt3RUFBQ21NLFVBQVM7a0ZBQ3RCckgsU0FBU1IsVUFBVSxJQUNqQnpCLENBQUFBLE1BQU15QixVQUFVLGlCQUNmLDhEQUFDckQsa0VBQVNBOzRFQUFDcUcsT0FBTTs7Ozs7bUdBRWpCLDhEQUFDdEcsd0VBQWVBOzRFQUFDc0csT0FBTTs7Ozs7a0ZBQ3pCOzs7Ozs7Z0VBR1I7Ozs7Ozs7Ozs7OztrRUFLSiw4REFBQ3ZILDZHQUFJQTt3REFBQ21OLE1BQU07NERBQUVDLElBQUk7NERBQUlDLElBQUk7d0RBQUU7OzBFQUUxQiw4REFBQ3hNLHNGQUFVQTtnRUFBQ3dHLFNBQVE7Z0VBQVFGLElBQUkyRDswRUFBYTs7Ozs7OzBFQUc3Qyw4REFBQ2xLLHNGQUFTQTtnRUFDUm9MLGFBQVk7Z0VBQ1ozRSxTQUFRO2dFQUNSaEUsSUFBRztnRUFDSHdDLE1BQUs7Z0VBQ0x5SCxNQUFLO2dFQUNMeEssT0FBT3lLLFFBQVF6SyxNQUFNMkIsZUFBZTtnRUFDcEMrSSxZQUFZMUssTUFBTTJCLGVBQWU7Z0VBQ2pDc0gsU0FBUztnRUFDVHFELFFBQVE7Z0VBQ1JuRCxVQUFVLENBQ1JyRyxJQUlBRCxrQkFDRUM7Z0VBR0pWLE9BQU9ILFNBQVNOLGVBQWU7Z0VBQy9CeUgsWUFBWTtvRUFDVndCLDRCQUNFLDhEQUFDek4sNkdBQWNBO3dFQUFDbU0sVUFBUztrRkFDdEJySCxTQUFTTixlQUFlLElBQ3RCM0IsQ0FBQUEsTUFBTTJCLGVBQWUsaUJBQ3BCLDhEQUFDdkQsa0VBQVNBOzRFQUFDcUcsT0FBTTs7Ozs7bUdBRWpCLDhEQUFDdEcsd0VBQWVBOzRFQUFDc0csT0FBTTs7Ozs7a0ZBQ3pCOzs7Ozs7Z0VBR1I7Ozs7Ozs7Ozs7OztrRUFHSiw4REFBQ3ZILDZHQUFJQTt3REFBQ21OLE1BQU07NERBQUVDLElBQUk7NERBQUlDLElBQUk7d0RBQUU7OzBFQUUxQiw4REFBQ3hNLHNGQUFVQTtnRUFBQ3dHLFNBQVE7Z0VBQVFGLElBQUkyRDswRUFBYTs7Ozs7OzBFQUc3Qyw4REFBQ3ZLLHdGQUFXQTtnRUFDVndMLFNBQVM7Z0VBQ1RqSixPQUFPeUssUUFBUXpLLE1BQU00QixlQUFlOztrRkFFcEMsOERBQUMvRCxtRkFBTUE7d0VBQ0xrRixNQUFLO3dFQUNMd0osU0FBUTt3RUFDUnRELFNBQVM7d0VBQ1Q3RyxPQUFPSCxTQUFTTCxlQUFlO3dFQUMvQnVILFVBQVUsQ0FBQ25EOzRFQUNULE1BQU13RyxpQkFBaUI7Z0ZBQ3JCeEosUUFBUTtvRkFDTkQsTUFBTTtvRkFDTlgsT0FBTzRELE1BQU1oRCxNQUFNLENBQUNaLEtBQUs7Z0ZBQzNCOzRFQUNGOzRFQUNBOEUsc0JBQXNCc0Y7d0VBQ3hCO3dFQUNBRixRQUFRO3dFQUNSRyxVQUFVNU07d0VBQ1ZnTCxZQUFZO3dFQUNaNkIsYUFBYSxDQUFDQzs0RUFDWixJQUFJLENBQUNBLFVBQVU7Z0ZBQ2IsT0FBTzs0RUFDVDs0RUFDQSxPQUFPQTt3RUFDVDt3RUFDQS9CLGNBQ0UzSSxTQUFTTCxlQUFlLElBQ3hCLENBQUM1QixNQUFNNEIsZUFBZSxpQkFDcEIsOERBQUN6RSw2R0FBY0E7NEVBQUNtTSxVQUFTO3NGQUN2Qiw0RUFBQ25MLHdFQUFlQTtnRkFBQ3NHLE9BQU07Ozs7Ozs7Ozs7cUZBRXZCO3dFQUVOcUcsV0FBVzs0RUFDVEMsWUFBWTtnRkFDVnZDLE9BQU87b0ZBQ0x3QyxXQUFXO2dGQUNiOzRFQUNGO3dFQUNGO3dFQUNBM0csSUFBSTs0RUFBRTRHLFVBQVU7d0VBQVE7a0ZBRXZCL0ksV0FBVzJFLEdBQUcsQ0FBQyxDQUFDcEcsMEJBQ2YsOERBQUM3QyxxRkFBUUE7Z0ZBQWlCd0UsT0FBTzNCOzBGQUM5QkE7K0VBRFlBOzs7Ozs7Ozs7O29FQUtsQlQsTUFBTTRCLGVBQWUsa0JBQ3BCLDhEQUFDbEUsMkZBQWNBO2tGQUNac0MsTUFBTTRCLGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFPOUIsOERBQUMxRSw2R0FBSUE7d0RBQUNtTixNQUFNOzREQUFFQyxJQUFJOzREQUFJQyxJQUFJO3dEQUFFOzswRUFFMUIsOERBQUN4TSxzRkFBVUE7Z0VBQUN3RyxTQUFRO2dFQUFRRixJQUFJMkQ7MEVBQWE7Ozs7OzswRUFHN0MsOERBQUN2Syx3RkFBV0E7Z0VBQ1Z3TCxTQUFTO2dFQUNUakosT0FBT3lLLFFBQVF6SyxNQUFNNkIsbUJBQW1COztrRkFFeEMsOERBQUNoRSxtRkFBTUE7d0VBQ0wwQyxJQUFHO3dFQUNId0MsTUFBSzt3RUFDTFgsT0FBT0gsU0FBU0osbUJBQW1CO3dFQUNuQ3NILFVBQVUsQ0FBQ25EOzRFQUNULE1BQU13RyxpQkFBaUI7Z0ZBQ3JCeEosUUFBUTtvRkFDTkQsTUFBTTtvRkFDTlgsT0FBTzRELE1BQU1oRCxNQUFNLENBQUNaLEtBQUs7Z0ZBQzNCOzRFQUNGOzRFQUNBZ0Ysb0JBQW9Cb0Y7d0VBQ3RCO3dFQUNBQyxVQUFVM007d0VBQ1YrSyxZQUFZO3dFQUNaNkIsYUFBYSxDQUFDQzs0RUFDWixJQUFJLENBQUNBLFVBQVU7Z0ZBQ2IsT0FBTzs0RUFDVDs0RUFDQSxPQUFPQTt3RUFDVDt3RUFDQS9CLGNBQ0UzSSxTQUFTSixtQkFBbUIsSUFDNUIsQ0FBQzdCLE1BQU02QixtQkFBbUIsaUJBQ3hCLDhEQUFDMUUsNkdBQWNBOzRFQUFDbU0sVUFBUztzRkFDdkIsNEVBQUNuTCx3RUFBZUE7Z0ZBQUNzRyxPQUFNOzs7Ozs7Ozs7O3FGQUV2QjtrRkFHTGxDLHFCQUFxQnNFLEdBQUcsQ0FBQyxDQUFDMEMsdUJBQ3pCLDhEQUFDM0wscUZBQVFBO2dGQUFjd0UsT0FBT21IOzBGQUMzQkE7K0VBRFlBOzs7Ozs7Ozs7O29FQUtsQnZKLE1BQU02QixtQkFBbUIsa0JBQ3hCLDhEQUFDbkUsMkZBQWNBO2tGQUNac0MsTUFBTTZCLG1CQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUtsQyw4REFBQzNFLDZHQUFJQTt3REFBQ21OLE1BQU07NERBQUVDLElBQUk7NERBQUlDLElBQUk7d0RBQUU7OzREQUN6QjswRUFFRCw4REFBQ3hNLHNGQUFVQTtnRUFBQ3dHLFNBQVE7Z0VBQVFGLElBQUkyRDswRUFBYTs7Ozs7OzBFQUc3Qyw4REFBQ2xLLHNGQUFTQTtnRUFDUnlDLElBQUc7Z0VBQ0gySSxhQUFZO2dFQUNabkcsTUFBSztnRUFDTFgsT0FBT0gsU0FBU0YsZUFBZTtnRUFDL0JvSCxVQUFVLENBQ1JyRyxJQUlBRCxrQkFDRUM7Z0VBR0o5QyxPQUFPeUssUUFBUXpLLE1BQU0rQixlQUFlO2dFQUNwQzJJLFlBQVkxSyxNQUFNK0IsZUFBZTtnRUFDakN1SyxRQUFRO2dFQUNSckQsU0FBUztnRUFDVHdELFVBQVUxTTtnRUFDVnFKLFlBQVk7b0VBQ1Z3Qiw0QkFDRSw4REFBQ3pOLDZHQUFjQTt3RUFBQ21NLFVBQVM7a0ZBQ3RCckgsU0FBU0YsZUFBZSxJQUN0Qi9CLENBQUFBLE1BQU0rQixlQUFlLGlCQUNwQiw4REFBQzNELGtFQUFTQTs0RUFBQ3FHLE9BQU07Ozs7O21HQUVqQiw4REFBQ3RHLHdFQUFlQTs0RUFBQ3NHLE9BQU07Ozs7O2tGQUN6Qjs7Ozs7O2dFQUdSOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLUiw4REFBQ3ZILDZHQUFJQTs0Q0FDSG1OLE1BQU07Z0RBQUVDLElBQUk7Z0RBQUlDLElBQUk7NENBQUU7NENBQ3RCL0IsT0FBTztnREFBRW9FLFFBQVE7Z0RBQVMxQixTQUFTOzRDQUFNO3NEQUV6Qyw0RUFBQ2hPLDZHQUFJQTtnREFBQ2lOLFNBQVM7Z0RBQUNDLFNBQVM7MERBQ3ZCLDRFQUFDbE4sNkdBQUlBO29EQUNIbU4sTUFBTTt3REFBRUMsSUFBSTt3REFBSUMsSUFBSTtvREFBRTtvREFDdEIvQixPQUFPO3dEQUNMTixTQUFTO3dEQUNURSxnQkFBZ0I7d0RBQ2hCeUQsS0FBSztvREFDUDs7c0VBRUEsOERBQUM1Tyw0R0FBTUE7NERBQ0x1TixNQUFLOzREQUNMakcsU0FBUTs0REFDUmtFLFNBQVMsQ0FBQ3pDLFFBQ1J0RCxpQkFBaUJzRCxPQUFPO3NFQUUzQjs7Ozs7O3NFQUdELDhEQUFDL0ksNEdBQU1BOzREQUNMdU4sTUFBSzs0REFDTGpHLFNBQVE7NERBQ1JzRSx5QkFBVyw4REFBQ3RLLHNFQUFhQTs7Ozs7NERBQ3pCOEYsSUFBSTtnRUFDRnFFLFNBQVM7Z0VBQ1RqRSxPQUFPO2dFQUNQLFdBQVc7b0VBQUVpRSxTQUFTO2dFQUFVO2dFQUNoQ21FLGVBQWU7Z0VBQ2Ysc0JBQXNCO29FQUNwQnBJLE9BQU87Z0VBQ1Q7NERBQ0Y7c0VBRUNoRixnQkFBZ0IsUUFBUSxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUF5Qi9EO0dBdGlETWpCO0tBQUFBO0FBd2lETiwrREFBZUEsa0JBQWtCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvKHBhZ2luYXMpL2FncmljdWx0b3IvcGFnZS50c3g/YzI2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgQWRkT3V0bGluZWRJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0FkZE91dGxpbmVkXCI7XHJcbmltcG9ydCB7IERhdGFHcmlkIGFzIE11aURhdGFHcmlkIH0gZnJvbSBcIkBtdWkveC1kYXRhLWdyaWRcIjtcclxuaW1wb3J0IHtcclxuICBCdXR0b24sXHJcbiAgR3JpZCxcclxuICBJbnB1dEFkb3JubWVudCxcclxuICBQYXBlcixcclxuICBTZWxlY3RDaGFuZ2VFdmVudCxcclxufSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBEaWFsb2cgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBEaWFsb2dDb250ZW50IH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgRGlhbG9nVGl0bGUgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBEaWFsb2dDb250ZW50VGV4dCB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IEZvcm1Db250cm9sIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgRm9ybUhlbHBlclRleHQgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5cclxuaW1wb3J0IHsgSWNvbkJ1dHRvbiB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IElucHV0TGFiZWwgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBNZW51SXRlbSB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IFNlbGVjdCB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IFRleHRGaWVsZCB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IFR5cG9ncmFwaHkgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgRGF0YXRhYmxlIGZyb20gXCIuLi8uLi9jb21wb25lbnRzL3RhYmxlL0RhdGFUYWJsZVwiO1xyXG5pbXBvcnQgQ2xvc2VJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0Nsb3NlXCI7XHJcbmltcG9ydCB7IEJveCB9IGZyb20gXCJAbXVpL3N5c3RlbVwiO1xyXG5pbXBvcnQgQ2hlY2tDaXJjbGVJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0NoZWNrQ2lyY2xlXCI7XHJcbmltcG9ydCBFcnJvckljb24gZnJvbSBcIkBtdWkvaWNvbnMtbWF0ZXJpYWwvRXJyb3JcIjtcclxuaW1wb3J0IHsgU2VhcmNoIGFzIFNlYXJjaEljb24gfSBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XHJcbmltcG9ydCBBZGRDaXJjbGVJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0FkZENpcmNsZVwiO1xyXG5cclxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xyXG5cclxuaW50ZXJmYWNlIEFncmljdWx0b3JHYW5hZGVyb1Byb3BzIHtcclxuICBvblNlYXJjaENoYW5nZT86IChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHZvaWQ7XHJcbiAgb25BZGRDbGljaz86ICgpID0+IHZvaWQ7XHJcbn1cclxuXHJcbmludGVyZmFjZSBDbGllbnQge1xyXG4gIGlkOiBudW1iZXI7XHJcbiAgcmF6b25Tb2NpYWw6IHN0cmluZztcclxuICB0aXBvQ2xpZW50ZT86IHN0cmluZztcclxuICBub21icmVDb250YWN0bz86IHN0cmluZztcclxuICBjYXJnb0NvbnRhY3RvPzogc3RyaW5nO1xyXG4gIGRpcmVjY2lvbjogc3RyaW5nO1xyXG4gIHRlbGVmb25vOiBzdHJpbmc7XHJcbiAgbWFpbDogc3RyaW5nO1xyXG4gIGx1Z2FyOiBzdHJpbmc7XHJcbiAgcHJvdmluY2lhPzogc3RyaW5nO1xyXG4gIGNvbmRGcmVudGVJdmE6IHN0cmluZztcclxuICBkb2N1bWVudG86IHN0cmluZztcclxufVxyXG5cclxuY29uc3QgQWdyaWN1bHRvckdhbmFkZXJvOiBSZWFjdC5GQzxBZ3JpY3VsdG9yR2FuYWRlcm9Qcm9wcz4gPSAoKSA9PiB7XHJcbiAgY29uc3QgW2lzU3VibWl0dGluZywgc2V0SXNTdWJtaXR0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc2VsZWN0ZWRSb3csIHNldFNlbGVjdGVkUm93XSA9IHVzZVN0YXRlPENsaWVudCB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtvcGVuLCBzZXRPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbcm93cywgc2V0Um93c10gPSB1c2VTdGF0ZTxDbGllbnRbXT4oW10pO1xyXG4gIGNvbnN0IFtpbnB1dFZhbHVlLCBzZXRJbnB1dFZhbHVlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtpc1NlYXJjaEJhck9wZW4sIHNldElzU2VhcmNoQmFyT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2ZpbHRlcmVkUm93cywgc2V0RmlsdGVyZWRSb3dzXSA9IHVzZVN0YXRlPENsaWVudFtdPihbXSk7XHJcbiAgY29uc3QgRGF0YUdyaWQgPSBNdWlEYXRhR3JpZDtcclxuICBjb25zdCBbcGVyc29uSWQsIHNldFBlcnNvbklkXSA9IHVzZVN0YXRlPHN0cmluZz4oXCJcIik7XHJcbiAgY29uc3QgW2VzdGFkb01vZGFsLCBzZXRFc3RhZG9Nb2RhbF0gPSB1c2VTdGF0ZTxcImFkZFwiIHwgXCJ1cGRhdGVcIj4oXCJhZGRcIik7XHJcbiAgY29uc3QgW3NlbGVjdGVkQ2xpZW50SWQsIHNldFNlbGVjdGVkQ2xpZW50SWRdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3Qgc2VsZWN0UHJvdmluY2lhUmVmID0gdXNlUmVmPEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudD4obnVsbCk7XHJcbiAgY29uc3Qgc2VsZWN0Q29uZEl2YVJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFNlbGVjdEVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IGRvY3VtZW50b1JlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFNlbGVjdEVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8eyBba2V5OiBzdHJpbmddOiBzdHJpbmcgfT4oe30pO1xyXG5cclxuICBjb25zdCBoYW5kbGVDbGllbnRTZWxlY3QgPSAoY2xpZW50SWQ6IG51bWJlcik6IHZvaWQgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRDbGllbnRJZChjbGllbnRJZCk7XHJcbiAgICBjb25zdCBzZWxlY3RlZENsaWVudCA9IChyb3dzIGFzIENsaWVudFtdKS5maW5kKFxyXG4gICAgICAocm93KSA9PiByb3cuaWQgPT09IGNsaWVudElkXHJcbiAgICApO1xyXG4gICAgaWYgKHNlbGVjdGVkQ2xpZW50KSB7XHJcbiAgICAgIC8vIFNwbGl0IHRoZSBsdWdhciBmaWVsZCBpbnRvIGxvY2FsaWRhZCBhbmQgcHJvdmluY2lhXHJcbiAgICAgIGNvbnN0IFtsb2NhbGlkYWQsIHByb3ZpbmNpYV0gPSBzZWxlY3RlZENsaWVudC5sdWdhci5zcGxpdChcIiAtIFwiKTtcclxuXHJcbiAgICAgIHNldEZvcm1EYXRhKHtcclxuICAgICAgICBwZXJzb25SYXpvblNvY2lhbDogc2VsZWN0ZWRDbGllbnQucmF6b25Tb2NpYWwsXHJcbiAgICAgICAgcGVyc29uVGlwb0NsaWVudGU6IHNlbGVjdGVkQ2xpZW50LnRpcG9DbGllbnRlIHx8IFwiXCIsXHJcbiAgICAgICAgcGVyc29uTm9tYnJlQ29udGFjdG86IHNlbGVjdGVkQ2xpZW50Lm5vbWJyZUNvbnRhY3RvIHx8IFwiXCIsXHJcbiAgICAgICAgcGVyc29uQ2FyZ29Db250YWN0bzogc2VsZWN0ZWRDbGllbnQuY2FyZ29Db250YWN0byB8fCBcIlwiLFxyXG4gICAgICAgIHBlcnNvbkRvbWljaWxpbzogc2VsZWN0ZWRDbGllbnQuZGlyZWNjaW9uLFxyXG4gICAgICAgIHBlcnNvblRlbGVmb25vOiBzZWxlY3RlZENsaWVudC50ZWxlZm9ubyxcclxuICAgICAgICBwZXJzb25NYWlsOiBzZWxlY3RlZENsaWVudC5tYWlsLFxyXG4gICAgICAgIHBlcnNvbkxvY2FsaWRhZDogbG9jYWxpZGFkLFxyXG4gICAgICAgIHBlcnNvblByb3ZpbmNpYTogcHJvdmluY2lhLFxyXG4gICAgICAgIHBlcnNvbkNvbmRGcmVudGVJdmE6IHNlbGVjdGVkQ2xpZW50LmNvbmRGcmVudGVJdmEsXHJcbiAgICAgICAgcGVyc29uRG9jdW1lbnRvOiBzZWxlY3RlZENsaWVudC5kb2N1bWVudG8sXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xyXG4gICAgLy9wZXJzb25JZDogXCJcIixcclxuICAgIHBlcnNvblJhem9uU29jaWFsOiBcIlwiLFxyXG4gICAgcGVyc29uVGlwb0NsaWVudGU6IFwiXCIsXHJcbiAgICBwZXJzb25Ob21icmVDb250YWN0bzogXCJcIixcclxuICAgIHBlcnNvbkNhcmdvQ29udGFjdG86IFwiXCIsXHJcbiAgICBwZXJzb25Eb21pY2lsaW86IFwiXCIsXHJcbiAgICBwZXJzb25UZWxlZm9ubzogXCJcIixcclxuICAgIHBlcnNvbk1haWw6IFwiXCIsXHJcbiAgICBwZXJzb25Mb2NhbGlkYWQ6IFwiXCIsXHJcbiAgICBwZXJzb25Qcm92aW5jaWE6IFwiXCIsXHJcbiAgICBwZXJzb25Db25kRnJlbnRlSXZhOiBcIlwiLFxyXG4gICAgcGVyc29uRG9jdW1lbnRvOiBcIlwiLFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBwcm92aW5jaWFzID0gW1xyXG4gICAgXCJCdWVub3MgQWlyZXNcIixcclxuICAgIFwiQ2F0YW1hcmNhXCIsXHJcbiAgICBcIkNoYWNvXCIsXHJcbiAgICBcIkNodWJ1dFwiLFxyXG4gICAgXCJDw7NyZG9iYVwiLFxyXG4gICAgXCJDb3JyaWVudGVzXCIsXHJcbiAgICBcIkVudHJlIFLDrW9zXCIsXHJcbiAgICBcIkZvcm1vc2FcIixcclxuICAgIFwiSnVqdXlcIixcclxuICAgIFwiTGEgUGFtcGFcIixcclxuICAgIFwiTGEgUmlvamFcIixcclxuICAgIFwiTWVuZG96YVwiLFxyXG4gICAgXCJNaXNpb25lc1wiLFxyXG4gICAgXCJOZXVxdcOpblwiLFxyXG4gICAgXCJSw61vIE5lZ3JvXCIsXHJcbiAgICBcIlNhbHRhXCIsXHJcbiAgICBcIlNhbiBKdWFuXCIsXHJcbiAgICBcIlNhbiBMdWlzXCIsXHJcbiAgICBcIlNhbnRhIENydXpcIixcclxuICAgIFwiU2FudGEgRmVcIixcclxuICAgIFwiU2FudGlhZ28gZGVsIEVzdGVyb1wiLFxyXG4gICAgXCJUaWVycmEgZGVsIEZ1ZWdvXCIsXHJcbiAgICBcIlR1Y3Vtw6FuXCIsXHJcbiAgXTtcclxuXHJcbiAgY29uc3QgdGlwb0NsaWVudGVPcHRpb25zID0gW1xyXG4gICAgLy8gUHJvZHVjdG9yZXNcclxuICAgIHsgdmFsdWU6IFwiUHJvZHVjdG9yKGNvbWVyY2lhbClcIiwgY2F0ZWdvcnk6IFwiUHJvZHVjdG9yZXNcIiwgaWNvbjogXCLwn5Go4oCN8J+SvFwiIH0sXHJcbiAgICB7IHZhbHVlOiBcIlByb2R1Y3RvcihmYW1pbGlhcilcIiwgY2F0ZWdvcnk6IFwiUHJvZHVjdG9yZXNcIiwgaWNvbjogXCLwn5Go4oCN8J+RqeKAjfCfkafigI3wn5GmXCIgfSxcclxuICAgIHsgdmFsdWU6IFwiRXN0YW5jaWFcIiwgY2F0ZWdvcnk6IFwiUHJvZHVjdG9yZXNcIiwgaWNvbjogXCLwn4+e77iPXCIgfSxcclxuXHJcbiAgICAvLyBFbXByZXNhcyB5IE9yZ2FuaXphY2lvbmVzXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIkVtcHJlc2EgKHBlcnNvbmEganVyw61kaWNhLCBwLiBlai4gU0EgLyBTUkwpXCIsXHJcbiAgICAgIGNhdGVnb3J5OiBcIkVtcHJlc2FzXCIsXHJcbiAgICAgIGljb246IFwi8J+PolwiLFxyXG4gICAgfSxcclxuICAgIHsgdmFsdWU6IFwiQ29vcGVyYXRpdmFcIiwgY2F0ZWdvcnk6IFwiRW1wcmVzYXNcIiwgaWNvbjogXCLwn4+Y77iPXCIgfSxcclxuICAgIHtcclxuICAgICAgdmFsdWU6IFwiQXNvY2lhY2nDs24vQ29uc29yY2lvL0VudGlkYWQgR3JlbWlhbFwiLFxyXG4gICAgICBjYXRlZ29yeTogXCJFbXByZXNhc1wiLFxyXG4gICAgICBpY29uOiBcIvCfpJ1cIixcclxuICAgIH0sXHJcblxyXG4gICAgLy8gU2VydmljaW9zIHkgQ29udHJhdGlzdGFzXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIkNvbnRyYXRpc3RhKHAuIGVqLiBvdHJvIHF1ZSBjb250cmF0YSBlcXVpcG8pXCIsXHJcbiAgICAgIGNhdGVnb3J5OiBcIlNlcnZpY2lvc1wiLFxyXG4gICAgICBpY29uOiBcIvCfmpxcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIkFjb3Bpby9JbmR1c3RyaWEvRXhwb3J0YWRvcihzaWxvcywgcGxhbnRhcywgY29tcHJhZG9yYXMpXCIsXHJcbiAgICAgIGNhdGVnb3J5OiBcIlNlcnZpY2lvc1wiLFxyXG4gICAgICBpY29uOiBcIvCfj61cIixcclxuICAgIH0sXHJcblxyXG4gICAgLy8gU2VjdG9yIFDDumJsaWNvIHkgT3Ryb3NcclxuICAgIHtcclxuICAgICAgdmFsdWU6IFwiTXVuaWNpcGFsaWRhZC9Fc3RhdGFsL0d1YmVybmFtZW50YWxcIixcclxuICAgICAgY2F0ZWdvcnk6IFwiUMO6YmxpY29cIixcclxuICAgICAgaWNvbjogXCLvv71cIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIlBhcnRpY3VsYXIocGVxdWXDsW9zIGNsaWVudGVzIGRvbcOpc3RpY29zKVwiLFxyXG4gICAgICBjYXRlZ29yeTogXCJPdHJvc1wiLFxyXG4gICAgICBpY29uOiBcIvCfkaRcIixcclxuICAgIH0sXHJcbiAgICB7IHZhbHVlOiBcIk90cm8ocGFyYSBjYXNvcyBubyBwcmV2aXN0b3MpXCIsIGNhdGVnb3J5OiBcIk90cm9zXCIsIGljb246IFwi4p2TXCIgfSxcclxuICAgIHsgdmFsdWU6IFwiTm8gRXNwZWNpZmljYWRvXCIsIGNhdGVnb3J5OiBcIk90cm9zXCIsIGljb246IFwi4p6WXCIgfSxcclxuICBdO1xyXG5cclxuICBjb25zdCBjb25kRnJlbnRlSXZhT3B0aW9ucyA9IFtcclxuICAgIFwiSVZBIFJlc3BvbnNhYmxlIEluc2NyaXB0b1wiLFxyXG4gICAgXCJJVkEgUmVzcG9uc2FibGUgbm8gSW5zY3JpcHRvXCIsXHJcbiAgICBcIklWQSBubyBSZXNwb25zYWJsZVwiLFxyXG4gICAgXCJJVkEgU3VqZXRvIEV4ZW50b1wiLFxyXG4gICAgXCJDb25zdW1pZG9yIEZpbmFsXCIsXHJcbiAgICBcIlJlc3BvbnNhYmxlIE1vbm90cmlidXRvXCIsXHJcbiAgICBcIlN1amV0byBubyBDYXRlZ29yaXphZG9cIixcclxuICAgIFwiUHJvdmVlZG9yIGRlbCBFeHRlcmlvclwiLFxyXG4gICAgXCJDbGllbnRlIGRlbCBFeHRlcmlvclwiLFxyXG4gICAgXCJJVkEgTGliZXJhZG9cIixcclxuICAgIFwiUGVxdWXDsW8gQ29udHJpYnV5ZW50ZSBTb2NpYWxcIixcclxuICAgIFwiTW9ub3RyaWJ1dGlzdGEgU29jaWFsXCIsXHJcbiAgICBcIlBlcXVlw7FvIENvbnRyaWJ1eWVudGUgRXZlbnR1YWxcIixcclxuICBdO1xyXG5cclxuICBjb25zdCBoYW5kbGVPcGVuQWRkID0gKCkgPT4ge1xyXG4gICAgc2V0RXN0YWRvTW9kYWwoXCJhZGRcIik7XHJcbiAgICBjbGVhckZyb20oKTtcclxuICAgIHNldE9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY2xlYXJGcm9tID0gKCkgPT4ge1xyXG4gICAgc2V0Rm9ybURhdGEoe1xyXG4gICAgICBwZXJzb25SYXpvblNvY2lhbDogXCJcIixcclxuICAgICAgcGVyc29uVGlwb0NsaWVudGU6IFwiXCIsXHJcbiAgICAgIHBlcnNvbk5vbWJyZUNvbnRhY3RvOiBcIlwiLFxyXG4gICAgICBwZXJzb25DYXJnb0NvbnRhY3RvOiBcIlwiLFxyXG4gICAgICBwZXJzb25Eb21pY2lsaW86IFwiXCIsXHJcbiAgICAgIHBlcnNvblRlbGVmb25vOiBcIlwiLFxyXG4gICAgICBwZXJzb25NYWlsOiBcIlwiLFxyXG4gICAgICBwZXJzb25Mb2NhbGlkYWQ6IFwiXCIsXHJcbiAgICAgIHBlcnNvblByb3ZpbmNpYTogXCJcIixcclxuICAgICAgcGVyc29uQ29uZEZyZW50ZUl2YTogXCJcIixcclxuICAgICAgcGVyc29uRG9jdW1lbnRvOiBcIlwiLFxyXG4gICAgfSk7XHJcbiAgICBzZXRFcnJvcih7fSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2xpY2tDbG9zZSA9IChcclxuICAgIF9ldmVudDogUmVhY3QuTW91c2VFdmVudDxIVE1MRWxlbWVudD4sXHJcbiAgICByZWFzb24/OiBzdHJpbmdcclxuICApID0+IHtcclxuICAgIGlmIChyZWFzb24gJiYgcmVhc29uID09PSBcImJhY2tkcm9wQ2xpY2tcIikgcmV0dXJuO1xyXG4gICAgc2V0T3BlbihmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoXHJcbiAgICBlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFNlbGVjdEVsZW1lbnQ+XHJcbiAgKSA9PiB7XHJcbiAgICBjb25zdCB7IG5hbWUsIHZhbHVlIH0gPSBlLnRhcmdldDtcclxuXHJcbiAgICBpZiAoXHJcbiAgICAgIG5hbWUgPT09IFwicGVyc29uUmF6b25Tb2NpYWxcIiB8fFxyXG4gICAgICBuYW1lID09PSBcInBlcnNvbkxvY2FsaWRhZFwiIHx8XHJcbiAgICAgIG5hbWUgPT09IFwicGVyc29uTm9tYnJlQ29udGFjdG9cIiB8fFxyXG4gICAgICBuYW1lID09PSBcInBlcnNvbkNhcmdvQ29udGFjdG9cIlxyXG4gICAgKSB7XHJcbiAgICAgIGlmICghL15bYS16QS1aw4Atw79cXHNdKiQvLnRlc3QodmFsdWUpKSB7XHJcbiAgICAgICAgc2V0RXJyb3IoKHByZXZFcnJvcikgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICAgIFtuYW1lXTogXCJTb2xvIHNlIHBlcm1pdGVuIGxldHJhcyB5IGVzcGFjaW9zXCIsXHJcbiAgICAgICAgfSkpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRFcnJvcigocHJldkVycm9yKSA9PiAoe1xyXG4gICAgICAgICAgLi4ucHJldkVycm9yLFxyXG4gICAgICAgICAgW25hbWVdOiBcIlwiLFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGlmIChuYW1lID09PSBcInBlcnNvbkRvbWljaWxpb1wiKSB7XHJcbiAgICAgIGlmICghL15bYS16QS1aw4Atw78wLTlcXHMuXSokLy50ZXN0KHZhbHVlKSkge1xyXG4gICAgICAgIHNldEVycm9yKChwcmV2RXJyb3IpID0+ICh7XHJcbiAgICAgICAgICAuLi5wcmV2RXJyb3IsXHJcbiAgICAgICAgICBwZXJzb25Eb21pY2lsaW86XHJcbiAgICAgICAgICAgIFwiU29sbyBzZSBwZXJtaXRlbiBsZXRyYXMsIG7Dum1lcm9zLCBlc3BhY2lvcyB5IHB1bnRvc1wiLFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0RXJyb3IoKHByZXZFcnJvcikgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICAgIHBlcnNvbkRvbWljaWxpbzogXCJcIixcclxuICAgICAgICB9KSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAobmFtZSA9PT0gXCJwZXJzb25UZWxlZm9ub1wiKSB7XHJcbiAgICAgIC8vIEVsaW1pbmFyIHRvZG8gbG8gcXVlIG5vIHNlYSBuw7ptZXJvXHJcbiAgICAgIGNvbnN0IGNsZWFuZWQgPSB2YWx1ZS5yZXBsYWNlKC9cXEQvZywgXCJcIik7XHJcblxyXG4gICAgICAvLyBMaW1pdGFyIGEgMTAgZMOtZ2l0b3NcclxuICAgICAgaWYgKGNsZWFuZWQubGVuZ3RoID4gMTApIHJldHVybjtcclxuXHJcbiAgICAgIGxldCBmb3JtYXR0ZWQ7XHJcbiAgICAgIGlmIChjbGVhbmVkLmxlbmd0aCA8PSA0KSB7XHJcbiAgICAgICAgZm9ybWF0dGVkID0gY2xlYW5lZDtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBmb3JtYXR0ZWQgPSBgJHtjbGVhbmVkLnNsaWNlKDAsIDQpfS0ke2NsZWFuZWQuc2xpY2UoNCl9YDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVmFsaWRhciBlbCBmb3JtYXRvIGNvbXBsZXRvXHJcbiAgICAgIGNvbnN0IGlzVmFsaWRGb3JtYXQgPSAvXlxcZHs0fS1cXGR7Nn0kLy50ZXN0KGZvcm1hdHRlZCk7XHJcblxyXG4gICAgICBzZXRFcnJvcigocHJldkVycm9yKSA9PiAoe1xyXG4gICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICBwZXJzb25UZWxlZm9ubzpcclxuICAgICAgICAgIGZvcm1hdHRlZC5sZW5ndGggPT09IDExICYmICFpc1ZhbGlkRm9ybWF0XHJcbiAgICAgICAgICAgID8gXCJGb3JtYXRvIGludsOhbGlkby4gRGViZSBzZXIgMDAwMC0wMDAwMDBcIlxyXG4gICAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgIH0pKTtcclxuXHJcbiAgICAgIHNldEZvcm1EYXRhKChwcmV2U3RhdGUpID0+ICh7XHJcbiAgICAgICAgLi4ucHJldlN0YXRlLFxyXG4gICAgICAgIFtuYW1lXTogZm9ybWF0dGVkLFxyXG4gICAgICB9KSk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBpZiAobmFtZSA9PT0gXCJwZXJzb25NYWlsXCIpIHtcclxuICAgICAgLy8gRXhwcmVzacOzbiByZWd1bGFyIHBhcmEgdmFsaWRhciBlbWFpbFxyXG4gICAgICBjb25zdCBlbWFpbFJlZ2V4ID0gL15bXFx3LV0rKFxcLltcXHctXSspKkAoW1xcdy1dK1xcLikrW2EtekEtWl17Miw3fSQvO1xyXG5cclxuICAgICAgLy8gU2kgZWwgY2FtcG8gbm8gZXN0w6EgdmFjw61vLCB2YWxpZGFyIGVsIGZvcm1hdG9cclxuICAgICAgaWYgKHZhbHVlICYmICFlbWFpbFJlZ2V4LnRlc3QodmFsdWUpKSB7XHJcbiAgICAgICAgc2V0RXJyb3IoKHByZXZFcnJvcikgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICAgIHBlcnNvbk1haWw6IFwiRm9ybWF0byBkZSBlbWFpbCBpbnbDoWxpZG8uIEVqZW1wbG86IHVzdWFyaW9AZG9taW5pby5jb21cIixcclxuICAgICAgICB9KSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0RXJyb3IoKHByZXZFcnJvcikgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXZFcnJvcixcclxuICAgICAgICAgIHBlcnNvbk1haWw6IFwiXCIsXHJcbiAgICAgICAgfSkpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKG5hbWUgPT09IFwicGVyc29uRG9jdW1lbnRvXCIpIHtcclxuICAgICAgLy8gRWxpbWluYXIgdG9kbyBsbyBxdWUgbm8gc2VhIG7Dum1lcm9cclxuICAgICAgY29uc3QgY2xlYW5lZCA9IHZhbHVlLnJlcGxhY2UoL1xcRC9nLCBcIlwiKTtcclxuXHJcbiAgICAgIC8vIExpbWl0YXIgYSAxMSBkw61naXRvcyBlbiB0b3RhbFxyXG4gICAgICBpZiAoY2xlYW5lZC5sZW5ndGggPiAxMSkgcmV0dXJuO1xyXG5cclxuICAgICAgbGV0IGZvcm1hdHRlZDtcclxuICAgICAgaWYgKGNsZWFuZWQubGVuZ3RoIDw9IDIpIHtcclxuICAgICAgICBmb3JtYXR0ZWQgPSBjbGVhbmVkO1xyXG4gICAgICB9IGVsc2UgaWYgKGNsZWFuZWQubGVuZ3RoIDw9IDEwKSB7XHJcbiAgICAgICAgZm9ybWF0dGVkID0gYCR7Y2xlYW5lZC5zbGljZSgwLCAyKX0tJHtjbGVhbmVkLnNsaWNlKDIpfWA7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgZm9ybWF0dGVkID0gYCR7Y2xlYW5lZC5zbGljZSgwLCAyKX0tJHtjbGVhbmVkLnNsaWNlKFxyXG4gICAgICAgICAgMixcclxuICAgICAgICAgIDEwXHJcbiAgICAgICAgKX0tJHtjbGVhbmVkLnNsaWNlKDEwLCAxMSl9YDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVmFsaWRhciBlbCBmb3JtYXRvIGNvbXBsZXRvXHJcbiAgICAgIGNvbnN0IGlzVmFsaWRGb3JtYXQgPSAvXlxcZHsyfS1cXGR7OH0tXFxkezF9JC8udGVzdChmb3JtYXR0ZWQpO1xyXG5cclxuICAgICAgc2V0RXJyb3IoKHByZXZFcnJvcikgPT4gKHtcclxuICAgICAgICAuLi5wcmV2RXJyb3IsXHJcbiAgICAgICAgcGVyc29uRG9jdW1lbnRvOlxyXG4gICAgICAgICAgZm9ybWF0dGVkLmxlbmd0aCA9PT0gMTIgJiYgIWlzVmFsaWRGb3JtYXRcclxuICAgICAgICAgICAgPyBcIkZvcm1hdG8gaW52w6FsaWRvLiBEZWJlIHNlciAwMC0wMDAwMDAwMC0wXCJcclxuICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICB9KSk7XHJcblxyXG4gICAgICBzZXRGb3JtRGF0YSgocHJldlN0YXRlKSA9PiAoe1xyXG4gICAgICAgIC4uLnByZXZTdGF0ZSxcclxuICAgICAgICBbbmFtZV06IGZvcm1hdHRlZCxcclxuICAgICAgfSkpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgc2V0Rm9ybURhdGEoKHByZXZTdGF0ZSkgPT4gKHtcclxuICAgICAgLi4ucHJldlN0YXRlLFxyXG4gICAgICBbbmFtZV06IHZhbHVlLFxyXG4gICAgfSkpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChlOiBSZWFjdC5Gb3JtRXZlbnQ8SFRNTEZvcm1FbGVtZW50PikgPT4ge1xyXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNlYXJjaENsaWNrID0gKCkgPT4ge1xyXG4gICAgc2V0SXNTZWFyY2hCYXJPcGVuKCFpc1NlYXJjaEJhck9wZW4pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGNvbHVtbnMgPSBbXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcImVtcHJlc2FcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJFbXByZXNhXCIsXHJcbiAgICAgIHdpZHRoOiAyODAsXHJcbiAgICAgIGhlYWRlckNsYXNzTmFtZTogXCJjdXN0b20taGVhZGVyXCIsXHJcbiAgICAgIHJlbmRlckNlbGw6IChwYXJhbXM6IGFueSkgPT4gKFxyXG4gICAgICAgIDxCb3ggc3g9e3sgcHk6IDEgfX0+XHJcbiAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICB2YXJpYW50PVwiYm9keTJcIlxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IFwiNjAwXCIsXHJcbiAgICAgICAgICAgICAgY29sb3I6IFwiIzMzM1wiLFxyXG4gICAgICAgICAgICAgIGZvbnRTaXplOiBcIjAuODc1cmVtXCIsXHJcbiAgICAgICAgICAgICAgbGluZUhlaWdodDogMS4yLFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7cGFyYW1zLnJvdy5yYXpvblNvY2lhbH1cclxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgIHtwYXJhbXMucm93LnRpcG9DbGllbnRlICYmIChcclxuICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICB2YXJpYW50PVwiY2FwdGlvblwiXHJcbiAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgIGNvbG9yOiBcIiM2NjZcIixcclxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjAuNzVyZW1cIixcclxuICAgICAgICAgICAgICAgIGZvbnRTdHlsZTogXCJpdGFsaWNcIixcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge3BhcmFtcy5yb3cudGlwb0NsaWVudGV9XHJcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9Cb3g+XHJcbiAgICAgICksXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJjb250YWN0b1wiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkNvbnRhY3RvXCIsXHJcbiAgICAgIHdpZHRoOiAyMjAsXHJcbiAgICAgIGhlYWRlckNsYXNzTmFtZTogXCJjdXN0b20taGVhZGVyXCIsXHJcbiAgICAgIHJlbmRlckNlbGw6IChwYXJhbXM6IGFueSkgPT4gKFxyXG4gICAgICAgIDxCb3ggc3g9e3sgcHk6IDEgfX0+XHJcbiAgICAgICAgICB7cGFyYW1zLnJvdy5ub21icmVDb250YWN0byA/IChcclxuICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImJvZHkyXCJcclxuICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IFwiNjAwXCIsXHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiMzMzNcIixcclxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMC44NzVyZW1cIixcclxuICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogMS4yLFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7cGFyYW1zLnJvdy5ub21icmVDb250YWN0b31cclxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAge3BhcmFtcy5yb3cuY2FyZ29Db250YWN0byAmJiAoXHJcbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY2FwdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzY2NlwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjAuNzVyZW1cIixcclxuICAgICAgICAgICAgICAgICAgICBmb250U3R5bGU6IFwiaXRhbGljXCIsXHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHtwYXJhbXMucm93LmNhcmdvQ29udGFjdG99XHJcbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJib2R5MlwiXHJcbiAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgIGNvbG9yOiBcIiM5OTlcIixcclxuICAgICAgICAgICAgICAgIGZvbnRTdHlsZTogXCJpdGFsaWNcIixcclxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjAuODc1cmVtXCIsXHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIFNpbiBjb250YWN0b1xyXG4gICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvQm94PlxyXG4gICAgICApLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwidGVsZWZvbm9cIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJUZWzDqWZvbm9cIixcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImN1c3RvbS1oZWFkZXJcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcIm1haWxcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJFbWFpbFwiLFxyXG4gICAgICB3aWR0aDogMTgwLFxyXG4gICAgICBoZWFkZXJDbGFzc05hbWU6IFwiY3VzdG9tLWhlYWRlclwiLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwibHVnYXJcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJVYmljYWNpw7NuXCIsXHJcbiAgICAgIHdpZHRoOiAyMDAsXHJcbiAgICAgIGhlYWRlckNsYXNzTmFtZTogXCJjdXN0b20taGVhZGVyXCIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJkb2N1bWVudG9cIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJEb2N1bWVudG9cIixcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiBcImN1c3RvbS1oZWFkZXJcIixcclxuICAgIH0sXHJcbiAgXTtcclxuXHJcbiAgLypBR1JFR0FSIEFHUklDVUxUT1IvR0FOQURFUk8qL1xyXG4gIGNvbnN0IGhhbmRsZUFkZENsaWVudGUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcIkluaWNpYW5kbyBlbnbDrW8uLi5cIik7XHJcbiAgICBzZXRJc1N1Ym1pdHRpbmcodHJ1ZSk7XHJcbiAgICBjb25zdCBsdWdhciA9IGAke2Zvcm1EYXRhLnBlcnNvbkxvY2FsaWRhZH0gLSAke2Zvcm1EYXRhLnBlcnNvblByb3ZpbmNpYX1gO1xyXG4gICAgY29uc3QgbmV3UGVyc29uID0ge1xyXG4gICAgICByYXpvblNvY2lhbDogZm9ybURhdGEucGVyc29uUmF6b25Tb2NpYWwsXHJcbiAgICAgIHRpcG9DbGllbnRlOiBmb3JtRGF0YS5wZXJzb25UaXBvQ2xpZW50ZSxcclxuICAgICAgbm9tYnJlQ29udGFjdG86IGZvcm1EYXRhLnBlcnNvbk5vbWJyZUNvbnRhY3RvLFxyXG4gICAgICBjYXJnb0NvbnRhY3RvOiBmb3JtRGF0YS5wZXJzb25DYXJnb0NvbnRhY3RvLFxyXG4gICAgICBkaXJlY2Npb246IGZvcm1EYXRhLnBlcnNvbkRvbWljaWxpbyxcclxuICAgICAgdGVsZWZvbm86IGZvcm1EYXRhLnBlcnNvblRlbGVmb25vLFxyXG4gICAgICBtYWlsOiBmb3JtRGF0YS5wZXJzb25NYWlsLFxyXG4gICAgICBsdWdhcjogbHVnYXIsXHJcbiAgICAgIHByb3ZpbmNpYTogZm9ybURhdGEucGVyc29uUHJvdmluY2lhLFxyXG4gICAgICBjb25kRnJlbnRlSXZhOiBmb3JtRGF0YS5wZXJzb25Db25kRnJlbnRlSXZhLFxyXG4gICAgICBkb2N1bWVudG86IGZvcm1EYXRhLnBlcnNvbkRvY3VtZW50byxcclxuICAgIH07XHJcblxyXG4gICAgLy8gTGxhbWFyIGEgbGEgZnVuY2nDs24gZGUgdmFsaWRhY2nDs25cclxuICAgIGNvbnN0IGVycm9ycyA9IGZvcm1EYXRhO1xyXG4gICAgY29uc29sZS5sb2coZXJyb3JzKTtcclxuICAgIGlmIChlcnJvcnMpIHtcclxuICAgICAgc2V0RXJyb3IoZXJyb3JzKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE1vc3RyYXIgY2FkYSBkYXRvIGluZGl2aWR1YWwgZW4gbGEgY29uc29sYVxyXG4gICAgY29uc29sZS5sb2coXCJSYXrDs24gU29jaWFsOlwiLCBuZXdQZXJzb24ucmF6b25Tb2NpYWwpO1xyXG4gICAgY29uc29sZS5sb2coXCJEaXJlY2Npw7NuOlwiLCBuZXdQZXJzb24uZGlyZWNjaW9uKTtcclxuICAgIGNvbnNvbGUubG9nKFwiVGVsw6lmb25vOlwiLCBuZXdQZXJzb24udGVsZWZvbm8pO1xyXG4gICAgY29uc29sZS5sb2coXCJNYWlsOlwiLCBuZXdQZXJzb24ubWFpbCk7XHJcbiAgICBjb25zb2xlLmxvZyhcIkx1Z2FyOlwiLCBsdWdhcik7XHJcbiAgICBjb25zb2xlLmxvZyhcIkNvbmRpY2nDs24gRnJlbnRlIElWQTpcIiwgbmV3UGVyc29uLmNvbmRGcmVudGVJdmEpO1xyXG4gICAgY29uc29sZS5sb2coXCJEb2N1bWVudG86XCIsIG5ld1BlcnNvbi5kb2N1bWVudG8pO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDo4MDgwL2FwaS9hZ3JpY3VsdG9yXCIsIHtcclxuICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkobmV3UGVyc29uKSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBWZXJpZmljYXIgc2kgbGEgc29saWNpdHVkIGZ1ZSBleGl0b3NhXHJcbiAgICAgIGlmICghcmVzLm9rKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRXJyb3IgYWwgZ3VhcmRhciBlbCBjbGllbnRlXCIpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjbGVhckZyb20oKTtcclxuICAgICAgY29uc3QgZGF0YUNsaWVudGVzID0gYXdhaXQgZmV0Y2hDbGllbnRlcygpO1xyXG4gICAgICBzZXRSb3dzKGRhdGFDbGllbnRlcyk7XHJcbiAgICAgIHNldE9wZW4oZmFsc2UpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGVuIGxhIHNvbGljaXR1ZDpcIiwgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8qQ0FSR0FSIEFHUklDVUxUT1IvR0FOQURFUk8gRU4gRUwgREFUQUdSSUQqL1xyXG4gIGNvbnN0IGZldGNoQ2xpZW50ZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9hcGkvYWdyaWN1bHRvclwiKTtcclxuICAgICAgaWYgKCFyZXMub2spIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJFcnJvciBhbCBvYnRlbmVyIGxvcyBjbGllbnRlc1wiKTtcclxuICAgICAgfVxyXG4gICAgICBjb25zdCBkYXRhQ2xpZW50ZXMgPSBhd2FpdCByZXMuanNvbigpO1xyXG4gICAgICByZXR1cm4gZGF0YUNsaWVudGVzO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGVuIGxhIHNvbGljaXR1ZDpcIiwgZXJyb3IpO1xyXG4gICAgICAvLyBEZXZvbHZlciB1biB2YWxvciBwcmVkZXRlcm1pbmFkbyBlbiBjYXNvIGRlIGVycm9yXHJcbiAgICAgIHJldHVybiBbXTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZ2V0RGF0YSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgZGF0YUNsaWVudGVzID0gYXdhaXQgZmV0Y2hDbGllbnRlcygpO1xyXG4gICAgICBzZXRSb3dzKGRhdGFDbGllbnRlcyk7XHJcbiAgICB9O1xyXG5cclxuICAgIGdldERhdGEoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8qQlVTQ0FSIEFHUklDVUxUT1IvR0FOQURFUk8qL1xyXG4gIGNvbnN0IGhhbmRsZVNlYXJoQ2xpZW50ZSA9IChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IHNlYXJjaFZhbHVlID0gZXZlbnQudGFyZ2V0LnZhbHVlO1xyXG4gICAgc2V0U2VhcmNoVGVybShzZWFyY2hWYWx1ZSk7XHJcblxyXG4gICAgY29uc3QgZmlsdGVyZWREYXRhID0gcm93cy5maWx0ZXIoKHJvdzogQ2xpZW50KSA9PiB7XHJcbiAgICAgIHJldHVybiAoXHJcbiAgICAgICAgcm93LnJhem9uU29jaWFsLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICByb3cuZGlyZWNjaW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICByb3cudGVsZWZvbm8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hWYWx1ZS50b0xvd2VyQ2FzZSgpKSB8fFxyXG4gICAgICAgIHJvdy5tYWlsLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICByb3cubHVnYXIudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hWYWx1ZS50b0xvd2VyQ2FzZSgpKSB8fFxyXG4gICAgICAgIHJvdy5jb25kRnJlbnRlSXZhLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICByb3cuZG9jdW1lbnRvLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVmFsdWUudG9Mb3dlckNhc2UoKSlcclxuICAgICAgKTtcclxuICAgIH0pO1xyXG4gICAgc2V0RmlsdGVyZWRSb3dzKGZpbHRlcmVkRGF0YSk7XHJcbiAgfTtcclxuXHJcbiAgLypFTElNSU5BUiBBR1JJQ1VMVE9SL0dBTkFERVJPKi9cclxuICBjb25zdCBoYW5kbGVEZWxldGVDbGllbnRlID0gYXN5bmMgKGlkOiBudW1iZXIpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFwiQ2xpZW50ZSBhIGVsaW1pbmFyOlwiLCBpZCk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9hcGkvYWdyaWN1bHRvci8ke2lkfWAsIHtcclxuICAgICAgICBtZXRob2Q6IFwiREVMRVRFXCIsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlcy5vaykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiQ2xpZW50ZSBlbGltaW5hZG8gZXhpdG9zYW1lbnRlLlwiKTtcclxuICAgICAgICAvLyBBY3R1YWxpemFyIGVsIGVzdGFkbyBkZSBsYXMgZmlsYXMgZGVzcHXDqXMgZGUgZWxpbWluYXIgdW4gY2xpZW50ZVxyXG4gICAgICAgIGNvbnN0IGRhdGFDbGllbnRlcyA9IGF3YWl0IGZldGNoQ2xpZW50ZXMoKTtcclxuICAgICAgICBzZXRSb3dzKGRhdGFDbGllbnRlcyk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGFsIGVsaW1pbmFyIGVsIGNsaWVudGU6XCIsIGlkKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGVuIGxhIHNvbGljaXR1ZCBkZSBlbGltaW5hY2nDs246XCIsIGVycm9yKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvKkNMSUNLIEJPVE9OIE1PRElGSUNBUihMQVBJWikqL1xyXG4gIGNvbnN0IGhhbmRsZUVkaXQgPSBhc3luYyAoaWQ6IGFueSkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9hcGkvYWdyaWN1bHRvci8ke2lkfWAsIHtcclxuICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlcy5vaykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiQ2xpZW50ZSBvYnRlbmlkbyBleGl0b3NhbWVudGUuXCIpO1xyXG5cclxuICAgICAgICBjb25zdCBhZ3JpY3VsdG9yID0gYXdhaXQgcmVzLmpzb24oKTtcclxuICAgICAgICBzZXRGb3JtRGF0YSh7XHJcbiAgICAgICAgICBwZXJzb25SYXpvblNvY2lhbDogYWdyaWN1bHRvci5yYXpvblNvY2lhbCxcclxuICAgICAgICAgIHBlcnNvblRpcG9DbGllbnRlOiBhZ3JpY3VsdG9yLnRpcG9DbGllbnRlIHx8IFwiXCIsXHJcbiAgICAgICAgICBwZXJzb25Ob21icmVDb250YWN0bzogYWdyaWN1bHRvci5ub21icmVDb250YWN0byB8fCBcIlwiLFxyXG4gICAgICAgICAgcGVyc29uQ2FyZ29Db250YWN0bzogYWdyaWN1bHRvci5jYXJnb0NvbnRhY3RvIHx8IFwiXCIsXHJcbiAgICAgICAgICBwZXJzb25Eb21pY2lsaW86IGFncmljdWx0b3IuZGlyZWNjaW9uLFxyXG4gICAgICAgICAgcGVyc29uVGVsZWZvbm86IGFncmljdWx0b3IudGVsZWZvbm8sXHJcbiAgICAgICAgICBwZXJzb25NYWlsOiBhZ3JpY3VsdG9yLm1haWwsXHJcbiAgICAgICAgICBwZXJzb25Mb2NhbGlkYWQ6IGFncmljdWx0b3IubHVnYXIuc3BsaXQoXCIgLSBcIilbMF0udHJpbSgpLCAvLyBDb3JyZWdpZG9cclxuICAgICAgICAgIHBlcnNvblByb3ZpbmNpYTogYWdyaWN1bHRvci5sdWdhci5zcGxpdChcIiAtIFwiKVsxXS50cmltKCksIC8vIENvcnJlZ2lkb1xyXG4gICAgICAgICAgcGVyc29uQ29uZEZyZW50ZUl2YTogYWdyaWN1bHRvci5jb25kRnJlbnRlSXZhLFxyXG4gICAgICAgICAgcGVyc29uRG9jdW1lbnRvOiBhZ3JpY3VsdG9yLmRvY3VtZW50byxcclxuICAgICAgICB9KTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgYWwgbW9kaWZpY2FyIGVsIGNsaWVudGU6XCIsIGlkKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGVuIGxhIHNvbGljaXR1ZCBkZSBlbGltaW5hY2nDs246XCIsIGVycm9yKTtcclxuICAgIH1cclxuXHJcbiAgICBzZXRFc3RhZG9Nb2RhbChcInVwZGF0ZVwiKTtcclxuICAgIHNldE9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgLypNT0RJRklDQVIgQUdSSUNVTFRPUi9HQU5BREVSTyBQQVJBIEdBVVJEQVIqL1xyXG4gIGNvbnN0IGhhbmRsZVVwZGF0ZUNsaWVudGUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIXNlbGVjdGVkUm93KSByZXR1cm47XHJcbiAgICBjb25zdCBsdWdhciA9IGAke2Zvcm1EYXRhLnBlcnNvbkxvY2FsaWRhZH0gLSAke2Zvcm1EYXRhLnBlcnNvblByb3ZpbmNpYX1gO1xyXG4gICAgY29uc3QgbmV3UGVyc29uOiBDbGllbnQgPSB7XHJcbiAgICAgIGlkOiBzZWxlY3RlZFJvdy5pZCxcclxuICAgICAgcmF6b25Tb2NpYWw6IGZvcm1EYXRhLnBlcnNvblJhem9uU29jaWFsLFxyXG4gICAgICB0aXBvQ2xpZW50ZTogZm9ybURhdGEucGVyc29uVGlwb0NsaWVudGUsXHJcbiAgICAgIG5vbWJyZUNvbnRhY3RvOiBmb3JtRGF0YS5wZXJzb25Ob21icmVDb250YWN0byxcclxuICAgICAgY2FyZ29Db250YWN0bzogZm9ybURhdGEucGVyc29uQ2FyZ29Db250YWN0byxcclxuICAgICAgZGlyZWNjaW9uOiBmb3JtRGF0YS5wZXJzb25Eb21pY2lsaW8sXHJcbiAgICAgIHRlbGVmb25vOiBmb3JtRGF0YS5wZXJzb25UZWxlZm9ubyxcclxuICAgICAgbWFpbDogZm9ybURhdGEucGVyc29uTWFpbCxcclxuICAgICAgbHVnYXI6IGx1Z2FyLFxyXG4gICAgICBwcm92aW5jaWE6IGZvcm1EYXRhLnBlcnNvblByb3ZpbmNpYSxcclxuICAgICAgY29uZEZyZW50ZUl2YTogZm9ybURhdGEucGVyc29uQ29uZEZyZW50ZUl2YSxcclxuICAgICAgZG9jdW1lbnRvOiBmb3JtRGF0YS5wZXJzb25Eb2N1bWVudG8sXHJcbiAgICB9O1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjgwODAvYXBpL2FncmljdWx0b3JgLCB7XHJcbiAgICAgICAgbWV0aG9kOiBcIlBVVFwiLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkobmV3UGVyc29uKSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlcy5vaykge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkVycm9yIGFsIGd1YXJkYXIgZWwgY2xpZW50ZVwiKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVXBkYXRlIHJvd3Mgd2l0aCBwcm9wZXIgdHlwaW5nXHJcbiAgICAgIGNvbnN0IHVwZGF0ZWRSb3dzID0gcm93cy5tYXAoKHJvdzogQ2xpZW50KSA9PiB7XHJcbiAgICAgICAgaWYgKHJvdy5pZCA9PT0gbmV3UGVyc29uLmlkKSB7XHJcbiAgICAgICAgICByZXR1cm4gbmV3UGVyc29uO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gcm93O1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHNldFJvd3ModXBkYXRlZFJvd3MpO1xyXG4gICAgICBjbGVhckZyb20oKTtcclxuICAgICAgc2V0T3BlbihmYWxzZSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZW4gbGEgc29saWNpdHVkOlwiLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlTG9jYWxpZGFkS2V5RG93biA9IChcclxuICAgIGV2ZW50OiBSZWFjdC5LZXlib2FyZEV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+XHJcbiAgKSA9PiB7XHJcbiAgICBpZiAoZXZlbnQua2V5ID09PSBcIkVudGVyXCIgJiYgc2VsZWN0UHJvdmluY2lhUmVmLmN1cnJlbnQpIHtcclxuICAgICAgc2VsZWN0UHJvdmluY2lhUmVmLmN1cnJlbnQuZm9jdXMoKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVQcm92aW5jaWFDaGFuZ2UgPSAoXHJcbiAgICBldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudCB8IEhUTUxTZWxlY3RFbGVtZW50PlxyXG4gICkgPT4ge1xyXG4gICAgaGFuZGxlSW5wdXRDaGFuZ2UoZXZlbnQpO1xyXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIGlmIChzZWxlY3RDb25kSXZhUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBzZWxlY3RDb25kSXZhUmVmLmN1cnJlbnQuZm9jdXMoKTtcclxuICAgICAgfVxyXG4gICAgfSwgMCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ29uZEl2YUNoYW5nZSA9IChcclxuICAgIGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFNlbGVjdEVsZW1lbnQ+XHJcbiAgKSA9PiB7XHJcbiAgICBoYW5kbGVJbnB1dENoYW5nZShldmVudCk7XHJcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgaWYgKGRvY3VtZW50b1JlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgZG9jdW1lbnRvUmVmLmN1cnJlbnQuZm9jdXMoKTtcclxuICAgICAgfVxyXG4gICAgfSwgMCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VsZWN0QWdyaWN1bHRvciA9IChpZDogbnVtYmVyKSA9PiB7XHJcbiAgICBjb25zdCBzZWxlY3RlZEFncmljdWx0b3IgPSByb3dzLmZpbmQoKHJvdykgPT4gcm93LmlkID09PSBpZCk7XHJcblxyXG4gICAgaWYgKHNlbGVjdGVkQWdyaWN1bHRvcikge1xyXG4gICAgICBjb25zdCBhZ3JpY3VsdG9yID0ge1xyXG4gICAgICAgIGlkOiBzZWxlY3RlZEFncmljdWx0b3IuaWQsXHJcbiAgICAgICAgcmF6b25Tb2NpYWw6IHNlbGVjdGVkQWdyaWN1bHRvci5yYXpvblNvY2lhbCxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIC8vIEd1YXJkYXIgZWwgYWdyaWN1bHRvciBzZWxlY2Npb25hZG9cclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJzZWxlY3RlZEFncmljdWx0b3JcIiwgSlNPTi5zdHJpbmdpZnkoYWdyaWN1bHRvcikpO1xyXG5cclxuICAgICAgLy8gUmVkaXJpZ2lyIGRlIHZ1ZWx0YSBhIGxhIHDDoWdpbmEgZGUgZXN0YWJsZWNpbWllbnRvXHJcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gXCIvZXN0YWJsZWNpbWllbnRvXCI7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3BhZ2UsIHNldFBhZ2VdID0gdXNlU3RhdGUoMCk7XHJcbiAgY29uc3QgaGFuZGxlU2VhcmNoQ2hhbmdlID0gKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xyXG4gICAgc2V0U2VhcmNoVGVybShldmVudC50YXJnZXQudmFsdWUpO1xyXG4gICAgc2V0UGFnZSgwKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBsYWJlbFN0eWxlcyA9IHtcclxuICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgIGNvbG9yOiBcIiMzMzNcIixcclxuICAgIG1hcmdpbkJvdHRvbTogXCI4cHhcIixcclxuICAgIGRpc3BsYXk6IFwiYmxvY2tcIixcclxuICAgIGZvbnRGYW1pbHk6IFwiTGV4ZW5kLCBzYW5zLXNlcmlmXCIsIC8vIENhbWJpYWRvIGEgTGV4ZW5kXHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxCb3hcclxuICAgICAgICBzeD17e1xyXG4gICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJzcGFjZS1iZXR3ZWVuXCIsXHJcbiAgICAgICAgICBtYjogMyxcclxuICAgICAgICAgIG10OiAzLFxyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgdmFyaWFudD1cImg0XCJcclxuICAgICAgICAgICAgY29tcG9uZW50PVwiZGl2XCJcclxuICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICBmb250V2VpZ2h0OiBcImJvbGRcIixcclxuICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIkxleGVuZCwgc2Fucy1zZXJpZlwiLCAvLyBDYW1iaWFkbyBhIExleGVuZFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICBBZ3JpY3VsdG9yZXNcclxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgIHZhcmlhbnQ9XCJzdWJ0aXRsZTFcIlxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGNvbG9yOiBcInRleHQuc2Vjb25kYXJ5XCIsXHJcbiAgICAgICAgICAgICAgbXQ6IDEsXHJcbiAgICAgICAgICAgICAgZm9udEZhbWlseTogYCR7aW50ZXIuc3R5bGUuZm9udEZhbWlseX1gLCAvLyBDYW1iaWFkbyBhIEludGVyXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIEdlc3Rpb25lIGxhIGluZm9ybWFjacOzbiBkZSBzdXMgQWdyaWN1bHRvcmVzXHJcbiAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgdmFyaWFudD1cImNvbnRhaW5lZFwiXHJcbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVPcGVuQWRkfVxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgYmdjb2xvcjogXCIjMkU3RDMyXCIsIC8vIENvbG9yIHZlcmRlIG3DoXMgb3NjdXJvXHJcbiAgICAgICAgICAgIGNvbG9yOiBcIiNmZmZmZmZcIixcclxuICAgICAgICAgICAgXCImOmhvdmVyXCI6IHsgYmdjb2xvcjogXCIjMEQ5QTBBXCIgfSxcclxuICAgICAgICAgICAgaGVpZ2h0OiBcImZpdC1jb250ZW50XCIsXHJcbiAgICAgICAgICAgIGFsaWduU2VsZjogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgZm9udEZhbWlseTogYCR7aW50ZXIuc3R5bGUuZm9udEZhbWlseX1gLCAvLyBBZ3JlZ2FkbyBwYXJhIHVzYXIgSW50ZXJcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBzdGFydEljb249ezxBZGRPdXRsaW5lZEljb24gLz59XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgTnVldm8gQWdyaWN1bHRvclxyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICA8L0JveD5cclxuXHJcbiAgICAgIDxQYXBlciBlbGV2YXRpb249ezJ9IHN4PXt7IHA6IDIsIG1iOiAzLCBib3JkZXJSYWRpdXM6IDIgfX0+XHJcbiAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgcGxhY2Vob2xkZXI9XCJCdXNjYXIuLi5cIlxyXG4gICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XHJcbiAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlU2VhcmhDbGllbnRlfVxyXG4gICAgICAgICAgSW5wdXRQcm9wcz17e1xyXG4gICAgICAgICAgICBzdGFydEFkb3JubWVudDogKFxyXG4gICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cInN0YXJ0XCI+XHJcbiAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTZWFyY2hDbGlja30+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWFyY2hJY29uIC8+XHJcbiAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBzeD17eyBtYjogMiB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPERhdGF0YWJsZVxyXG4gICAgICAgICAgY29sdW1ucz17Y29sdW1uc31cclxuICAgICAgICAgIHJvd3M9e2ZpbHRlcmVkUm93cy5sZW5ndGggPiAwID8gZmlsdGVyZWRSb3dzIDogcm93c31cclxuICAgICAgICAgIG9wdGlvbj17dHJ1ZX1cclxuICAgICAgICAgIG9wdGlvbkRlbGV0ZUZ1bmN0aW9uPXtoYW5kbGVEZWxldGVDbGllbnRlfVxyXG4gICAgICAgICAgb3B0aW9uVXBkYXRlRnVuY3Rpb249e2hhbmRsZUVkaXR9XHJcbiAgICAgICAgICBzZXRTZWxlY3RlZFJvdz17KHJvdykgPT4gc2V0U2VsZWN0ZWRSb3cocm93IGFzIENsaWVudCB8IG51bGwpfVxyXG4gICAgICAgICAgc2VsZWN0ZWRSb3c9e3NlbGVjdGVkUm93fVxyXG4gICAgICAgICAgb3B0aW9uU2VsZWN0PXtoYW5kbGVTZWxlY3RBZ3JpY3VsdG9yfVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvUGFwZXI+XHJcblxyXG4gICAgICA8RGlhbG9nXHJcbiAgICAgICAgb3Blbj17b3Blbn1cclxuICAgICAgICBvbkNsb3NlPXtoYW5kbGVDbGlja0Nsb3NlfVxyXG4gICAgICAgIG1heFdpZHRoPVwibGdcIlxyXG4gICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgIHN4PXt7XHJcbiAgICAgICAgICBcIiYgLk11aURpYWxvZy1wYXBlclwiOiB7XHJcbiAgICAgICAgICAgIHdpZHRoOiBcIjExMDBweFwiLCAvLyBBbmNobyBhbXBsaWFkbyBkZSA4MjVweCBhIDExMDBweFxyXG4gICAgICAgICAgICBtYXhXaWR0aDogXCI5NXZ3XCIsIC8vIE1lam9yIGFwcm92ZWNoYW1pZW50byBkZWwgdmlld3BvcnRcclxuICAgICAgICAgICAgbWluSGVpZ2h0OiBcIjYwMHB4XCIsIC8vIEFsdHVyYSBtw61uaW1hIHBhcmEgbWVqb3IgcHJvcG9yY2nDs25cclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfX1cclxuICAgICAgPlxyXG4gICAgICAgIDxCb3ggc3g9e3sgcDogNCB9fT5cclxuICAgICAgICAgIHsvKiBQYWRkaW5nIGFtcGxpYWRvIHBhcmEgbWVqb3IgZXNwYWNpYWRvICovfVxyXG4gICAgICAgICAgPEJveCBzeD17eyBtYjogMiB9fT5cclxuICAgICAgICAgICAgPERpYWxvZ1RpdGxlXHJcbiAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgIHA6IDAsXHJcbiAgICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIkxleGVuZCwgc2Fucy1zZXJpZlwiLFxyXG4gICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMS41cmVtXCIsXHJcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiBcImJvbGRcIixcclxuICAgICAgICAgICAgICAgIGNvbG9yOiBcIiMzMzNcIixcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgUmVnaXN0cmFyIG51ZXZvIGFncmljdWx0b3IvZ2FuYWRlcm9cclxuICAgICAgICAgICAgPC9EaWFsb2dUaXRsZT5cclxuICAgICAgICAgICAgPERpYWxvZ0NvbnRlbnRUZXh0XHJcbiAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgIHA6IDAsXHJcbiAgICAgICAgICAgICAgICBtdDogMSxcclxuICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiSW50ZXIsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgIGNvbG9yOiBcIiM2NjZcIixcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQ29tcGxldGUgbGEgaW5mb3JtYWNpw7NuIGRlbCBudWV2byBhZ3JpY3VsdG9yL2dhbmFkZXJvIGEgcmVnaXN0cmFyLlxyXG4gICAgICAgICAgICA8L0RpYWxvZ0NvbnRlbnRUZXh0PlxyXG4gICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICA8SWNvbkJ1dHRvblxyXG4gICAgICAgICAgICBhcmlhLWxhYmVsPVwiY2xvc2VcIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoZXZlbnQpID0+IGhhbmRsZUNsaWNrQ2xvc2UoZXZlbnQsIFwiY2xvc2VCdXR0b25DbGlja1wiKX1cclxuICAgICAgICAgICAgc3g9e3sgcG9zaXRpb246IFwiYWJzb2x1dGVcIiwgcmlnaHQ6IDgsIHRvcDogOCB9fVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8Q2xvc2VJY29uIC8+XHJcbiAgICAgICAgICA8L0ljb25CdXR0b24+XHJcbiAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgIGNvbXBvbmVudD1cImZvcm1cIlxyXG4gICAgICAgICAgICBvblN1Ym1pdD17aGFuZGxlU3VibWl0fVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPERpYWxvZ0NvbnRlbnQgc3g9e3sgcDogMCB9fT5cclxuICAgICAgICAgICAgICB7LyogUXVpdGFtb3MgZWwgcGFkZGluZyBkZWwgRGlhbG9nQ29udGVudCAqL31cclxuXHJcbiAgICAgICAgICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezN9PlxyXG4gICAgICAgICAgICAgICAgey8qIEVzcGFjaWFkbyBhbXBsaWFkbyBlbnRyZSBlbGVtZW50b3MgKi99XHJcbiAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiB9fT5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezN9PlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBTZWNjacOzbjogSW5mb3JtYWNpw7NuIGRlIGxhIEVtcHJlc2EvRW50aWRhZCAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJoNlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJMZXhlbmQsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiBcIjYwMFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiMzMzNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtYjogMyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtdDogMSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxLjJyZW1cIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgSW5mb3JtYWNpw7NuIGRlIGxhIEVtcHJlc2EvRW50aWRhZFxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIFByaW1lcmEgZmlsYTogUmF6w7NuIFNvY2lhbCB5IFRpcG8gZGUgQ2xpZW50ZSAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBSYXrDs24gU29jaWFsXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWo6IEVzdGFuY2lhIExhIEVzcGVyYW56YSBTLkEuXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJyYXpvblNvY2lhbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwZXJzb25SYXpvblNvY2lhbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3I9e0Jvb2xlYW4oZXJyb3IucGVyc29uUmF6b25Tb2NpYWwpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWxwZXJUZXh0PXtlcnJvci5wZXJzb25SYXpvblNvY2lhbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlOiBSZWFjdC5DaGFuZ2VFdmVudDxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MVGV4dEFyZWFFbGVtZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSW5wdXRDaGFuZ2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlIGFzIFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZXJzb25SYXpvblNvY2lhbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2VzdGFkb01vZGFsID09PSBcInVwZGF0ZVwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgSW5wdXRQcm9wcz17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGVuZEFkb3JubWVudDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0QWRvcm5tZW50IHBvc2l0aW9uPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS5wZXJzb25SYXpvblNvY2lhbCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlcnJvci5wZXJzb25SYXpvblNvY2lhbCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFcnJvckljb24gY29sb3I9XCJlcnJvclwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY29sb3I9XCJzdWNjZXNzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5wdXRBZG9ybm1lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZTogeyBmb250RmFtaWx5OiBcIkludGVyLCBzYW5zLXNlcmlmXCIgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aUlucHV0QmFzZS1pbnB1dFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIkludGVyLCBzYW5zLXNlcmlmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aUZvcm1IZWxwZXJUZXh0LXJvb3RcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBUaXBvIGRlIENsaWVudGVcclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxGb3JtQ29udHJvbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3I9e0Jvb2xlYW4oZXJyb3IucGVyc29uVGlwb0NsaWVudGUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ0aXBvQ2xpZW50ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInBlcnNvblRpcG9DbGllbnRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGVyc29uVGlwb0NsaWVudGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSW5wdXRDaGFuZ2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUgYXMgUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheUVtcHR5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgTWVudVByb3BzPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBQYXBlclByb3BzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4SGVpZ2h0OiA0MDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IFwiYXV0b1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiBcIjMwMHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCImIC5NdWlNZW51SXRlbS1yb290XCI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiTGV4ZW5kLCBzYW5zLXNlcmlmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIwLjg3NXJlbVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogNjAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogMS41LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogXCIxMnB4IDE2cHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdoaXRlU3BhY2U6IFwibm9ybWFsXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3b3JkV3JhcDogXCJicmVhay13b3JkXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW5IZWlnaHQ6IFwiYXV0b1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzMzM1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tOiBcIjFweCBzb2xpZCAjZjBmMGYwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIiY6bGFzdC1jaGlsZFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlckJvdHRvbTogXCJub25lXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiTGV4ZW5kLCBzYW5zLXNlcmlmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IFwiNTZweFwiLCAvLyBBbHR1cmEgaWd1YWwgYWwgVGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aVNlbGVjdC1zZWxlY3RcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIkxleGVuZCwgc2Fucy1zZXJpZlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIwLjg3NXJlbVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA2MDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEuNCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogXCIxNi41cHggMTRweFwiLCAvLyBQYWRkaW5nIGlndWFsIGFsIFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjMzMzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogXCIxLjQzNzVlbVwiLCAvLyBBbHR1cmEgaW50ZXJuYSBpZ3VhbCBhbCBUZXh0RmllbGRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgXCImIC5NdWlPdXRsaW5lZElucHV0LW5vdGNoZWRPdXRsaW5lXCI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6IFwicmdiYSgwLCAwLCAwLCAwLjIzKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiJjpob3ZlciAuTXVpT3V0bGluZWRJbnB1dC1ub3RjaGVkT3V0bGluZVwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBcInJnYmEoMCwgMCwgMCwgMC44NylcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYuTXVpLWZvY3VzZWQgLk11aU91dGxpbmVkSW5wdXQtbm90Y2hlZE91dGxpbmVcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogXCIjMTk3NmQyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiBcIjJweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT1cIlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJMZXhlbmQsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFN0eWxlOiBcIml0YWxpY1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjOTk5XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjAuODc1cmVtXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiNmYWZhZmFcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgU2VsZWNjaW9uZSB1biB0aXBvIGRlIGNsaWVudGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0aXBvQ2xpZW50ZU9wdGlvbnMubWFwKCh0aXBvLCBpbmRleCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNGaXJzdEluQ2F0ZWdvcnkgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA9PT0gMCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXBvQ2xpZW50ZU9wdGlvbnNbaW5kZXggLSAxXS5jYXRlZ29yeSAhPT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXBvLmNhdGVnb3J5O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17YCR7dGlwby52YWx1ZX0tJHtpbmRleH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0aXBvLnZhbHVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIkxleGVuZCwgc2Fucy1zZXJpZlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMC44NzVyZW1cIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEuNSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IFwiMTJweCAxNnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aGl0ZVNwYWNlOiBcIm5vcm1hbFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd29yZFdyYXA6IFwiYnJlYWstd29yZFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWluSGVpZ2h0OiBcImF1dG9cIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogXCI4cHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiMzMzNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclRvcDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNGaXJzdEluQ2F0ZWdvcnkgJiYgaW5kZXggPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIjFweCBzb2xpZCAjZTBlMGUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwibm9uZVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luVG9wOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0ZpcnN0SW5DYXRlZ29yeSAmJiBpbmRleCA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiNHB4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiMFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZ1RvcDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNGaXJzdEluQ2F0ZWdvcnkgJiYgaW5kZXggPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIjE2cHhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIxMnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIiY6aG92ZXJcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwiI2Y4ZjlmYVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJMZWZ0OiBcIjNweCBzb2xpZCAjMjE5NmYzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCImLk11aS1zZWxlY3RlZFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjZTNmMmZkXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlckxlZnQ6IFwiM3B4IHNvbGlkICMxOTc2ZDJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogNjAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIiY6aG92ZXJcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjYmJkZWZiXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMXJlbVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW5SaWdodDogXCI4cHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RpcG8uaWNvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsZXhEaXJlY3Rpb246IFwiY29sdW1uXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsZXg6IDEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJMZXhlbmQsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA2MDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzMzM1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGlwby52YWx1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJMZXhlbmQsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIwLjc1cmVtXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzY2NlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTdHlsZTogXCJpdGFsaWNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA1MDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0aXBvLmNhdGVnb3J5fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtlcnJvci5wZXJzb25UaXBvQ2xpZW50ZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1IZWxwZXJUZXh0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBmb250RmFtaWx5OiBcIkludGVyLCBzYW5zLXNlcmlmXCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3IucGVyc29uVGlwb0NsaWVudGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtSGVscGVyVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvRm9ybUNvbnRyb2w+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogU2VjY2nDs246IEluZm9ybWFjacOzbiBkZWwgQ29udGFjdG8gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiaDZcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiTGV4ZW5kLCBzYW5zLXNlcmlmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogXCI2MDBcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjMzMzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWI6IDMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbXQ6IDMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMS4ycmVtXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEluZm9ybWFjacOzbiBkZWwgQ29udGFjdG9cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBTZWd1bmRhIGZpbGE6IE5vbWJyZSBDb250YWN0byB5IENhcmdvICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxHcmlkIHNpemU9e3sgeHM6IDEyLCBzbTogNiB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIE5vbWJyZSBkZWwgQ29udGFjdG9cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0RmllbGRcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFajogSnVhbiBQw6lyZXpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cIm5vbWJyZUNvbnRhY3RvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInBlcnNvbk5vbWJyZUNvbnRhY3RvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihlcnJvci5wZXJzb25Ob21icmVDb250YWN0byl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlbHBlclRleHQ9e2Vycm9yLnBlcnNvbk5vbWJyZUNvbnRhY3RvfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGU6IFJlYWN0LkNoYW5nZUV2ZW50PFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgSFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJbnB1dENoYW5nZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUgYXMgUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbk5vbWJyZUNvbnRhY3RvfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgSW5wdXRQcm9wcz17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGVuZEFkb3JubWVudDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0QWRvcm5tZW50IHBvc2l0aW9uPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS5wZXJzb25Ob21icmVDb250YWN0byAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlcnJvci5wZXJzb25Ob21icmVDb250YWN0byA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFcnJvckljb24gY29sb3I9XCJlcnJvclwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY29sb3I9XCJzdWNjZXNzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5wdXRBZG9ybm1lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZTogeyBmb250RmFtaWx5OiBcIkludGVyLCBzYW5zLXNlcmlmXCIgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aUlucHV0QmFzZS1pbnB1dFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIkludGVyLCBzYW5zLXNlcmlmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aUZvcm1IZWxwZXJUZXh0LXJvb3RcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBDYXJnbyAoT3BjaW9uYWwpXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWo6IEVuY2FyZ2FkbywgUHJvcGlldGFyaW8sIEFkbWluaXN0cmFkb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImNhcmdvQ29udGFjdG9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicGVyc29uQ2FyZ29Db250YWN0b1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3I9e0Jvb2xlYW4oZXJyb3IucGVyc29uQ2FyZ29Db250YWN0byl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlbHBlclRleHQ9e2Vycm9yLnBlcnNvbkNhcmdvQ29udGFjdG99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZTogUmVhY3QuQ2hhbmdlRXZlbnQ8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFRleHRBcmVhRWxlbWVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUlucHV0Q2hhbmdlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZSBhcyBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGVyc29uQ2FyZ29Db250YWN0b31cclxuICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cImVuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybURhdGEucGVyc29uQ2FyZ29Db250YWN0byAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlcnJvci5wZXJzb25DYXJnb0NvbnRhY3RvID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVycm9ySWNvbiBjb2xvcj1cImVycm9yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjb2xvcj1cInN1Y2Nlc3NcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlOiB7IGZvbnRGYW1pbHk6IFwiSW50ZXIsIHNhbnMtc2VyaWZcIiB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFwiJiAuTXVpSW5wdXRCYXNlLWlucHV0XCI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiSW50ZXIsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFwiJiAuTXVpRm9ybUhlbHBlclRleHQtcm9vdFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIkludGVyLCBzYW5zLXNlcmlmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogVGVyY2VyYSBmaWxhOiBEb21pY2lsaW8gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBEb21pY2lsaW9cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0RmllbGRcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFajogQ29yZG9iYSAxMjNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImRvbWljaWxpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwZXJzb25Eb21pY2lsaW9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKGVycm9yLnBlcnNvbkRvbWljaWxpbyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlbHBlclRleHQ9e2Vycm9yLnBlcnNvbkRvbWljaWxpb31cclxuICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZTogUmVhY3QuQ2hhbmdlRXZlbnQ8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFRleHRBcmVhRWxlbWVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUlucHV0Q2hhbmdlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZSBhcyBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGVyc29uRG9taWNpbGlvfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBJbnB1dFByb3BzPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZW5kQWRvcm5tZW50OiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRBZG9ybm1lbnQgcG9zaXRpb249XCJlbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1EYXRhLnBlcnNvbkRvbWljaWxpbyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlcnJvci5wZXJzb25Eb21pY2lsaW8gPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXJyb3JJY29uIGNvbG9yPVwiZXJyb3JcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNvbG9yPVwic3VjY2Vzc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0lucHV0QWRvcm5tZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU6IHsgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXCImIC5NdWlJbnB1dEJhc2UtaW5wdXRcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXCImIC5NdWlGb3JtSGVscGVyVGV4dC1yb290XCI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiSW50ZXIsIHNhbnMtc2VyaWZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBDdWFydGEgZmlsYTogVGVsw6lmb25vIHkgRW1haWwgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgey8qIENhbWJpYWRvIGRlIHNtPXs3fSBhIHNtPXs2fSAqL31cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFRlbMOpZm9ub1xyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVqOiAwMDAwLTAwMDAwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwidGVsZWZvbm9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicGVyc29uVGVsZWZvbm9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKGVycm9yLnBlcnNvblRlbGVmb25vKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaGVscGVyVGV4dD17ZXJyb3IucGVyc29uVGVsZWZvbm99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGU6IFJlYWN0LkNoYW5nZUV2ZW50PFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgSFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJbnB1dENoYW5nZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUgYXMgUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvblRlbGVmb25vfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBJbnB1dFByb3BzPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZW5kQWRvcm5tZW50OiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRBZG9ybm1lbnQgcG9zaXRpb249XCJlbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1EYXRhLnBlcnNvblRlbGVmb25vICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVycm9yLnBlcnNvblRlbGVmb25vID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVycm9ySWNvbiBjb2xvcj1cImVycm9yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjb2xvcj1cInN1Y2Nlc3NcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgey8qIENhbWJpYWRvIGRlIHNtPXs1fSBhIHNtPXs2fSAqL31cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEVtYWlsXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWo6IGp1YW4ucGVyZXpAZ21haWwuY29tXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwZXJzb25NYWlsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKGVycm9yLnBlcnNvbk1haWwpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWxwZXJUZXh0PXtlcnJvci5wZXJzb25NYWlsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGU6IFJlYWN0LkNoYW5nZUV2ZW50PFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgSFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJbnB1dENoYW5nZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUgYXMgUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbk1haWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cImVuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybURhdGEucGVyc29uTWFpbCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlcnJvci5wZXJzb25NYWlsID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVycm9ySWNvbiBjb2xvcj1cImVycm9yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjb2xvcj1cInN1Y2Nlc3NcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBUZXJjZXJhIGZpbGE6IExvY2FsaWRhZCB5IFByb3ZpbmNpYSAqL31cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBzaXplPXt7IHhzOiAxMiwgc206IDYgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7LyogQ2FtYmlhZG8gZGUgc209ezd9IGEgc209ezZ9ICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgc3g9e2xhYmVsU3R5bGVzfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgTG9jYWxpZGFkXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWo6IE1lcmNlZGVzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJsb2NhbGlkYWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicGVyc29uTG9jYWxpZGFkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihlcnJvci5wZXJzb25Mb2NhbGlkYWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWxwZXJUZXh0PXtlcnJvci5wZXJzb25Mb2NhbGlkYWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGU6IFJlYWN0LkNoYW5nZUV2ZW50PFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgSFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJbnB1dENoYW5nZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUgYXMgUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbkxvY2FsaWRhZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgSW5wdXRQcm9wcz17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGVuZEFkb3JubWVudDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0QWRvcm5tZW50IHBvc2l0aW9uPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS5wZXJzb25Mb2NhbGlkYWQgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZXJyb3IucGVyc29uTG9jYWxpZGFkID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVycm9ySWNvbiBjb2xvcj1cImVycm9yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjb2xvcj1cInN1Y2Nlc3NcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgey8qIENhbWJpYWRvIGRlIHNtPXs1fSBhIHNtPXs2fSAqL31cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFByb3ZpbmNpYVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcj17Qm9vbGVhbihlcnJvci5wZXJzb25Qcm92aW5jaWEpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInBlcnNvblByb3ZpbmNpYVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWxJZD1cImRlbW8tc2ltcGxlLXNlbGVjdC1sYWJlbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvblByb3ZpbmNpYX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGV2ZW50OiBTZWxlY3RDaGFuZ2VFdmVudDxzdHJpbmc+KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzeW50aGV0aWNFdmVudCA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogXCJwZXJzb25Qcm92aW5jaWFcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogZXZlbnQudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBhcyBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MU2VsZWN0RWxlbWVudD47XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVQcm92aW5jaWFDaGFuZ2Uoc3ludGhldGljRXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dFJlZj17c2VsZWN0UHJvdmluY2lhUmVmfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXlFbXB0eVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJlbmRlclZhbHVlPXsoc2VsZWN0ZWQpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghc2VsZWN0ZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFwiU2VsZWNjaW9uZSB1bmEgcHJvdmluY2lhXCI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VsZWN0ZWQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybURhdGEucGVyc29uUHJvdmluY2lhICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAhZXJyb3IucGVyc29uUHJvdmluY2lhID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRBZG9ybm1lbnQgcG9zaXRpb249XCJlbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNvbG9yPVwic3VjY2Vzc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5wdXRBZG9ybm1lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogbnVsbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBNZW51UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFBhcGVyUHJvcHM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXhIZWlnaHQ6IDIwMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBtaW5XaWR0aDogXCIyMDBweFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvdmluY2lhcy5tYXAoKHByb3ZpbmNpYSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIGtleT17cHJvdmluY2lhfSB2YWx1ZT17cHJvdmluY2lhfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3ZpbmNpYX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3IucGVyc29uUHJvdmluY2lhICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUhlbHBlclRleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3IucGVyc29uUHJvdmluY2lhfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvRm9ybUhlbHBlclRleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0Zvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIEN1YXJ0YSBmaWxhOiBDb25kLiBGcmVudGUgYWwgSVZBIHkgRG9jdW1lbnRvICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxHcmlkIHNpemU9e3sgeHM6IDEyLCBzbTogNiB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBDYW1iaWFkbyBkZSBzbT17N30gYSBzbT17Nn0gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17bGFiZWxTdHlsZXN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBDb25kLiBGcmVudGUgYWwgSVZBXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUNvbnRyb2xcclxuICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKGVycm9yLnBlcnNvbkNvbmRGcmVudGVJdmEpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjb25kSXZhXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicGVyc29uQ29uZEZyZW50ZUl2YVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbkNvbmRGcmVudGVJdmF9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhldmVudDogU2VsZWN0Q2hhbmdlRXZlbnQ8c3RyaW5nPikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3ludGhldGljRXZlbnQgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IFwicGVyc29uQ29uZEZyZW50ZUl2YVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBldmVudC50YXJnZXQudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGFzIFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxTZWxlY3RFbGVtZW50PjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNvbmRJdmFDaGFuZ2Uoc3ludGhldGljRXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXRSZWY9e3NlbGVjdENvbmRJdmFSZWZ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheUVtcHR5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyVmFsdWU9eyhzZWxlY3RlZCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFzZWxlY3RlZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gXCJTZWxlY2Npb25lIHVuYSBvcGNpw7NuXCI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VsZWN0ZWQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybURhdGEucGVyc29uQ29uZEZyZW50ZUl2YSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgIWVycm9yLnBlcnNvbkNvbmRGcmVudGVJdmEgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cImVuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY29sb3I9XCJzdWNjZXNzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnB1dEFkb3JubWVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbmRGcmVudGVJdmFPcHRpb25zLm1hcCgob3B0aW9uKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TWVudUl0ZW0ga2V5PXtvcHRpb259IHZhbHVlPXtvcHRpb259PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7b3B0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtlcnJvci5wZXJzb25Db25kRnJlbnRlSXZhICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUhlbHBlclRleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3IucGVyc29uQ29uZEZyZW50ZUl2YX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0Zvcm1IZWxwZXJUZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2IH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAge1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgey8qIENhbWJpYWRvIGRlIHNtPXs1fSBhIHNtPXs2fSAqL31cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXtsYWJlbFN0eWxlc30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIERvY3VtZW50b1xyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImRvY3VtZW50b1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWo6IDAwLTAwMDAwMDAwLTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicGVyc29uRG9jdW1lbnRvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcnNvbkRvY3VtZW50b31cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlOiBSZWFjdC5DaGFuZ2VFdmVudDxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MVGV4dEFyZWFFbGVtZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSW5wdXRDaGFuZ2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlIGFzIFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yPXtCb29sZWFuKGVycm9yLnBlcnNvbkRvY3VtZW50byl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlbHBlclRleHQ9e2Vycm9yLnBlcnNvbkRvY3VtZW50b31cclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0UmVmPXtkb2N1bWVudG9SZWZ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRBZG9ybm1lbnQ6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cImVuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybURhdGEucGVyc29uRG9jdW1lbnRvICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVycm9yLnBlcnNvbkRvY3VtZW50byA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFcnJvckljb24gY29sb3I9XCJlcnJvclwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY29sb3I9XCJzdWNjZXNzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5wdXRBZG9ybm1lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICA8R3JpZFxyXG4gICAgICAgICAgICAgICAgICBzaXplPXt7IHhzOiAxMiwgc206IDYgfX1cclxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgbWFyZ2luOiBcIjhweCAwXCIsIHBhZGRpbmc6IFwiOHB4XCIgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezF9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxHcmlkXHJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPXt7IHhzOiAxMiwgc206IDYgfX1cclxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJmbGV4LWVuZFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBnYXA6IFwiOHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhldmVudCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVDbGlja0Nsb3NlKGV2ZW50LCBcImNsb3NlQnV0dG9uQ2xpY2tcIilcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxhclxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY29udGFpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnRJY29uPXs8QWRkQ2lyY2xlSWNvbiAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBiZ2NvbG9yOiBcIiMyRTdEMzJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXCImOmhvdmVyXCI6IHsgYmdjb2xvcjogXCIjMUI1RTIwXCIgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0VHJhbnNmb3JtOiBcIm5vbmVcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcIiYgLk11aVN2Z0ljb24tcm9vdFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2VzdGFkb01vZGFsID09PSBcImFkZFwiID8gXCJSZWdpc3RyYXJcIiA6IFwiR3VhcmRhclwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XHJcbiAgICAgICAgICA8L0JveD5cclxuICAgICAgICA8L0JveD5cclxuICAgICAgPC9EaWFsb2c+XHJcblxyXG4gICAgICB7LyogPERhdGF0YWJsZVxyXG4gICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgcm93cz17ZmlsdGVyZWRSb3dzLmxlbmd0aCA+IDAgPyBmaWx0ZXJlZFJvd3MgOiByb3dzfVxyXG4gICAgICAgIG9wdGlvbj17dHJ1ZX1cclxuICAgICAgICBvcHRpb25EZWxldGVGdW5jdGlvbj17aGFuZGxlRGVsZXRlQ2xpZW50ZX1cclxuICAgICAgICBvcHRpb25VcGRhdGVGdW5jdGlvbj17aGFuZGxlRWRpdH1cclxuICAgICAgICBzZXRTZWxlY3RlZFJvdz17c2V0U2VsZWN0ZWRSb3d9XHJcbiAgICAgICAgc2VsZWN0ZWRSb3c9e3NlbGVjdGVkUm93fVxyXG4gICAgICAgIG9wdGlvblNlbGVjdD17aGFuZGxlU2VsZWN0QWdyaWN1bHRvcn1cclxuICAgICAgLz4gKi99XHJcbiAgICAgIHsvKiA8L2Rpdj4gKi99XHJcbiAgICAgIHsvKiA8L2Rpdj4gKi99XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQWdyaWN1bHRvckdhbmFkZXJvO1xyXG4iXSwibmFtZXMiOlsiaW50ZXIiLCJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwiQWRkT3V0bGluZWRJY29uIiwiRGF0YUdyaWQiLCJNdWlEYXRhR3JpZCIsIkJ1dHRvbiIsIkdyaWQiLCJJbnB1dEFkb3JubWVudCIsIlBhcGVyIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nQ29udGVudFRleHQiLCJGb3JtQ29udHJvbCIsIkZvcm1IZWxwZXJUZXh0IiwiSWNvbkJ1dHRvbiIsIk1lbnVJdGVtIiwiU2VsZWN0IiwiVGV4dEZpZWxkIiwiVHlwb2dyYXBoeSIsIkRhdGF0YWJsZSIsIkNsb3NlSWNvbiIsIkJveCIsIkNoZWNrQ2lyY2xlSWNvbiIsIkVycm9ySWNvbiIsIlNlYXJjaCIsIlNlYXJjaEljb24iLCJBZGRDaXJjbGVJY29uIiwiQWdyaWN1bHRvckdhbmFkZXJvIiwiaXNTdWJtaXR0aW5nIiwic2V0SXNTdWJtaXR0aW5nIiwic2VsZWN0ZWRSb3ciLCJzZXRTZWxlY3RlZFJvdyIsIm9wZW4iLCJzZXRPcGVuIiwicm93cyIsInNldFJvd3MiLCJpbnB1dFZhbHVlIiwic2V0SW5wdXRWYWx1ZSIsImlzU2VhcmNoQmFyT3BlbiIsInNldElzU2VhcmNoQmFyT3BlbiIsImZpbHRlcmVkUm93cyIsInNldEZpbHRlcmVkUm93cyIsInBlcnNvbklkIiwic2V0UGVyc29uSWQiLCJlc3RhZG9Nb2RhbCIsInNldEVzdGFkb01vZGFsIiwic2VsZWN0ZWRDbGllbnRJZCIsInNldFNlbGVjdGVkQ2xpZW50SWQiLCJzZWxlY3RQcm92aW5jaWFSZWYiLCJzZWxlY3RDb25kSXZhUmVmIiwiZG9jdW1lbnRvUmVmIiwiZXJyb3IiLCJzZXRFcnJvciIsImhhbmRsZUNsaWVudFNlbGVjdCIsImNsaWVudElkIiwic2VsZWN0ZWRDbGllbnQiLCJmaW5kIiwicm93IiwiaWQiLCJsb2NhbGlkYWQiLCJwcm92aW5jaWEiLCJsdWdhciIsInNwbGl0Iiwic2V0Rm9ybURhdGEiLCJwZXJzb25SYXpvblNvY2lhbCIsInJhem9uU29jaWFsIiwicGVyc29uVGlwb0NsaWVudGUiLCJ0aXBvQ2xpZW50ZSIsInBlcnNvbk5vbWJyZUNvbnRhY3RvIiwibm9tYnJlQ29udGFjdG8iLCJwZXJzb25DYXJnb0NvbnRhY3RvIiwiY2FyZ29Db250YWN0byIsInBlcnNvbkRvbWljaWxpbyIsImRpcmVjY2lvbiIsInBlcnNvblRlbGVmb25vIiwidGVsZWZvbm8iLCJwZXJzb25NYWlsIiwibWFpbCIsInBlcnNvbkxvY2FsaWRhZCIsInBlcnNvblByb3ZpbmNpYSIsInBlcnNvbkNvbmRGcmVudGVJdmEiLCJjb25kRnJlbnRlSXZhIiwicGVyc29uRG9jdW1lbnRvIiwiZG9jdW1lbnRvIiwiZm9ybURhdGEiLCJwcm92aW5jaWFzIiwidGlwb0NsaWVudGVPcHRpb25zIiwidmFsdWUiLCJjYXRlZ29yeSIsImljb24iLCJjb25kRnJlbnRlSXZhT3B0aW9ucyIsImhhbmRsZU9wZW5BZGQiLCJjbGVhckZyb20iLCJoYW5kbGVDbGlja0Nsb3NlIiwiX2V2ZW50IiwicmVhc29uIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJlIiwibmFtZSIsInRhcmdldCIsInRlc3QiLCJwcmV2RXJyb3IiLCJjbGVhbmVkIiwicmVwbGFjZSIsImxlbmd0aCIsImZvcm1hdHRlZCIsInNsaWNlIiwiaXNWYWxpZEZvcm1hdCIsInByZXZTdGF0ZSIsImVtYWlsUmVnZXgiLCJoYW5kbGVTdWJtaXQiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZVNlYXJjaENsaWNrIiwiY29sdW1ucyIsImZpZWxkIiwiaGVhZGVyTmFtZSIsIndpZHRoIiwiaGVhZGVyQ2xhc3NOYW1lIiwicmVuZGVyQ2VsbCIsInBhcmFtcyIsInN4IiwicHkiLCJ2YXJpYW50IiwiZm9udFdlaWdodCIsImNvbG9yIiwiZm9udFNpemUiLCJsaW5lSGVpZ2h0IiwiZm9udFN0eWxlIiwiaGFuZGxlQWRkQ2xpZW50ZSIsImNvbnNvbGUiLCJsb2ciLCJuZXdQZXJzb24iLCJlcnJvcnMiLCJyZXMiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm9rIiwiRXJyb3IiLCJkYXRhQ2xpZW50ZXMiLCJmZXRjaENsaWVudGVzIiwianNvbiIsImdldERhdGEiLCJoYW5kbGVTZWFyaENsaWVudGUiLCJldmVudCIsInNlYXJjaFZhbHVlIiwic2V0U2VhcmNoVGVybSIsImZpbHRlcmVkRGF0YSIsImZpbHRlciIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJoYW5kbGVEZWxldGVDbGllbnRlIiwiaGFuZGxlRWRpdCIsImFncmljdWx0b3IiLCJ0cmltIiwiaGFuZGxlVXBkYXRlQ2xpZW50ZSIsInVwZGF0ZWRSb3dzIiwibWFwIiwiaGFuZGxlTG9jYWxpZGFkS2V5RG93biIsImtleSIsImN1cnJlbnQiLCJmb2N1cyIsImhhbmRsZVByb3ZpbmNpYUNoYW5nZSIsInNldFRpbWVvdXQiLCJoYW5kbGVDb25kSXZhQ2hhbmdlIiwiaGFuZGxlU2VsZWN0QWdyaWN1bHRvciIsInNlbGVjdGVkQWdyaWN1bHRvciIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJzZWFyY2hUZXJtIiwicGFnZSIsInNldFBhZ2UiLCJoYW5kbGVTZWFyY2hDaGFuZ2UiLCJsYWJlbFN0eWxlcyIsIm1hcmdpbkJvdHRvbSIsImRpc3BsYXkiLCJmb250RmFtaWx5IiwianVzdGlmeUNvbnRlbnQiLCJtYiIsIm10IiwiY29tcG9uZW50Iiwic3R5bGUiLCJvbkNsaWNrIiwiYmdjb2xvciIsImhlaWdodCIsImFsaWduU2VsZiIsInN0YXJ0SWNvbiIsImVsZXZhdGlvbiIsInAiLCJib3JkZXJSYWRpdXMiLCJmdWxsV2lkdGgiLCJwbGFjZWhvbGRlciIsIm9uQ2hhbmdlIiwiSW5wdXRQcm9wcyIsInN0YXJ0QWRvcm5tZW50IiwicG9zaXRpb24iLCJvcHRpb24iLCJvcHRpb25EZWxldGVGdW5jdGlvbiIsIm9wdGlvblVwZGF0ZUZ1bmN0aW9uIiwib3B0aW9uU2VsZWN0Iiwib25DbG9zZSIsIm1heFdpZHRoIiwibWluSGVpZ2h0IiwiYXJpYS1sYWJlbCIsInJpZ2h0IiwidG9wIiwib25TdWJtaXQiLCJjbGFzc05hbWUiLCJjb250YWluZXIiLCJzcGFjaW5nIiwic2l6ZSIsInhzIiwic20iLCJ0eXBlIiwiQm9vbGVhbiIsImhlbHBlclRleHQiLCJkaXNhYmxlZCIsImVuZEFkb3JubWVudCIsImRpc3BsYXlFbXB0eSIsIk1lbnVQcm9wcyIsIlBhcGVyUHJvcHMiLCJtYXhIZWlnaHQiLCJtaW5XaWR0aCIsInBhZGRpbmciLCJ3aGl0ZVNwYWNlIiwid29yZFdyYXAiLCJib3JkZXJCb3R0b20iLCJhbGlnbkl0ZW1zIiwiYm9yZGVyQ29sb3IiLCJib3JkZXJXaWR0aCIsImJhY2tncm91bmRDb2xvciIsInRpcG8iLCJpbmRleCIsImlzRmlyc3RJbkNhdGVnb3J5IiwiZ2FwIiwiYm9yZGVyVG9wIiwibWFyZ2luVG9wIiwicGFkZGluZ1RvcCIsImJvcmRlckxlZnQiLCJzcGFuIiwibWFyZ2luUmlnaHQiLCJmbGV4RGlyZWN0aW9uIiwiZmxleCIsInJlcXVpcmVkIiwibGFiZWxJZCIsInN5bnRoZXRpY0V2ZW50IiwiaW5wdXRSZWYiLCJyZW5kZXJWYWx1ZSIsInNlbGVjdGVkIiwibWFyZ2luIiwidGV4dFRyYW5zZm9ybSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});