"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [enabledTabs, setEnabledTabs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        true,\n        false,\n        false\n    ]); // Solo la primera pestaña habilitada inicialmente\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Funciones de validación para cada pestaña\n    const validateTab1 = ()=>{\n        const { propietarioNombre, propietarioDocumento, propietarioTelefono, propietarioEmail } = formData;\n        return !!(propietarioNombre.trim() && propietarioDocumento.trim() && propietarioTelefono.trim() && propietarioEmail.trim());\n    };\n    const validateTab2 = ()=>{\n        const { empresaRazonSocial, empresaTipoCliente, empresaDomicilio, empresaLocalidad, empresaProvincia, empresaCondFrenteIva } = formData;\n        return !!(empresaRazonSocial.trim() && empresaTipoCliente.trim() && empresaDomicilio.trim() && empresaLocalidad.trim() && empresaProvincia.trim() && empresaCondFrenteIva.trim());\n    };\n    const validateTab3 = ()=>{\n        return formData.contactos.length > 0 && formData.contactos[0].nombre.trim() !== \"\" && formData.contactos[0].cargo.trim() !== \"\" && formData.contactos[0].telefono.trim() !== \"\" && formData.contactos[0].email.trim() !== \"\";\n    };\n    // Función para habilitar la siguiente pestaña\n    const enableNextTab = (currentTab)=>{\n        const newEnabledTabs = [\n            ...enabledTabs\n        ];\n        if (currentTab + 1 < newEnabledTabs.length) {\n            newEnabledTabs[currentTab + 1] = true;\n            setEnabledTabs(newEnabledTabs);\n        }\n    };\n    // Función para ir a la siguiente pestaña\n    const handleNextTab = ()=>{\n        let canProceed = false;\n        switch(tabValue){\n            case 0:\n                canProceed = validateTab1();\n                break;\n            case 1:\n                canProceed = validateTab2();\n                break;\n            case 2:\n                canProceed = validateTab3();\n                break;\n        }\n        if (canProceed) {\n            enableNextTab(tabValue);\n            setTabValue(tabValue + 1);\n        } else {\n            alert(\"Por favor complete todos los campos requeridos antes de continuar.\");\n        }\n    };\n    // Función para ir a la pestaña anterior\n    const handlePreviousTab = ()=>{\n        if (tabValue > 0) {\n            setTabValue(tabValue - 1);\n        }\n    };\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData((prevData)=>({\n                    ...prevData,\n                    empresaRazonSocial: selectedClient.razonSocial,\n                    empresaTipoCliente: selectedClient.tipoCliente || \"\",\n                    empresaDomicilio: selectedClient.direccion,\n                    empresaLocalidad: localidad,\n                    empresaProvincia: provincia,\n                    empresaCondFrenteIva: selectedClient.condFrenteIva,\n                    // Update contacts array with the selected client's contact information\n                    contactos: [\n                        {\n                            id: 1,\n                            nombre: selectedClient.nombreContacto || \"\",\n                            cargo: selectedClient.cargoContacto || \"\",\n                            telefono: selectedClient.telefono,\n                            email: selectedClient.mail\n                        }\n                    ]\n                }));\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Pestaña 1: Propietario/Persona Física\n        propietarioNombre: \"\",\n        propietarioDocumento: \"\",\n        propietarioTelefono: \"\",\n        propietarioEmail: \"\",\n        usarComoRazonSocial: false,\n        // Pestaña 2: Empresa/Razón Social\n        empresaRazonSocial: \"\",\n        empresaTipoCliente: \"\",\n        empresaDomicilio: \"\",\n        empresaLocalidad: \"\",\n        empresaProvincia: \"\",\n        empresaCondFrenteIva: \"\",\n        // Pestaña 3: Contactos/Encargados (array dinámico)\n        contactos: [\n            {\n                id: 1,\n                nombre: \"\",\n                cargo: \"\",\n                telefono: \"\",\n                email: \"\"\n            }\n        ]\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            // Pestaña 1: Propietario/Persona Física\n            propietarioNombre: \"\",\n            propietarioDocumento: \"\",\n            propietarioTelefono: \"\",\n            propietarioEmail: \"\",\n            usarComoRazonSocial: false,\n            // Pestaña 2: Empresa/Razón Social\n            empresaRazonSocial: \"\",\n            empresaTipoCliente: \"\",\n            empresaDomicilio: \"\",\n            empresaLocalidad: \"\",\n            empresaProvincia: \"\",\n            empresaCondFrenteIva: \"\",\n            // Pestaña 3: Contactos/Encargados\n            contactos: [\n                {\n                    id: 1,\n                    nombre: \"\",\n                    cargo: \"\",\n                    telefono: \"\",\n                    email: \"\"\n                }\n            ]\n        });\n        setError({});\n        setTabValue(0); // Resetear al primer tab\n        setEnabledTabs([\n            true,\n            false,\n            false\n        ]); // Resetear pestañas habilitadas\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Validaciones para campos de texto (nombres, razón social, etc.)\n        if (name === \"propietarioNombre\" || name === \"empresaRazonSocial\" || name === \"empresaLocalidad\" || name.startsWith(\"contacto\") && name.includes(\"nombre\")) {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para domicilio\n        if (name === \"empresaDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        // Validaciones para teléfonos\n        if (name === \"propietarioTelefono\" || name.includes(\"telefono\")) {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"propietarioDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    [name]: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>{\n            const newState = {\n                ...prevState,\n                [name]: value\n            };\n            // Si está marcada la opción de usar como razón social, sincronizar datos\n            if (prevState.usarComoRazonSocial) {\n                if (name === \"propietarioNombre\") {\n                    newState.empresaRazonSocial = value;\n                    // También actualizar el primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            nombre: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioTelefono\") {\n                    // Actualizar el teléfono del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            telefono: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                } else if (name === \"propietarioEmail\") {\n                    // Actualizar el email del primer contacto\n                    newState.contactos = [\n                        {\n                            ...prevState.contactos[0],\n                            email: value\n                        },\n                        ...prevState.contactos.slice(1)\n                    ];\n                }\n            }\n            return newState;\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 553,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            direccion: formData.empresaDomicilio,\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            lugar: lugar,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            documento: \"\"\n        };\n        // TODO: Add proper form validation if needed\n        // For now, individual field validations are handled in the onChange handlers\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData((prevData)=>({\n                        ...prevData,\n                        empresaRazonSocial: agricultor.razonSocial,\n                        empresaTipoCliente: agricultor.tipoCliente || \"\",\n                        empresaDomicilio: agricultor.direccion,\n                        empresaLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                        empresaProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                        empresaCondFrenteIva: agricultor.condFrenteIva,\n                        contactos: [\n                            {\n                                id: 1,\n                                nombre: agricultor.nombreContacto || \"\",\n                                cargo: agricultor.cargoContacto || \"\",\n                                telefono: agricultor.telefono,\n                                email: agricultor.mail\n                            }\n                        ]\n                    }));\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        var _formData_contactos_, _formData_contactos_1, _formData_contactos_2, _formData_contactos_3;\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.empresaLocalidad, \" - \").concat(formData.empresaProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            razonSocial: formData.empresaRazonSocial,\n            tipoCliente: formData.empresaTipoCliente,\n            nombreContacto: ((_formData_contactos_ = formData.contactos[0]) === null || _formData_contactos_ === void 0 ? void 0 : _formData_contactos_.nombre) || \"\",\n            cargoContacto: ((_formData_contactos_1 = formData.contactos[0]) === null || _formData_contactos_1 === void 0 ? void 0 : _formData_contactos_1.cargo) || \"\",\n            direccion: formData.empresaDomicilio,\n            telefono: ((_formData_contactos_2 = formData.contactos[0]) === null || _formData_contactos_2 === void 0 ? void 0 : _formData_contactos_2.telefono) || \"\",\n            mail: ((_formData_contactos_3 = formData.contactos[0]) === null || _formData_contactos_3 === void 0 ? void 0 : _formData_contactos_3.email) || \"\",\n            lugar: lugar,\n            provincia: formData.empresaProvincia,\n            condFrenteIva: formData.empresaCondFrenteIva,\n            documento: \"\"\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 935,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 945,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 934,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 967,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 956,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 926,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 982,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 974,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 973,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1018,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1046,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1041,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                sx: {\n                                    p: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            borderBottom: 1,\n                                            borderColor: \"divider\",\n                                            mb: 3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            value: tabValue,\n                                            onChange: (event, newValue)=>{\n                                                // Solo permitir cambiar a pestañas habilitadas\n                                                if (enabledTabs[newValue]) {\n                                                    setTabValue(newValue);\n                                                }\n                                            },\n                                            sx: {\n                                                \"& .MuiTab-root\": {\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    fontWeight: 600,\n                                                    textTransform: \"none\",\n                                                    fontSize: \"1rem\"\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDC64 Propietario/Persona F\\xedsica\",\n                                                    sx: {\n                                                        minWidth: 220\n                                                    },\n                                                    disabled: !enabledTabs[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1073,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83C\\uDFE2 Empresa/Raz\\xf3n Social\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1078,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    label: \"\\uD83D\\uDCDE Contacto/Encargado\",\n                                                    sx: {\n                                                        minWidth: 200\n                                                    },\n                                                    disabled: !enabledTabs[2]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1083,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1056,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tabValue === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos del Propietario/Persona F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Nombre Completo *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1107,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Juan Carlos P\\xe9rez\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioNombre\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioNombre),\n                                                                helperText: error.propietarioNombre,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioNombre,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioNombre && (error.propietarioNombre ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1126,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1128,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1123,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1110,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"DNI/CUIT *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1138,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 20-12345678-9\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioDocumento\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioDocumento),\n                                                                helperText: error.propietarioDocumento,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioDocumento,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioDocumento && (error.propietarioDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1157,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1159,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1154,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1141,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tel\\xe9fono Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1169,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: 0000-000000\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioTelefono\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioTelefono),\n                                                                helperText: error.propietarioTelefono,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioTelefono,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioTelefono && (error.propietarioTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1187,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1189,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1184,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1172,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1168,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Email Personal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1199,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: <EMAIL>\",\n                                                                variant: \"outlined\",\n                                                                name: \"propietarioEmail\",\n                                                                type: \"email\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.propietarioEmail),\n                                                                helperText: error.propietarioEmail,\n                                                                onChange: handleInputChange,\n                                                                value: formData.propietarioEmail,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.propietarioEmail && (error.propietarioEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1217,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1219,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1214,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1202,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                mt: 2,\n                                                                p: 2,\n                                                                bgcolor: \"#f5f5f5\",\n                                                                borderRadius: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        cursor: \"pointer\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.usarComoRazonSocial,\n                                                                            onChange: (e)=>{\n                                                                                const checked = e.target.checked;\n                                                                                setFormData((prev)=>{\n                                                                                    var _prev_contactos_;\n                                                                                    return {\n                                                                                        ...prev,\n                                                                                        usarComoRazonSocial: checked,\n                                                                                        // Si se marca, copiar datos del propietario a la empresa\n                                                                                        empresaRazonSocial: checked ? prev.propietarioNombre : \"\",\n                                                                                        // También copiar el primer contacto con los datos del propietario\n                                                                                        contactos: checked ? [\n                                                                                            {\n                                                                                                ...prev.contactos[0],\n                                                                                                nombre: prev.propietarioNombre,\n                                                                                                telefono: prev.propietarioTelefono,\n                                                                                                email: prev.propietarioEmail,\n                                                                                                cargo: ((_prev_contactos_ = prev.contactos[0]) === null || _prev_contactos_ === void 0 ? void 0 : _prev_contactos_.cargo) || \"Propietario\"\n                                                                                            },\n                                                                                            ...prev.contactos.slice(1)\n                                                                                        ] : prev.contactos\n                                                                                    };\n                                                                                });\n                                                                            },\n                                                                            style: {\n                                                                                marginRight: \"8px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1244,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                fontFamily: \"Inter, sans-serif\"\n                                                                            },\n                                                                            children: \"✅ Usar estos datos del propietario para la empresa y contacto principal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1275,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1237,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        color: \"#2E7D32\",\n                                                                        mt: 1,\n                                                                        display: \"block\",\n                                                                        fontStyle: \"italic\"\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCA1 Los datos del propietario se sincronizar\\xe1n autom\\xe1ticamente con la raz\\xf3n social de la empresa y el contacto principal\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1284,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1229,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1228,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1104,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Cancelar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1312,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1321,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1303,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1093,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Datos de la Empresa/Raz\\xf3n Social\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1339,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                container: true,\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Raz\\xf3n Social *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1352,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Agropecuaria San Juan S.A.\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaRazonSocial\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaRazonSocial),\n                                                                helperText: error.empresaRazonSocial,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaRazonSocial,\n                                                                disabled: formData.usarComoRazonSocial,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaRazonSocial && (error.empresaRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1372,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1374,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1369,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                },\n                                                                sx: {\n                                                                    \"& .MuiInputBase-input\": {\n                                                                        backgroundColor: formData.usarComoRazonSocial ? \"#f5f5f5\" : \"transparent\"\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1355,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            formData.usarComoRazonSocial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                sx: {\n                                                                    color: \"#666\",\n                                                                    mt: 1,\n                                                                    display: \"block\"\n                                                                },\n                                                                children: \"\\uD83D\\uDCA1 Autocompletado desde datos del propietario\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1388,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Tipo de Cliente *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1399,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaTipoCliente),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaTipoCliente\",\n                                                                        value: formData.empresaTipoCliente,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaTipoCliente\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione tipo de cliente\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1422,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            const option = tipoClienteOptions.find((opt)=>opt.value === selected);\n                                                                            return option ? \"\".concat(option.icon, \" \").concat(option.value) : selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione tipo de cliente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1444,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            tipoClienteOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: option.value,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                        sx: {\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\",\n                                                                                            gap: 1\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.icon\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1456,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: option.value\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1457,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1449,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, option.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1448,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1406,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaTipoCliente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1463,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1402,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1398,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Domicilio de la Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1472,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Av. San Mart\\xedn 1234\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaDomicilio\",\n                                                                type: \"text\",\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaDomicilio),\n                                                                helperText: error.empresaDomicilio,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaDomicilio,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaDomicilio && (error.empresaDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1490,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1492,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1487,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1475,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1471,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Localidad *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1502,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                placeholder: \"Ej: Mercedes\",\n                                                                variant: \"outlined\",\n                                                                name: \"empresaLocalidad\",\n                                                                type: \"text\",\n                                                                required: true,\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaLocalidad),\n                                                                helperText: error.empresaLocalidad,\n                                                                onChange: handleInputChange,\n                                                                value: formData.empresaLocalidad,\n                                                                InputProps: {\n                                                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        position: \"end\",\n                                                                        children: formData.empresaLocalidad && (error.empresaLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1521,\n                                                                            columnNumber: 35\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1523,\n                                                                            columnNumber: 35\n                                                                        }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1518,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1505,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1501,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Provincia *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1532,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaProvincia),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaProvincia\",\n                                                                        value: formData.empresaProvincia,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaProvincia\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione provincia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1555,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione provincia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1572,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: provincia,\n                                                                                    children: provincia\n                                                                                }, provincia, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1576,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1539,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaProvincia\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1582,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1535,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: {\n                                                            xs: 12,\n                                                            sm: 6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: labelStyles,\n                                                                children: \"Condici\\xf3n frente al IVA *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1591,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                fullWidth: true,\n                                                                error: Boolean(error.empresaCondFrenteIva),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        name: \"empresaCondFrenteIva\",\n                                                                        value: formData.empresaCondFrenteIva,\n                                                                        onChange: (event)=>{\n                                                                            const syntheticEvent = {\n                                                                                target: {\n                                                                                    name: \"empresaCondFrenteIva\",\n                                                                                    value: event.target.value\n                                                                                }\n                                                                            };\n                                                                            handleInputChange(syntheticEvent);\n                                                                        },\n                                                                        displayEmpty: true,\n                                                                        renderValue: (selected)=>{\n                                                                            if (!selected) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        color: \"#999\"\n                                                                                    },\n                                                                                    children: \"Seleccione condici\\xf3n IVA\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1614,\n                                                                                    columnNumber: 33\n                                                                                }, void 0);\n                                                                            }\n                                                                            return selected;\n                                                                        },\n                                                                        sx: {\n                                                                            height: \"56px\",\n                                                                            \"& .MuiSelect-select\": {\n                                                                                padding: \"16.5px 14px\",\n                                                                                height: \"1.4375em\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                value: \"\",\n                                                                                disabled: true,\n                                                                                children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                lineNumber: 1631,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            condFrenteIvaOptions.map((condicion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    value: condicion,\n                                                                                    children: condicion\n                                                                                }, condicion, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1635,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1598,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    error.empresaCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        children: error.empresaCondFrenteIva\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1641,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                lineNumber: 1594,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1590,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1659,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        onClick: handleNextTab,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: \"Siguiente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1666,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1650,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1338,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tabValue === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    mb: 3,\n                                                    fontFamily: \"Lexend, sans-serif\",\n                                                    color: \"#333\"\n                                                },\n                                                children: \"Contactos/Encargados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1684,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            formData.contactos.map((contacto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    sx: {\n                                                        mb: 4,\n                                                        p: 3,\n                                                        border: \"1px solid #e0e0e0\",\n                                                        borderRadius: 2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                justifyContent: \"space-between\",\n                                                                alignItems: \"center\",\n                                                                mb: 2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"subtitle1\",\n                                                                    sx: {\n                                                                        fontWeight: 600,\n                                                                        color: \"#333\"\n                                                                    },\n                                                                    children: [\n                                                                        \"Contacto #\",\n                                                                        index + 1\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1714,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                formData.contactos.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"outlined\",\n                                                                    color: \"error\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>{\n                                                                        setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                contactos: prev.contactos.filter((c)=>c.id !== contacto.id)\n                                                                            }));\n                                                                    },\n                                                                    children: \"Eliminar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1721,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1706,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            container: true,\n                                                            spacing: 3,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre del Encargado *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1742,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Mar\\xeda Gonz\\xe1lez\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_nombre\"),\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_nombre\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_nombre\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].nombre = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.nombre,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.nombre && (error[\"contacto_\".concat(contacto.id, \"_nombre\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1774,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1776,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1771,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1745,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1741,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Cargo (Opcional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1786,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Administrador, Encargado\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_cargo\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].cargo = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.cargo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1789,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1785,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tel\\xe9fono de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1813,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 0000-000000\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_telefono\"),\n                                                                            type: \"text\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_telefono\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_telefono\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].telefono = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.telefono,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.telefono && (error[\"contacto_\".concat(contacto.id, \"_telefono\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1848,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1850,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1843,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1816,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1812,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Email de Contacto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1860,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: <EMAIL>\",\n                                                                            variant: \"outlined\",\n                                                                            name: \"contacto_\".concat(contacto.id, \"_email\"),\n                                                                            type: \"email\",\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error[\"contacto_\".concat(contacto.id, \"_email\")]),\n                                                                            helperText: error[\"contacto_\".concat(contacto.id, \"_email\")],\n                                                                            onChange: (e)=>{\n                                                                                const newContactos = [\n                                                                                    ...formData.contactos\n                                                                                ];\n                                                                                const contactoIndex = newContactos.findIndex((c)=>c.id === contacto.id);\n                                                                                newContactos[contactoIndex].email = e.target.value;\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        contactos: newContactos\n                                                                                    }));\n                                                                            },\n                                                                            value: contacto.email,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: contacto.email && (error[\"contacto_\".concat(contacto.id, \"_email\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1891,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1893,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1888,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1863,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1859,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                            lineNumber: 1739,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, contacto.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1697,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 1907,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: ()=>{\n                                                    const newId = Math.max(...formData.contactos.map((c)=>c.id)) + 1;\n                                                    setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactos: [\n                                                                ...prev.contactos,\n                                                                {\n                                                                    id: newId,\n                                                                    nombre: \"\",\n                                                                    cargo: \"\",\n                                                                    telefono: \"\",\n                                                                    email: \"\"\n                                                                }\n                                                            ]\n                                                        }));\n                                                },\n                                                sx: {\n                                                    mt: 2\n                                                },\n                                                children: \"Agregar otro contacto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1905,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mt: 3,\n                                                    pt: 2,\n                                                    borderTop: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        onClick: handlePreviousTab,\n                                                        sx: {\n                                                            minWidth: 120\n                                                        },\n                                                        children: \"Anterior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1940,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        type: \"submit\",\n                                                        disabled: isSubmitting,\n                                                        onClick: handleAddCliente,\n                                                        sx: {\n                                                            minWidth: 120,\n                                                            bgcolor: \"#2E7D32\",\n                                                            \"&:hover\": {\n                                                                bgcolor: \"#1B5E20\"\n                                                            }\n                                                        },\n                                                        children: isSubmitting ? \"Guardando...\" : estadoModal === \"add\" ? \"Registrar\" : \"Actualizar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 1947,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1931,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 1683,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 1053,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 1048,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 1016,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 1003,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"BbVUwd51WSh2d/v81WqeKFo1Vys=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});