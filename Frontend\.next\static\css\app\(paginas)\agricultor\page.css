/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Lexend:wght@400;500;600&display=swap');

:root {
  /* --font-roboto: "Roboto", sans-serif;
  --font-open-sans: "Open Sans", sans-serif;
  --font-lato: "Lato", sans-serif;
  --font-montserrat: "Montserrat", sans-serif;
  --font-poppins: "Poppins", sans-serif;
  --font-fira-sans: "Fira Sans", sans-serif;
  --font-roboto-condensed: "Roboto Condensed", sans-serif;
  --font-proza-libre: "Proza Libre", sans-serif;
  --font-work-sans: "Work Sans", sans-serif;
  --font-oswald: "Oswald", sans-serif;

  --font-roboto-slab: "Roboto Slab", serif;
  --font-merriweather: "Merriweather", serif;
  --font-playfair-display: "Playfair Display", serif;
  --font-lora: "Lora", serif;
  --font-pt-serif: "PT Serif", serif;
  --font-noto-serif: "Noto Serif", serif;
  --font-libre-baskerville: "Libre Baskerville", serif;
  --font-old-standard-tt: "Old Standard TT", serif;
  --font-vollkorn: "Vollkorn", serif;
  --font-slabo: "Slabo", serif; */

  --font-sans: "Open Sans", "Arial", sans-serif;
  --font-serif: "Roboto", "Times New Roman", serif;
  --max-width: 1100px;
  --border-radius: 12px;
  --font-mono: ui-monospace, Menlo, Monaco, "Cascadia Mono", "Segoe UI Mono",
    "Roboto Mono", "Oxygen Mono", "Ubuntu Monospace", "Source Code Pro",
    "Fira Mono", "Droid Sans Mono", "Courier New", monospace;

  --foreground-rgb: 0, 0, 0;

  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --primary-glow: conic-gradient(
    from 180deg at 50% 50%,
    #16abff33 0deg,
    #0885ff33 55deg,
    #54d6ff33 120deg,
    #0071ff33 160deg,
    transparent 360deg
  );
  --secondary-glow: radial-gradient(
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );

  --tile-start-rgb: 239, 245, 249;
  --tile-end-rgb: 228, 232, 233;
  --tile-border: conic-gradient(
    #00000080,
    #00000040,
    #00000030,
    #00000020,
    #00000010,
    #00000010,
    #00000080
  );

  --callout-rgb: 238, 240, 241;
  --callout-border-rgb: 172, 175, 176;
  --card-rgb: 180, 185, 188;
  --card-border-rgb: 131, 134, 135;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;

    --primary-glow: radial-gradient(rgba(1, 65, 255, 0.4), rgba(1, 65, 255, 0));
    --secondary-glow: linear-gradient(
      to bottom right,
      rgba(1, 65, 255, 0),
      rgba(1, 65, 255, 0),
      rgba(1, 65, 255, 0.3)
    );

    --tile-start-rgb: 2, 13, 46;
    --tile-end-rgb: 2, 5, 19;
    --tile-border: conic-gradient(
      #ffffff80,
      #ffffff40,
      #ffffff30,
      #ffffff20,
      #ffffff10,
      #ffffff10,
      #ffffff80
    );

    --callout-rgb: 20, 20, 20;
    --callout-border-rgb: 108, 108, 108;
    --card-rgb: 100, 100, 100;
    --card-border-rgb: 200, 200, 200;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

/* body {
   color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb)); 
} */

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: light; /* Cambiar 'dark' a 'light' */
  }
}

/* Estilos para el popup de Mapbox */
.custom-popup .mapboxgl-popup-content {
  position: relative;
  border: 2px solid #4caf50 !important;
  border-radius: 8px !important;
  padding: 0 !important;
  background: white !important;
}

.custom-popup .mapboxgl-popup-tip {
  border: none !important;
  width: 0 !important;
  height: 0 !important;
  position: relative !important;
  margin-top: -2px !important;
}

.custom-popup .mapboxgl-popup-tip::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 50%;
  transform: translateX(-50%);
  border: 10px solid transparent;
  border-top: 10px solid #4caf50;
}

.custom-popup .mapboxgl-popup-tip::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  border: 10px solid transparent;
  border-top: 10px solid white;
  z-index: 1;
}

/* Estilos adicionales para el formulario de registro */



/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\(paginas)\\agricultor\\page.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
  font-family: '__Inter_Fallback_1deade';
  src: local("Arial");
  ascent-override:90.49%;
  descent-override:22.56%;
  line-gap-override:0.00%;
  size-adjust:107.06%;
}@font-face {font-family: '__Inter_Fallback_Fallback_1deade';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_1deade {font-family: '__Inter_Fallback_1deade', '__Inter_Fallback_Fallback_1deade';font-style: normal
}
