"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/agricultor/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(paginas)/agricultor/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\agricultor\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/AddOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddOutlined.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/esm/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Grid,InputAdornment,Paper,Tab,Tabs!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContent!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DialogTitle!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DialogContentText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/DialogContentText/DialogContentText.js\");\n/* harmony import */ var _barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FormControl!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=FormHelperText!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/FormHelperText/FormHelperText.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/table/DataTable */ \"(app-pages-browser)/./src/app/components/table/DataTable.tsx\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Error */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/AddCircle */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/AddCircle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgricultorGanadero = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRow, setSelectedRow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearchBarOpen, setIsSearchBarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredRows, setFilteredRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const DataGrid = _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_3__.DataGrid;\n    const [personId, setPersonId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estadoModal, setEstadoModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabValue, setTabValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const selectProvinciaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectCondIvaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const documentoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleClientSelect = (clientId)=>{\n        setSelectedClientId(clientId);\n        const selectedClient = rows.find((row)=>row.id === clientId);\n        if (selectedClient) {\n            // Split the lugar field into localidad and provincia\n            const [localidad, provincia] = selectedClient.lugar.split(\" - \");\n            setFormData({\n                personRazonSocial: selectedClient.razonSocial,\n                personTipoCliente: selectedClient.tipoCliente || \"\",\n                personNombreContacto: selectedClient.nombreContacto || \"\",\n                personCargoContacto: selectedClient.cargoContacto || \"\",\n                personDomicilio: selectedClient.direccion,\n                personTelefono: selectedClient.telefono,\n                personMail: selectedClient.mail,\n                personLocalidad: localidad,\n                personProvincia: provincia,\n                personCondFrenteIva: selectedClient.condFrenteIva,\n                personDocumento: selectedClient.documento\n            });\n        }\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Pestaña 1: Propietario/Persona Física\n        propietarioNombre: \"\",\n        propietarioDocumento: \"\",\n        propietarioTelefono: \"\",\n        propietarioEmail: \"\",\n        usarComoRazonSocial: false,\n        // Pestaña 2: Empresa/Razón Social\n        empresaRazonSocial: \"\",\n        empresaTipoCliente: \"\",\n        empresaDomicilio: \"\",\n        empresaLocalidad: \"\",\n        empresaProvincia: \"\",\n        empresaCondFrenteIva: \"\",\n        // Pestaña 3: Contactos/Encargados (array dinámico)\n        contactos: [\n            {\n                id: 1,\n                nombre: \"\",\n                cargo: \"\",\n                telefono: \"\",\n                email: \"\"\n            }\n        ]\n    });\n    const provincias = [\n        \"Buenos Aires\",\n        \"Catamarca\",\n        \"Chaco\",\n        \"Chubut\",\n        \"C\\xf3rdoba\",\n        \"Corrientes\",\n        \"Entre R\\xedos\",\n        \"Formosa\",\n        \"Jujuy\",\n        \"La Pampa\",\n        \"La Rioja\",\n        \"Mendoza\",\n        \"Misiones\",\n        \"Neuqu\\xe9n\",\n        \"R\\xedo Negro\",\n        \"Salta\",\n        \"San Juan\",\n        \"San Luis\",\n        \"Santa Cruz\",\n        \"Santa Fe\",\n        \"Santiago del Estero\",\n        \"Tierra del Fuego\",\n        \"Tucum\\xe1n\"\n    ];\n    const tipoClienteOptions = [\n        // Productores\n        {\n            value: \"Productor(comercial)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n        },\n        {\n            value: \"Productor(familiar)\",\n            category: \"Productores\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n        },\n        {\n            value: \"Estancia\",\n            category: \"Productores\",\n            icon: \"\\uD83C\\uDFDE️\"\n        },\n        // Empresas y Organizaciones\n        {\n            value: \"Empresa (persona jur\\xeddica, p. ej. SA / SRL)\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFE2\"\n        },\n        {\n            value: \"Cooperativa\",\n            category: \"Empresas\",\n            icon: \"\\uD83C\\uDFD8️\"\n        },\n        {\n            value: \"Asociaci\\xf3n/Consorcio/Entidad Gremial\",\n            category: \"Empresas\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        // Servicios y Contratistas\n        {\n            value: \"Contratista(p. ej. otro que contrata equipo)\",\n            category: \"Servicios\",\n            icon: \"\\uD83D\\uDE9C\"\n        },\n        {\n            value: \"Acopio/Industria/Exportador(silos, plantas, compradoras)\",\n            category: \"Servicios\",\n            icon: \"\\uD83C\\uDFED\"\n        },\n        // Sector Público y Otros\n        {\n            value: \"Municipalidad/Estatal/Gubernamental\",\n            category: \"P\\xfablico\",\n            icon: \"�\"\n        },\n        {\n            value: \"Particular(peque\\xf1os clientes dom\\xe9sticos)\",\n            category: \"Otros\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            value: \"Otro(para casos no previstos)\",\n            category: \"Otros\",\n            icon: \"❓\"\n        },\n        {\n            value: \"No Especificado\",\n            category: \"Otros\",\n            icon: \"➖\"\n        }\n    ];\n    const condFrenteIvaOptions = [\n        \"IVA Responsable Inscripto\",\n        \"IVA Responsable no Inscripto\",\n        \"IVA no Responsable\",\n        \"IVA Sujeto Exento\",\n        \"Consumidor Final\",\n        \"Responsable Monotributo\",\n        \"Sujeto no Categorizado\",\n        \"Proveedor del Exterior\",\n        \"Cliente del Exterior\",\n        \"IVA Liberado\",\n        \"Peque\\xf1o Contribuyente Social\",\n        \"Monotributista Social\",\n        \"Peque\\xf1o Contribuyente Eventual\"\n    ];\n    const handleOpenAdd = ()=>{\n        setEstadoModal(\"add\");\n        clearFrom();\n        setOpen(true);\n    };\n    const clearFrom = ()=>{\n        setFormData({\n            personRazonSocial: \"\",\n            personTipoCliente: \"\",\n            personNombreContacto: \"\",\n            personCargoContacto: \"\",\n            personDomicilio: \"\",\n            personTelefono: \"\",\n            personMail: \"\",\n            personLocalidad: \"\",\n            personProvincia: \"\",\n            personCondFrenteIva: \"\",\n            personDocumento: \"\"\n        });\n        setError({});\n        setTabValue(0); // Resetear al primer tab\n    };\n    const handleClickClose = (_event, reason)=>{\n        if (reason && reason === \"backdropClick\") return;\n        setOpen(false);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        if (name === \"personRazonSocial\" || name === \"personLocalidad\" || name === \"personNombreContacto\" || name === \"personCargoContacto\") {\n            if (!/^[a-zA-ZÀ-ÿ\\s]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"Solo se permiten letras y espacios\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        [name]: \"\"\n                    }));\n            }\n        }\n        if (name === \"personDomicilio\") {\n            if (!/^[a-zA-ZÀ-ÿ0-9\\s.]*$/.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        personDomicilio: \"Solo se permiten letras, n\\xfameros, espacios y puntos\"\n                    }));\n                return;\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        personDomicilio: \"\"\n                    }));\n            }\n        }\n        if (name === \"personTelefono\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 10 dígitos\n            if (cleaned.length > 10) return;\n            let formatted;\n            if (cleaned.length <= 4) {\n                formatted = cleaned;\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 4), \"-\").concat(cleaned.slice(4));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{4}-\\d{6}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    personTelefono: formatted.length === 11 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 0000-000000\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        if (name === \"personMail\") {\n            // Expresión regular para validar email\n            const emailRegex = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n            // Si el campo no está vacío, validar el formato\n            if (value && !emailRegex.test(value)) {\n                setError((prevError)=>({\n                        ...prevError,\n                        personMail: \"Formato de email inv\\xe1lido. Ejemplo: <EMAIL>\"\n                    }));\n            } else {\n                setError((prevError)=>({\n                        ...prevError,\n                        personMail: \"\"\n                    }));\n            }\n        }\n        if (name === \"personDocumento\") {\n            // Eliminar todo lo que no sea número\n            const cleaned = value.replace(/\\D/g, \"\");\n            // Limitar a 11 dígitos en total\n            if (cleaned.length > 11) return;\n            let formatted;\n            if (cleaned.length <= 2) {\n                formatted = cleaned;\n            } else if (cleaned.length <= 10) {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2));\n            } else {\n                formatted = \"\".concat(cleaned.slice(0, 2), \"-\").concat(cleaned.slice(2, 10), \"-\").concat(cleaned.slice(10, 11));\n            }\n            // Validar el formato completo\n            const isValidFormat = /^\\d{2}-\\d{8}-\\d{1}$/.test(formatted);\n            setError((prevError)=>({\n                    ...prevError,\n                    personDocumento: formatted.length === 12 && !isValidFormat ? \"Formato inv\\xe1lido. Debe ser 00-00000000-0\" : \"\"\n                }));\n            setFormData((prevState)=>({\n                    ...prevState,\n                    [name]: formatted\n                }));\n            return;\n        }\n        setFormData((prevState)=>({\n                ...prevState,\n                [name]: value\n            }));\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n    };\n    const handleSearchClick = ()=>{\n        setIsSearchBarOpen(!isSearchBarOpen);\n    };\n    const columns = [\n        {\n            field: \"empresa\",\n            headerName: \"Empresa\",\n            width: 280,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                fontWeight: \"600\",\n                                color: \"#333\",\n                                fontSize: \"0.875rem\",\n                                lineHeight: 1.2\n                            },\n                            children: params.row.razonSocial\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, undefined),\n                        params.row.tipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: \"#666\",\n                                fontSize: \"0.75rem\",\n                                fontStyle: \"italic\"\n                            },\n                            children: params.row.tipoCliente\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"contacto\",\n            headerName: \"Contacto\",\n            width: 220,\n            headerClassName: \"custom-header\",\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        py: 1\n                    },\n                    children: params.row.nombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body2\",\n                                sx: {\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    fontSize: \"0.875rem\",\n                                    lineHeight: 1.2\n                                },\n                                children: params.row.nombreContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 15\n                            }, undefined),\n                            params.row.cargoContacto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                sx: {\n                                    color: \"#666\",\n                                    fontSize: \"0.75rem\",\n                                    fontStyle: \"italic\"\n                                },\n                                children: params.row.cargoContacto\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: \"#999\",\n                            fontStyle: \"italic\",\n                            fontSize: \"0.875rem\"\n                        },\n                        children: \"Sin contacto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 431,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            field: \"telefono\",\n            headerName: \"Tel\\xe9fono\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"mail\",\n            headerName: \"Email\",\n            width: 180,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"lugar\",\n            headerName: \"Ubicaci\\xf3n\",\n            width: 200,\n            headerClassName: \"custom-header\"\n        },\n        {\n            field: \"documento\",\n            headerName: \"Documento\",\n            width: 140,\n            headerClassName: \"custom-header\"\n        }\n    ];\n    /*AGREGAR AGRICULTOR/GANADERO*/ const handleAddCliente = async ()=>{\n        console.log(\"Iniciando env\\xedo...\");\n        setIsSubmitting(true);\n        const lugar = \"\".concat(formData.personLocalidad, \" - \").concat(formData.personProvincia);\n        const newPerson = {\n            razonSocial: formData.personRazonSocial,\n            tipoCliente: formData.personTipoCliente,\n            nombreContacto: formData.personNombreContacto,\n            cargoContacto: formData.personCargoContacto,\n            direccion: formData.personDomicilio,\n            telefono: formData.personTelefono,\n            mail: formData.personMail,\n            lugar: lugar,\n            provincia: formData.personProvincia,\n            condFrenteIva: formData.personCondFrenteIva,\n            documento: formData.personDocumento\n        };\n        // Llamar a la función de validación\n        const errors = formData;\n        console.log(errors);\n        if (errors) {\n            setError(errors);\n            return;\n        }\n        // Mostrar cada dato individual en la consola\n        console.log(\"Raz\\xf3n Social:\", newPerson.razonSocial);\n        console.log(\"Direcci\\xf3n:\", newPerson.direccion);\n        console.log(\"Tel\\xe9fono:\", newPerson.telefono);\n        console.log(\"Mail:\", newPerson.mail);\n        console.log(\"Lugar:\", lugar);\n        console.log(\"Condici\\xf3n Frente IVA:\", newPerson.condFrenteIva);\n        console.log(\"Documento:\", newPerson.documento);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            // Verificar si la solicitud fue exitosa\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            clearFrom();\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/ const fetchClientes = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\");\n            if (!res.ok) {\n                throw new Error(\"Error al obtener los clientes\");\n            }\n            const dataClientes = await res.json();\n            return dataClientes;\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n            // Devolver un valor predeterminado en caso de error\n            return [];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            const dataClientes = await fetchClientes();\n            setRows(dataClientes);\n        };\n        getData();\n    }, []);\n    /*BUSCAR AGRICULTOR/GANADERO*/ const handleSearhCliente = (event)=>{\n        const searchValue = event.target.value;\n        setSearchTerm(searchValue);\n        const filteredData = rows.filter((row)=>{\n            return row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) || row.direccion.toLowerCase().includes(searchValue.toLowerCase()) || row.telefono.toLowerCase().includes(searchValue.toLowerCase()) || row.mail.toLowerCase().includes(searchValue.toLowerCase()) || row.lugar.toLowerCase().includes(searchValue.toLowerCase()) || row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) || row.documento.toLowerCase().includes(searchValue.toLowerCase());\n        });\n        setFilteredRows(filteredData);\n    };\n    /*ELIMINAR AGRICULTOR/GANADERO*/ const handleDeleteCliente = async (id)=>{\n        console.log(\"Cliente a eliminar:\", id);\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente eliminado exitosamente.\");\n                // Actualizar el estado de las filas después de eliminar un cliente\n                const dataClientes = await fetchClientes();\n                setRows(dataClientes);\n            } else {\n                console.error(\"Error al eliminar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n    };\n    /*CLICK BOTON MODIFICAR(LAPIZ)*/ const handleEdit = async (id)=>{\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor/\".concat(id), {\n                method: \"GET\"\n            });\n            if (res.ok) {\n                console.log(\"Cliente obtenido exitosamente.\");\n                const agricultor = await res.json();\n                setFormData({\n                    personRazonSocial: agricultor.razonSocial,\n                    personTipoCliente: agricultor.tipoCliente || \"\",\n                    personNombreContacto: agricultor.nombreContacto || \"\",\n                    personCargoContacto: agricultor.cargoContacto || \"\",\n                    personDomicilio: agricultor.direccion,\n                    personTelefono: agricultor.telefono,\n                    personMail: agricultor.mail,\n                    personLocalidad: agricultor.lugar.split(\" - \")[0].trim(),\n                    personProvincia: agricultor.lugar.split(\" - \")[1].trim(),\n                    personCondFrenteIva: agricultor.condFrenteIva,\n                    personDocumento: agricultor.documento\n                });\n            } else {\n                console.error(\"Error al modificar el cliente:\", id);\n            }\n        } catch (error) {\n            console.error(\"Error en la solicitud de eliminaci\\xf3n:\", error);\n        }\n        setEstadoModal(\"update\");\n        setOpen(true);\n    };\n    /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/ const handleUpdateCliente = async ()=>{\n        if (!selectedRow) return;\n        const lugar = \"\".concat(formData.personLocalidad, \" - \").concat(formData.personProvincia);\n        const newPerson = {\n            id: selectedRow.id,\n            razonSocial: formData.personRazonSocial,\n            tipoCliente: formData.personTipoCliente,\n            nombreContacto: formData.personNombreContacto,\n            cargoContacto: formData.personCargoContacto,\n            direccion: formData.personDomicilio,\n            telefono: formData.personTelefono,\n            mail: formData.personMail,\n            lugar: lugar,\n            provincia: formData.personProvincia,\n            condFrenteIva: formData.personCondFrenteIva,\n            documento: formData.personDocumento\n        };\n        try {\n            const res = await fetch(\"http://localhost:8080/api/agricultor\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newPerson)\n            });\n            if (!res.ok) {\n                throw new Error(\"Error al guardar el cliente\");\n            }\n            // Update rows with proper typing\n            const updatedRows = rows.map((row)=>{\n                if (row.id === newPerson.id) {\n                    return newPerson;\n                }\n                return row;\n            });\n            setRows(updatedRows);\n            clearFrom();\n            setOpen(false);\n        } catch (error) {\n            console.error(\"Error en la solicitud:\", error);\n        }\n    };\n    const handleLocalidadKeyDown = (event)=>{\n        if (event.key === \"Enter\" && selectProvinciaRef.current) {\n            selectProvinciaRef.current.focus();\n        }\n    };\n    const handleProvinciaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (selectCondIvaRef.current) {\n                selectCondIvaRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleCondIvaChange = (event)=>{\n        handleInputChange(event);\n        setTimeout(()=>{\n            if (documentoRef.current) {\n                documentoRef.current.focus();\n            }\n        }, 0);\n    };\n    const handleSelectAgricultor = (id)=>{\n        const selectedAgricultor = rows.find((row)=>row.id === id);\n        if (selectedAgricultor) {\n            const agricultor = {\n                id: selectedAgricultor.id,\n                razonSocial: selectedAgricultor.razonSocial\n            };\n            // Guardar el agricultor seleccionado\n            localStorage.setItem(\"selectedAgricultor\", JSON.stringify(agricultor));\n            // Redirigir de vuelta a la página de establecimiento\n            window.location.href = \"/establecimiento\";\n        }\n    };\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSearchChange = (event)=>{\n        setSearchTerm(event.target.value);\n        setPage(0);\n    };\n    const labelStyles = {\n        fontWeight: 600,\n        color: \"#333\",\n        marginBottom: \"8px\",\n        display: \"block\",\n        fontFamily: \"Lexend, sans-serif\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    mb: 3,\n                    mt: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"div\",\n                                sx: {\n                                    fontWeight: \"bold\",\n                                    fontFamily: \"Lexend, sans-serif\"\n                                },\n                                children: \"Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle1\",\n                                sx: {\n                                    color: \"text.secondary\",\n                                    mt: 1,\n                                    fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                                },\n                                children: \"Gestione la informaci\\xf3n de sus Agricultores\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleOpenAdd,\n                        sx: {\n                            bgcolor: \"#2E7D32\",\n                            color: \"#ffffff\",\n                            \"&:hover\": {\n                                bgcolor: \"#0D9A0A\"\n                            },\n                            height: \"fit-content\",\n                            alignSelf: \"center\",\n                            fontFamily: \"\".concat((next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily)\n                        },\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddOutlined__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 812,\n                            columnNumber: 22\n                        }, void 0),\n                        children: \"Nuevo Agricultor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 801,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 771,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 2,\n                sx: {\n                    p: 2,\n                    mb: 3,\n                    borderRadius: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        variant: \"outlined\",\n                        placeholder: \"Buscar...\",\n                        value: searchTerm,\n                        onChange: handleSearhCliente,\n                        InputProps: {\n                            startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                position: \"start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onClick: handleSearchClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 828,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 15\n                            }, void 0)\n                        },\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 819,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        columns: columns,\n                        rows: filteredRows.length > 0 ? filteredRows : rows,\n                        option: true,\n                        optionDeleteFunction: handleDeleteCliente,\n                        optionUpdateFunction: handleEdit,\n                        setSelectedRow: (row)=>setSelectedRow(row),\n                        selectedRow: selectedRow,\n                        optionSelect: handleSelectAgricultor\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                        lineNumber: 836,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 818,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                open: open,\n                onClose: handleClickClose,\n                maxWidth: \"lg\",\n                fullWidth: true,\n                sx: {\n                    \"& .MuiDialog-paper\": {\n                        width: \"1100px\",\n                        maxWidth: \"95vw\",\n                        minHeight: \"600px\"\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        p: 4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogTitle_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Registrar nuevo agricultor/ganadero\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContentText_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        p: 0,\n                                        mt: 1,\n                                        fontFamily: \"Inter, sans-serif\",\n                                        color: \"#666\"\n                                    },\n                                    children: \"Complete la informaci\\xf3n del nuevo agricultor/ganadero a registrar.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            \"aria-label\": \"close\",\n                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                            sx: {\n                                position: \"absolute\",\n                                right: 8,\n                                top: 8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                lineNumber: 891,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            component: \"form\",\n                            onSubmit: handleSubmit,\n                            className: (next_font_google_target_css_path_src_app_paginas_agricultor_page_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DialogContent_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        p: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            sx: {\n                                                borderBottom: 1,\n                                                borderColor: \"divider\",\n                                                mb: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                value: tabValue,\n                                                onChange: (event, newValue)=>setTabValue(newValue),\n                                                sx: {\n                                                    \"& .MuiTab-root\": {\n                                                        fontFamily: \"Lexend, sans-serif\",\n                                                        fontWeight: 600,\n                                                        textTransform: \"none\",\n                                                        fontSize: \"1rem\"\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        label: \"\\uD83D\\uDC64 Propietario/Due\\xf1o\",\n                                                        sx: {\n                                                            minWidth: 200\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        label: \"\\uD83C\\uDFE2 Empresa y Contacto\",\n                                                        sx: {\n                                                            minWidth: 200\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 900,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            container: true,\n                                            spacing: 3,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: {\n                                                    xs: 12\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    container: true,\n                                                    spacing: 3,\n                                                    children: [\n                                                        tabValue === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"h6\",\n                                                                        sx: {\n                                                                            fontFamily: \"Lexend, sans-serif\",\n                                                                            fontWeight: \"600\",\n                                                                            color: \"#333\",\n                                                                            mb: 3,\n                                                                            mt: 1,\n                                                                            fontSize: \"1.2rem\"\n                                                                        },\n                                                                        children: \"Informaci\\xf3n del Propietario/Due\\xf1o\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 924,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre Completo del Propietario\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Juan Carlos P\\xe9rez\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"nombrePropietario\",\n                                                                            name: \"personRazonSocial\",\n                                                                            type: \"text\",\n                                                                            error: Boolean(error.personRazonSocial),\n                                                                            helperText: error.personRazonSocial,\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personRazonSocial,\n                                                                            disabled: estadoModal === \"update\",\n                                                                            fullWidth: true,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personRazonSocial && (error.personRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 970,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 972,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 967,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                style: {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            },\n                                                                            sx: {\n                                                                                \"& .MuiInputBase-input\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                },\n                                                                                \"& .MuiFormHelperText-root\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 945,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Documento (DNI/CUIT)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 991,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 00-00000000-0\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"documentoPropietario\",\n                                                                            name: \"personDocumento\",\n                                                                            type: \"text\",\n                                                                            error: Boolean(error.personDocumento),\n                                                                            helperText: error.personDocumento,\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personDocumento,\n                                                                            fullWidth: true,\n                                                                            inputRef: documentoRef,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personDocumento && (error.personDocumento ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1019,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1021,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1016,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 994,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tel\\xe9fono Personal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1031,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: 0000-000000\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"telefonoPropietario\",\n                                                                            name: \"personTelefono\",\n                                                                            type: \"text\",\n                                                                            error: Boolean(error.personTelefono),\n                                                                            helperText: error.personTelefono,\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personTelefono,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personTelefono && (error.personTelefono ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1058,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1060,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1055,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1034,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1030,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Email Personal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1070,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: <EMAIL>\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"emailPropietario\",\n                                                                            name: \"personMail\",\n                                                                            type: \"email\",\n                                                                            required: true,\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error.personMail),\n                                                                            helperText: error.personMail,\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personMail,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personMail && (error.personMail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1098,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1100,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1095,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1073,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1069,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true),\n                                                        tabValue === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"h6\",\n                                                                        sx: {\n                                                                            fontFamily: \"Lexend, sans-serif\",\n                                                                            fontWeight: \"600\",\n                                                                            color: \"#333\",\n                                                                            mb: 3,\n                                                                            mt: 1,\n                                                                            fontSize: \"1.2rem\"\n                                                                        },\n                                                                        children: \"Informaci\\xf3n de la Empresa\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1114,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1113,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Raz\\xf3n Social de la Empresa\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1131,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Estancia La Esperanza S.A.\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"razonSocialEmpresa\",\n                                                                            name: \"personRazonSocial\",\n                                                                            type: \"text\",\n                                                                            error: Boolean(error.personRazonSocial),\n                                                                            helperText: \"Este campo se completa autom\\xe1ticamente con el nombre del propietario, pero puede editarse\",\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personRazonSocial,\n                                                                            fullWidth: true,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personRazonSocial && (error.personRazonSocial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1158,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1160,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1155,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                style: {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            },\n                                                                            sx: {\n                                                                                \"& .MuiInputBase-input\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                },\n                                                                                \"& .MuiFormHelperText-root\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1134,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1130,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Tipo de Cliente\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1179,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error.personTipoCliente),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    id: \"tipoCliente\",\n                                                                                    name: \"personTipoCliente\",\n                                                                                    value: formData.personTipoCliente,\n                                                                                    onChange: (e)=>handleInputChange(e),\n                                                                                    displayEmpty: true,\n                                                                                    MenuProps: {\n                                                                                        PaperProps: {\n                                                                                            sx: {\n                                                                                                maxHeight: 400,\n                                                                                                width: \"auto\",\n                                                                                                minWidth: \"300px\",\n                                                                                                \"& .MuiMenuItem-root\": {\n                                                                                                    fontFamily: \"Lexend, sans-serif\",\n                                                                                                    fontSize: \"0.875rem\",\n                                                                                                    fontWeight: 600,\n                                                                                                    lineHeight: 1.5,\n                                                                                                    padding: \"12px 16px\",\n                                                                                                    whiteSpace: \"normal\",\n                                                                                                    wordWrap: \"break-word\",\n                                                                                                    minHeight: \"auto\",\n                                                                                                    color: \"#333\",\n                                                                                                    borderBottom: \"1px solid #f0f0f0\",\n                                                                                                    \"&:last-child\": {\n                                                                                                        borderBottom: \"none\"\n                                                                                                    }\n                                                                                                }\n                                                                                            }\n                                                                                        }\n                                                                                    },\n                                                                                    sx: {\n                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                        height: \"56px\",\n                                                                                        \"& .MuiSelect-select\": {\n                                                                                            fontFamily: \"Lexend, sans-serif\",\n                                                                                            fontSize: \"0.875rem\",\n                                                                                            fontWeight: 600,\n                                                                                            lineHeight: 1.4,\n                                                                                            padding: \"16.5px 14px\",\n                                                                                            color: \"#333\",\n                                                                                            height: \"1.4375em\",\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\"\n                                                                                        },\n                                                                                        \"& .MuiOutlinedInput-notchedOutline\": {\n                                                                                            borderColor: \"rgba(0, 0, 0, 0.23)\"\n                                                                                        },\n                                                                                        \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                                                                                            borderColor: \"rgba(0, 0, 0, 0.87)\"\n                                                                                        },\n                                                                                        \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                                                                                            borderColor: \"#1976d2\",\n                                                                                            borderWidth: \"2px\"\n                                                                                        }\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            value: \"\",\n                                                                                            disabled: true,\n                                                                                            sx: {\n                                                                                                fontFamily: \"Lexend, sans-serif\",\n                                                                                                fontStyle: \"italic\",\n                                                                                                color: \"#999\",\n                                                                                                fontSize: \"0.875rem\",\n                                                                                                fontWeight: 600,\n                                                                                                backgroundColor: \"#fafafa\"\n                                                                                            },\n                                                                                            children: \"Seleccione un tipo de cliente\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                            lineNumber: 1247,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        tipoClienteOptions.map((tipo, index)=>{\n                                                                                            const isFirstInCategory = index === 0 || tipoClienteOptions[index - 1].category !== tipo.category;\n                                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                value: tipo.value,\n                                                                                                sx: {\n                                                                                                    fontFamily: \"Lexend, sans-serif\",\n                                                                                                    fontSize: \"0.875rem\",\n                                                                                                    fontWeight: 600,\n                                                                                                    lineHeight: 1.5,\n                                                                                                    padding: \"12px 16px\",\n                                                                                                    whiteSpace: \"normal\",\n                                                                                                    wordWrap: \"break-word\",\n                                                                                                    minHeight: \"auto\",\n                                                                                                    display: \"flex\",\n                                                                                                    alignItems: \"center\",\n                                                                                                    gap: \"8px\",\n                                                                                                    color: \"#333\",\n                                                                                                    borderTop: isFirstInCategory && index > 0 ? \"1px solid #e0e0e0\" : \"none\",\n                                                                                                    marginTop: isFirstInCategory && index > 0 ? \"4px\" : \"0\",\n                                                                                                    paddingTop: isFirstInCategory && index > 0 ? \"16px\" : \"12px\",\n                                                                                                    \"&:hover\": {\n                                                                                                        backgroundColor: \"#f8f9fa\",\n                                                                                                        borderLeft: \"3px solid #2196f3\"\n                                                                                                    },\n                                                                                                    \"&.Mui-selected\": {\n                                                                                                        backgroundColor: \"#e3f2fd\",\n                                                                                                        borderLeft: \"3px solid #1976d2\",\n                                                                                                        fontWeight: 600,\n                                                                                                        \"&:hover\": {\n                                                                                                            backgroundColor: \"#bbdefb\"\n                                                                                                        }\n                                                                                                    }\n                                                                                                },\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        style: {\n                                                                                                            fontSize: \"1rem\",\n                                                                                                            marginRight: \"8px\"\n                                                                                                        },\n                                                                                                        children: tipo.icon\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                        lineNumber: 1310,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                                        sx: {\n                                                                                                            display: \"flex\",\n                                                                                                            flexDirection: \"column\",\n                                                                                                            flex: 1\n                                                                                                        },\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                style: {\n                                                                                                                    fontFamily: \"Lexend, sans-serif\",\n                                                                                                                    fontWeight: 600,\n                                                                                                                    color: \"#333\"\n                                                                                                                },\n                                                                                                                children: tipo.value\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                                lineNumber: 1325,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                style: {\n                                                                                                                    fontFamily: \"Lexend, sans-serif\",\n                                                                                                                    fontSize: \"0.75rem\",\n                                                                                                                    color: \"#666\",\n                                                                                                                    fontStyle: \"italic\",\n                                                                                                                    fontWeight: 500\n                                                                                                                },\n                                                                                                                children: tipo.category\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                                lineNumber: 1334,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                        lineNumber: 1318,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, \"\".concat(tipo.value, \"-\").concat(index), true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1268,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined);\n                                                                                        })\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1186,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                error.personTipoCliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    sx: {\n                                                                                        fontFamily: \"Inter, sans-serif\"\n                                                                                    },\n                                                                                    children: error.personTipoCliente\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1351,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1182,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1178,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Direcci\\xf3n de la Empresa\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1362,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Cordoba 123\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"direccionEmpresa\",\n                                                                            name: \"personDomicilio\",\n                                                                            type: \"text\",\n                                                                            error: Boolean(error.personDomicilio),\n                                                                            helperText: error.personDomicilio,\n                                                                            fullWidth: true,\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personDomicilio,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personDomicilio && (error.personDomicilio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1389,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1391,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1386,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                style: {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            },\n                                                                            sx: {\n                                                                                \"& .MuiInputBase-input\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                },\n                                                                                \"& .MuiFormHelperText-root\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1365,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1361,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Localidad\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1410,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Mercedes\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"localidadEmpresa\",\n                                                                            name: \"personLocalidad\",\n                                                                            type: \"text\",\n                                                                            error: Boolean(error.personLocalidad),\n                                                                            helperText: error.personLocalidad,\n                                                                            fullWidth: true,\n                                                                            required: true,\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personLocalidad,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personLocalidad && (error.personLocalidad ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1438,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1440,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1435,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1413,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1409,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Provincia\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1449,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error.personProvincia),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    name: \"personProvincia\",\n                                                                                    labelId: \"demo-simple-select-label\",\n                                                                                    fullWidth: true,\n                                                                                    value: formData.personProvincia,\n                                                                                    onChange: (event)=>{\n                                                                                        const syntheticEvent = {\n                                                                                            target: {\n                                                                                                name: \"personProvincia\",\n                                                                                                value: event.target.value\n                                                                                            }\n                                                                                        };\n                                                                                        handleProvinciaChange(syntheticEvent);\n                                                                                    },\n                                                                                    required: true,\n                                                                                    inputRef: selectProvinciaRef,\n                                                                                    displayEmpty: true,\n                                                                                    renderValue: (selected)=>{\n                                                                                        if (!selected) {\n                                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                style: {\n                                                                                                    color: \"#999\"\n                                                                                                },\n                                                                                                children: \"Seleccione una provincia\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1476,\n                                                                                                columnNumber: 37\n                                                                                            }, void 0);\n                                                                                        }\n                                                                                        return selected;\n                                                                                    },\n                                                                                    sx: {\n                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                        height: \"56px\",\n                                                                                        \"& .MuiSelect-select\": {\n                                                                                            fontFamily: \"Lexend, sans-serif\",\n                                                                                            fontSize: \"0.875rem\",\n                                                                                            fontWeight: 600,\n                                                                                            lineHeight: 1.4,\n                                                                                            padding: \"16.5px 14px\",\n                                                                                            color: \"#333\",\n                                                                                            height: \"1.4375em\",\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\"\n                                                                                        }\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            value: \"\",\n                                                                                            disabled: true,\n                                                                                            children: \"Seleccione una provincia\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                            lineNumber: 1499,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        provincias.map((provincia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                value: provincia,\n                                                                                                children: provincia\n                                                                                            }, provincia, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1503,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined))\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1456,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                error.personProvincia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    children: error.personProvincia\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1509,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1452,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1448,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"h6\",\n                                                                        sx: {\n                                                                            fontFamily: \"Lexend, sans-serif\",\n                                                                            fontWeight: \"600\",\n                                                                            color: \"#333\",\n                                                                            mb: 3,\n                                                                            mt: 3,\n                                                                            fontSize: \"1.2rem\"\n                                                                        },\n                                                                        children: \"Contacto/Encargado\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                        lineNumber: 1518,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1517,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Nombre del Encargado\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1535,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Juan P\\xe9rez\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"nombreContacto\",\n                                                                            name: \"personNombreContacto\",\n                                                                            type: \"text\",\n                                                                            error: Boolean(error.personNombreContacto),\n                                                                            helperText: error.personNombreContacto,\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personNombreContacto,\n                                                                            fullWidth: true,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personNombreContacto && (error.personNombreContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1562,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1564,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1559,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                style: {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            },\n                                                                            sx: {\n                                                                                \"& .MuiInputBase-input\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                },\n                                                                                \"& .MuiFormHelperText-root\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1538,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1534,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12,\n                                                                        sm: 6\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Cargo (Opcional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1582,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TextField_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            placeholder: \"Ej: Encargado, Propietario, Administrador\",\n                                                                            variant: \"outlined\",\n                                                                            id: \"cargoContacto\",\n                                                                            name: \"personCargoContacto\",\n                                                                            type: \"text\",\n                                                                            error: Boolean(error.personCargoContacto),\n                                                                            helperText: error.personCargoContacto,\n                                                                            onChange: (e)=>handleInputChange(e),\n                                                                            value: formData.personCargoContacto,\n                                                                            fullWidth: true,\n                                                                            InputProps: {\n                                                                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    position: \"end\",\n                                                                                    children: formData.personCargoContacto && (error.personCargoContacto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        color: \"error\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1609,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        color: \"success\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                        lineNumber: 1611,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1606,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                style: {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            },\n                                                                            sx: {\n                                                                                \"& .MuiInputBase-input\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                },\n                                                                                \"& .MuiFormHelperText-root\": {\n                                                                                    fontFamily: \"Inter, sans-serif\"\n                                                                                }\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1585,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1581,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: {\n                                                                        xs: 12\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: labelStyles,\n                                                                            children: \"Condici\\xf3n frente al IVA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1630,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormControl_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            fullWidth: true,\n                                                                            error: Boolean(error.personCondFrenteIva),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    name: \"personCondFrenteIva\",\n                                                                                    fullWidth: true,\n                                                                                    value: formData.personCondFrenteIva,\n                                                                                    onChange: (event)=>{\n                                                                                        const syntheticEvent = {\n                                                                                            target: {\n                                                                                                name: \"personCondFrenteIva\",\n                                                                                                value: event.target.value\n                                                                                            }\n                                                                                        };\n                                                                                        handleCondIvaChange(syntheticEvent);\n                                                                                    },\n                                                                                    required: true,\n                                                                                    inputRef: selectCondIvaRef,\n                                                                                    displayEmpty: true,\n                                                                                    renderValue: (selected)=>{\n                                                                                        if (!selected) {\n                                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                style: {\n                                                                                                    color: \"#999\"\n                                                                                                },\n                                                                                                children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1656,\n                                                                                                columnNumber: 37\n                                                                                            }, void 0);\n                                                                                        }\n                                                                                        return selected;\n                                                                                    },\n                                                                                    sx: {\n                                                                                        fontFamily: \"Lexend, sans-serif\",\n                                                                                        height: \"56px\",\n                                                                                        \"& .MuiSelect-select\": {\n                                                                                            fontFamily: \"Lexend, sans-serif\",\n                                                                                            fontSize: \"0.875rem\",\n                                                                                            fontWeight: 600,\n                                                                                            lineHeight: 1.4,\n                                                                                            padding: \"16.5px 14px\",\n                                                                                            color: \"#333\",\n                                                                                            height: \"1.4375em\",\n                                                                                            display: \"flex\",\n                                                                                            alignItems: \"center\"\n                                                                                        }\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            value: \"\",\n                                                                                            disabled: true,\n                                                                                            children: \"Seleccione condici\\xf3n frente al IVA\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                            lineNumber: 1679,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        condFrenteIvaOptions.map((condicion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                value: condicion,\n                                                                                                children: condicion\n                                                                                            }, condicion, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                                lineNumber: 1683,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined))\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1637,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                error.personCondFrenteIva && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FormHelperText_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    children: error.personCondFrenteIva\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                                    lineNumber: 1689,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                            lineNumber: 1633,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                                    lineNumber: 1629,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                    lineNumber: 920,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    sx: {\n                                        mt: 4,\n                                        display: \"flex\",\n                                        justifyContent: \"flex-end\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            type: \"button\",\n                                            variant: \"outlined\",\n                                            onClick: (event)=>handleClickClose(event, \"closeButtonClick\"),\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1711,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Grid_InputAdornment_Paper_Tab_Tabs_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            type: \"submit\",\n                                            variant: \"contained\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AddCircle__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                                lineNumber: 1721,\n                                                columnNumber: 28\n                                            }, void 0),\n                                            onClick: estadoModal === \"add\" ? handleAddCliente : handleUpdateCliente,\n                                            sx: {\n                                                bgcolor: \"#2E7D32\",\n                                                color: \"#ffffff\",\n                                                \"&:hover\": {\n                                                    bgcolor: \"#1B5E20\"\n                                                },\n                                                textTransform: \"none\",\n                                                \"& .MuiSvgIcon-root\": {\n                                                    color: \"#ffffff\"\n                                                }\n                                            },\n                                            children: estadoModal === \"add\" ? \"Registrar\" : \"Guardar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                            lineNumber: 1718,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                                    lineNumber: 1703,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                            lineNumber: 893,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                    lineNumber: 861,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\agricultor\\\\page.tsx\",\n                lineNumber: 848,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AgricultorGanadero, \"/+696nRnzpsigbBxgjKR25/yc5U=\");\n_c = AgricultorGanadero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AgricultorGanadero);\nvar _c;\n$RefreshReg$(_c, \"AgricultorGanadero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/agricultor/page.tsx\n"));

/***/ })

});